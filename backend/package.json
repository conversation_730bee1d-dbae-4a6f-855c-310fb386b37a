{"name": "function-calling-backend", "version": "1.0.0", "description": "Backend API for Function Calling Tools", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf dist"}, "keywords": ["openai", "function-calling", "tools", "api"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "openai": "^5.9.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.13", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}