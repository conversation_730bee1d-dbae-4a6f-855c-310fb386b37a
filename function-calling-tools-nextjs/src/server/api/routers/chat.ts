import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '../trpc';
import { OpenAIService } from '../../services/openai';
import { ChatMessage, ToolCall, ToolCallResult } from '../../../types';
import { weatherTool, translationTool, summaryTool } from '../../tools';

// 内存中的对话存储（实际项目中应该使用数据库）
const conversations = new Map<string, ChatMessage[]>();

const openaiService = new OpenAIService();

// 工具执行器映射
const toolExecutors = {
  weather_query: weatherTool.execute,
  translate_text: translationTool.execute,
  summarize_text: summaryTool.execute
};

async function executeToolCall(toolCall: ToolCall): Promise<ToolCallResult> {
  try {
    const { function: func } = toolCall;
    const executor = toolExecutors[func.name as keyof typeof toolExecutors];
    
    if (!executor) {
      throw new Error(`Unknown tool: ${func.name}`);
    }

    // 解析参数
    let args: any;
    try {
      args = JSON.parse(func.arguments);
    } catch (error) {
      throw new Error('Invalid JSON in function arguments');
    }

    console.log(`🔧 Executing tool: ${func.name} with args:`, args);

    // 执行工具函数
    const result = await executor(args);

    // 构建工具调用结果
    const toolResult: ToolCallResult = {
      tool_call_id: toolCall.id,
      content: JSON.stringify(result, null, 2)
    };

    console.log(`✅ Tool execution completed: ${func.name}`);

    return toolResult;
  } catch (error: any) {
    console.error(`❌ Tool execution failed: ${toolCall.function.name}`, error);
    
    // 返回错误结果
    const errorResult: ToolCallResult = {
      tool_call_id: toolCall.id,
      content: JSON.stringify({
        error: true,
        message: error.message,
        code: 'TOOL_EXECUTION_FAILED'
      }, null, 2)
    };

    return errorResult;
  }
}

async function executeToolCalls(toolCalls: ToolCall[]): Promise<ToolCallResult[]> {
  const results: ToolCallResult[] = [];
  
  for (const toolCall of toolCalls) {
    const result = await executeToolCall(toolCall);
    results.push(result);
  }
  
  return results;
}

function generateId(): string {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export const chatRouter = createTRPCRouter({
  // 发送聊天消息
  sendMessage: publicProcedure
    .input(z.object({
      message: z.string(),
      conversation_id: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      const { message, conversation_id } = input;

      // 生成或获取对话ID
      const conversationId = conversation_id || `conv_${generateId()}`;
      
      // 获取对话历史
      const conversationHistory = conversations.get(conversationId) || [];

      // 创建用户消息
      const userMessage: ChatMessage = {
        id: `msg_${generateId()}`,
        role: 'user',
        content: message,
        timestamp: Date.now()
      };

      // 添加系统消息（如果是新对话）
      if (conversationHistory.length === 0) {
        const systemMessage: ChatMessage = {
          id: `sys_${Date.now()}`,
          role: 'system',
          content: `你是一个智能助手，可以使用多种工具来帮助用户。你有以下工具可用：
1. 天气查询 - 获取城市天气信息
2. 翻译工具 - 多语言文本翻译
3. 文本摘要 - 生成文本摘要

请根据用户的需求选择合适的工具，并提供有用的回答。`,
          timestamp: Date.now()
        };
        conversationHistory.push(systemMessage);
      }

      // 添加用户消息到历史
      conversationHistory.push(userMessage);

      // 调用OpenAI获取响应
      const { message: assistantMessage, toolCalls } = await openaiService.createChatCompletion(
        conversationHistory,
        true
      );

      // 添加助手消息到历史
      conversationHistory.push(assistantMessage);

      let finalResponse = assistantMessage;

      // 如果有工具调用，执行工具并获取最终响应
      if (toolCalls && toolCalls.length > 0) {
        console.log(`🔧 Executing ${toolCalls.length} tool call(s)`);

        // 执行工具调用
        const toolResults = await executeToolCalls(toolCalls);

        // 添加工具结果到对话历史
        for (const result of toolResults) {
          const toolMessage: ChatMessage = {
            id: `tool_${generateId()}`,
            role: 'tool',
            content: result.content,
            tool_call_id: result.tool_call_id,
            timestamp: Date.now()
          };
          conversationHistory.push(toolMessage);
        }

        // 获取基于工具结果的最终响应
        const { message: finalAssistantMessage } = await openaiService.createChatCompletion(
          conversationHistory,
          false // 不再使用工具
        );

        conversationHistory.push(finalAssistantMessage);
        finalResponse = finalAssistantMessage;
      }

      // 保存对话历史
      conversations.set(conversationId, conversationHistory);

      console.log(`💬 Chat response sent for conversation: ${conversationId}`);

      return {
        message: finalResponse,
        conversation_id: conversationId,
        tool_calls: toolCalls
      };
    }),

  // 获取对话历史
  getConversation: publicProcedure
    .input(z.object({
      conversation_id: z.string(),
    }))
    .query(async ({ input }) => {
      const { conversation_id } = input;
      
      const conversationHistory = conversations.get(conversation_id);
      
      if (!conversationHistory) {
        throw new Error('Conversation not found');
      }

      return {
        conversation_id,
        messages: conversationHistory,
        message_count: conversationHistory.length
      };
    }),

  // 获取所有对话列表
  getConversations: publicProcedure
    .query(async () => {
      const conversationList = Array.from(conversations.entries()).map(([id, messages]) => ({
        conversation_id: id,
        message_count: messages.length,
        last_message: messages[messages.length - 1],
        created_at: messages[0]?.timestamp,
        updated_at: messages[messages.length - 1]?.timestamp
      }));

      return {
        conversations: conversationList,
        total_count: conversationList.length
      };
    }),

  // 删除对话
  deleteConversation: publicProcedure
    .input(z.object({
      conversation_id: z.string(),
    }))
    .mutation(async ({ input }) => {
      const { conversation_id } = input;
      
      const existed = conversations.has(conversation_id);
      conversations.delete(conversation_id);

      return {
        success: true,
        message: existed ? 'Conversation deleted' : 'Conversation not found',
        conversation_id
      };
    }),
});
