import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '../trpc';
import { weatherTool, translationTool, summaryTool, getAvailableTools } from '../../tools';

export const toolsRouter = createTRPCRouter({
  // 获取所有可用工具
  getAll: publicProcedure
    .query(async () => {
      return {
        tools: getAvailableTools(),
        total_count: getAvailableTools().length
      };
    }),

  // 天气查询
  getWeather: publicProcedure
    .input(z.object({
      city: z.string(),
      country: z.string().optional(),
    }))
    .query(async ({ input }) => {
      return await weatherTool.execute(input);
    }),

  // 翻译
  translate: publicProcedure
    .input(z.object({
      text: z.string(),
      target_language: z.string(),
      source_language: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      return await translationTool.execute({
        text: input.text,
        target_language: input.target_language,
        source_language: input.source_language
      });
    }),

  // 文本摘要
  summarize: publicProcedure
    .input(z.object({
      text: z.string(),
      max_length: z.number().optional(),
      style: z.enum(['concise', 'detailed', 'bullet_points']).optional(),
    }))
    .mutation(async ({ input }) => {
      return await summaryTool.execute(input);
    }),
});
