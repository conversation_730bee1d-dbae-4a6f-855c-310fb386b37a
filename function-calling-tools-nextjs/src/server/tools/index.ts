import { executeWeatherQuery } from './weather';
import { executeTranslation } from './translation';
import { executeSummary } from './summary';

export const weatherTool = {
  name: 'weather_query',
  description: '获取指定城市的天气信息',
  execute: executeWeatherQuery
};

export const translationTool = {
  name: 'translate_text',
  description: '文本翻译服务',
  execute: executeTranslation
};

export const summaryTool = {
  name: 'summarize_text',
  description: '文本摘要生成',
  execute: executeSummary
};

// 所有工具的集合
export const allTools = [
  weatherTool,
  translationTool,
  summaryTool
];

// 获取所有可用工具的信息
export function getAvailableTools() {
  return allTools.map(tool => ({
    name: tool.name,
    description: tool.description,
    available: true
  }));
}
