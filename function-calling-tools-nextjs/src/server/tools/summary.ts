interface SummaryArgs {
  text: string;
  max_length?: number;
  style?: 'concise' | 'detailed' | 'bullet_points';
}

export interface SummaryResult {
  original_text: string;
  summary: string;
  word_count: {
    original: number;
    summary: number;
  };
}

export async function executeSummary(args: SummaryArgs): Promise<SummaryResult> {
  try {
    const { text, max_length = 100, style = 'concise' } = args;
    
    // 计算原文字数
    const originalWordCount = countWords(text);
    
    // 生成摘要
    let summary = '';
    
    if (style === 'bullet_points') {
      summary = generateBulletPointSummary(text, max_length);
    } else if (style === 'detailed') {
      summary = generateDetailedSummary(text, max_length);
    } else {
      summary = generateConciseSummary(text, max_length);
    }
    
    // 计算摘要字数
    const summaryWordCount = countWords(summary);
    
    const result: SummaryResult = {
      original_text: text,
      summary,
      word_count: {
        original: originalWordCount,
        summary: summaryWordCount
      }
    };
    
    console.log(`📝 Summary generated: ${originalWordCount} words -> ${summaryWordCount} words`);
    
    return result;
  } catch (error: any) {
    console.error('Summary generation error:', error);
    throw new Error(`Failed to generate summary: ${error.message}`);
  }
}

function countWords(text: string): number {
  // 简单的字数统计
  return text.split(/\s+/).filter(word => word.length > 0).length;
}

function generateConciseSummary(text: string, maxLength: number): string {
  // 简单的摘要生成 - 实际项目中应使用NLP或AI模型
  
  // 提取前几句话
  const sentences = text.split(/[.!?。！？]+/).filter(s => s.trim().length > 0);
  
  let summary = '';
  let currentLength = 0;
  
  for (const sentence of sentences) {
    const sentenceWords = countWords(sentence);
    
    if (currentLength + sentenceWords <= maxLength) {
      summary += sentence.trim() + '. ';
      currentLength += sentenceWords;
    } else {
      break;
    }
  }
  
  return summary.trim();
}

function generateDetailedSummary(text: string, maxLength: number): string {
  // 详细摘要 - 实际项目中应使用NLP或AI模型
  const conciseSummary = generateConciseSummary(text, maxLength * 0.8);
  
  // 添加一些额外的详细信息
  const detailPrefix = '本文详细讨论了';
  const detailSuffix = '等内容。';
  
  // 提取一些关键词
  const keywords = extractKeywords(text, 3);
  
  return conciseSummary + '\n\n' + detailPrefix + keywords.join('、') + detailSuffix;
}

function generateBulletPointSummary(text: string, maxLength: number): string {
  // 要点摘要 - 实际项目中应使用NLP或AI模型
  const sentences = text.split(/[.!?。！？]+/).filter(s => s.trim().length > 0);
  
  // 选择前几句作为要点
  const bulletPoints = sentences
    .slice(0, Math.min(5, sentences.length))
    .map(s => '• ' + s.trim());
  
  return bulletPoints.join('\n');
}

function extractKeywords(text: string, count: number): string[] {
  // 简单的关键词提取 - 实际项目中应使用NLP或AI模型
  const words = text.split(/\s+/).filter(word => word.length > 3);
  
  // 去重并选择前几个较长的词
  const uniqueWords = Array.from(new Set(words))
    .sort((a, b) => b.length - a.length)
    .slice(0, count);
  
  return uniqueWords;
}
