interface TranslationArgs {
  text: string;
  target_language: string;
  source_language?: string;
}

export interface TranslationResult {
  original_text: string;
  translated_text: string;
  source_language: string;
  target_language: string;
}

// 模拟翻译数据
const mockTranslations: Record<string, Record<string, Record<string, string>>> = {
  'zh': {
    'en': {
      '你好': 'Hello',
      '世界': 'World',
      '你好，世界！': 'Hello, World!',
      '谢谢': 'Thank you',
      '再见': 'Goodbye',
      '早上好': 'Good morning',
      '晚上好': 'Good evening',
      '我爱你': 'I love you',
      '今天天气很好': 'The weather is nice today',
      '我正在学习编程': 'I am learning programming'
    },
    'ja': {
      '你好': 'こんにちは',
      '世界': '世界',
      '你好，世界！': 'こんにちは、世界！',
      '谢谢': 'ありがとう',
      '再见': 'さようなら',
      '早上好': 'おはよう',
      '晚上好': 'こんばんは',
      '我爱你': '愛してる',
      '今天天气很好': '今日はいい天気ですね',
      '我正在学习编程': 'プログラミングを勉強しています'
    }
  },
  'en': {
    'zh': {
      'Hello': '你好',
      'World': '世界',
      'Hello, World!': '你好，世界！',
      'Thank you': '谢谢',
      'Goodbye': '再见',
      'Good morning': '早上好',
      'Good evening': '晚上好',
      'I love you': '我爱你',
      'The weather is nice today': '今天天气很好',
      'I am learning programming': '我正在学习编程'
    },
    'ja': {
      'Hello': 'こんにちは',
      'World': '世界',
      'Hello, World!': 'こんにちは、世界！',
      'Thank you': 'ありがとう',
      'Goodbye': 'さようなら',
      'Good morning': 'おはよう',
      'Good evening': 'こんばんは',
      'I love you': '愛してる',
      'The weather is nice today': '今日はいい天気ですね',
      'I am learning programming': 'プログラミングを勉強しています'
    }
  },
  'ja': {
    'zh': {
      'こんにちは': '你好',
      '世界': '世界',
      'こんにちは、世界！': '你好，世界！',
      'ありがとう': '谢谢',
      'さようなら': '再见',
      'おはよう': '早上好',
      'こんばんは': '晚上好',
      '愛してる': '我爱你'
    },
    'en': {
      'こんにちは': 'Hello',
      '世界': 'World',
      'こんにちは、世界！': 'Hello, World!',
      'ありがとう': 'Thank you',
      'さようなら': 'Goodbye',
      'おはよう': 'Good morning',
      'こんばんは': 'Good evening',
      '愛してる': 'I love you'
    }
  }
};

export async function executeTranslation(args: TranslationArgs): Promise<TranslationResult> {
  try {
    const { text, target_language, source_language } = args;
    
    // 检测源语言（简单实现）
    const detectedSourceLang = source_language || detectLanguage(text);
    
    // 查找翻译
    let translatedText = text; // 默认返回原文
    
    const sourceTranslations = mockTranslations[detectedSourceLang];
    if (sourceTranslations && sourceTranslations[target_language]) {
      const targetTranslations = sourceTranslations[target_language];
      
      // 精确匹配
      if (targetTranslations[text]) {
        translatedText = targetTranslations[text];
      } else {
        // 模糊匹配或生成模拟翻译
        translatedText = generateMockTranslation(text, target_language);
      }
    } else {
      // 生成模拟翻译
      translatedText = generateMockTranslation(text, target_language);
    }
    
    const result: TranslationResult = {
      original_text: text,
      translated_text: translatedText,
      source_language: detectedSourceLang,
      target_language: target_language
    };
    
    console.log(`🌐 Translation: "${text}" (${detectedSourceLang}) -> "${translatedText}" (${target_language})`);
    
    return result;
  } catch (error: any) {
    console.error('Translation error:', error);
    throw new Error(`Failed to translate text: ${error.message}`);
  }
}

function detectLanguage(text: string): string {
  // 简单的语言检测
  const chineseRegex = /[\u4e00-\u9fff]/;
  const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
  
  if (chineseRegex.test(text)) {
    return 'zh';
  } else if (japaneseRegex.test(text)) {
    return 'ja';
  } else {
    return 'en';
  }
}

function generateMockTranslation(text: string, targetLang: string): string {
  // 简单的模拟翻译生成
  const prefixes = {
    'zh': '[中文翻译] ',
    'en': '[English Translation] ',
    'ja': '[日本語翻訳] '
  };
  
  const prefix = prefixes[targetLang as keyof typeof prefixes] || '[Translation] ';
  return prefix + text;
}
