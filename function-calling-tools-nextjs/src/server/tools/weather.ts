interface WeatherQueryArgs {
  city: string;
  country?: string;
}

export interface WeatherResult {
  city: string;
  temperature: number;
  description: string;
  humidity: number;
  wind_speed: number;
  feels_like: number;
}

// 模拟天气数据 - 在实际项目中应该调用真实的天气API
const mockWeatherData: Record<string, WeatherResult> = {
  '北京': {
    city: '北京',
    temperature: 22,
    description: '晴朗',
    humidity: 45,
    wind_speed: 12,
    feels_like: 24
  },
  '上海': {
    city: '上海',
    temperature: 26,
    description: '多云',
    humidity: 68,
    wind_speed: 8,
    feels_like: 28
  },
  '广州': {
    city: '广州',
    temperature: 29,
    description: '小雨',
    humidity: 78,
    wind_speed: 6,
    feels_like: 32
  },
  'beijing': {
    city: 'Beijing',
    temperature: 22,
    description: 'Sunny',
    humidity: 45,
    wind_speed: 12,
    feels_like: 24
  },
  'shanghai': {
    city: 'Shanghai',
    temperature: 26,
    description: 'Cloudy',
    humidity: 68,
    wind_speed: 8,
    feels_like: 28
  },
  'new york': {
    city: 'New York',
    temperature: 18,
    description: 'Partly Cloudy',
    humidity: 52,
    wind_speed: 15,
    feels_like: 16
  },
  'london': {
    city: 'London',
    temperature: 15,
    description: 'Rainy',
    humidity: 82,
    wind_speed: 20,
    feels_like: 12
  },
  'tokyo': {
    city: 'Tokyo',
    temperature: 24,
    description: 'Clear',
    humidity: 58,
    wind_speed: 10,
    feels_like: 26
  }
};

export async function executeWeatherQuery(args: WeatherQueryArgs): Promise<WeatherResult> {
  try {
    const { city, country } = args;
    
    // 标准化城市名称
    const normalizedCity = city.toLowerCase().trim();
    
    // 查找天气数据
    let weatherData = mockWeatherData[normalizedCity] || mockWeatherData[city];
    
    if (!weatherData) {
      // 如果没有找到精确匹配，尝试模糊匹配
      const fuzzyMatch = Object.keys(mockWeatherData).find(key => 
        key.includes(normalizedCity) || normalizedCity.includes(key)
      );
      
      if (fuzzyMatch) {
        weatherData = mockWeatherData[fuzzyMatch];
      }
    }
    
    if (!weatherData) {
      // 生成随机天气数据作为fallback
      weatherData = generateRandomWeather(city);
    }
    
    // 添加一些随机变化使数据更真实
    const variation = (Math.random() - 0.5) * 4; // ±2度变化
    weatherData = {
      ...weatherData,
      temperature: Math.round((weatherData.temperature + variation) * 10) / 10,
      feels_like: Math.round((weatherData.feels_like + variation) * 10) / 10,
    };
    
    console.log(`🌤️ Weather query for ${city}: ${weatherData.temperature}°C, ${weatherData.description}`);
    
    return weatherData;
  } catch (error: any) {
    console.error('Weather query error:', error);
    throw new Error(`Failed to get weather for ${args.city}: ${error.message}`);
  }
}

function generateRandomWeather(city: string): WeatherResult {
  const descriptions = ['Sunny', 'Cloudy', 'Partly Cloudy', 'Rainy', 'Clear', 'Overcast'];
  const baseTemp = Math.floor(Math.random() * 30) + 5; // 5-35°C
  
  return {
    city,
    temperature: baseTemp,
    description: descriptions[Math.floor(Math.random() * descriptions.length)],
    humidity: Math.floor(Math.random() * 40) + 40, // 40-80%
    wind_speed: Math.floor(Math.random() * 20) + 5, // 5-25 km/h
    feels_like: baseTemp + (Math.random() - 0.5) * 6 // ±3度
  };
}
