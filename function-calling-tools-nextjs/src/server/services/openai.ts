import OpenAI from 'openai';
import { ChatMessage, ToolCall, FunctionDefinition } from '../../types';

// 工具定义
const toolDefinitions: FunctionDefinition[] = [
  {
    name: 'weather_query',
    description: '获取指定城市的天气信息',
    parameters: {
      type: 'object',
      properties: {
        city: {
          type: 'string',
          description: '城市名称'
        },
        country: {
          type: 'string',
          description: '国家代码（可选）'
        }
      },
      required: ['city']
    }
  },
  {
    name: 'translate_text',
    description: '文本翻译服务',
    parameters: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: '要翻译的文本'
        },
        target_language: {
          type: 'string',
          description: '目标语言代码（如：en, zh, ja）'
        },
        source_language: {
          type: 'string',
          description: '源语言代码（可选，自动检测）'
        }
      },
      required: ['text', 'target_language']
    }
  },
  {
    name: 'summarize_text',
    description: '文本摘要生成',
    parameters: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: '要生成摘要的文本'
        },
        max_length: {
          type: 'number',
          description: '摘要最大长度（可选）'
        },
        style: {
          type: 'string',
          description: '摘要风格',
          enum: ['concise', 'detailed', 'bullet_points']
        }
      },
      required: ['text']
    }
  }
];

export class OpenAIService {
  private client: OpenAI;

  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || 'sk-holeazkksyigknkqzozszsgvgzmumdlajxjczwrecwtsdgxw',
      baseURL: 'https://api.siliconflow.cn/v1'
    });
  }

  async createChatCompletion(
    messages: ChatMessage[],
    useTools: boolean = true
  ): Promise<{
    message: ChatMessage;
    toolCalls?: ToolCall[];
  }> {
    try {
      // 转换消息格式为 OpenAI 格式
      const openaiMessages = this.convertToOpenAIMessages(messages);

      const completion = await this.client.chat.completions.create({
        model: 'Qwen/Qwen2.5-32B-Instruct',
        messages: openaiMessages,
        max_tokens: 2000,
        temperature: 0.7,
        tools: useTools ? toolDefinitions.map(tool => ({ type: 'function', function: tool })) : undefined,
        tool_choice: useTools ? 'auto' : undefined,
      });

      const choice = completion.choices[0];
      if (!choice?.message) {
        throw new Error('No response from OpenAI');
      }

      const responseMessage: ChatMessage = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        role: 'assistant',
        content: choice.message.content || '',
        timestamp: Date.now()
      };

      // 处理工具调用
      let toolCalls: ToolCall[] | undefined;
      if (choice.message.tool_calls) {
        toolCalls = choice.message.tool_calls.map(tc => ({
          id: tc.id,
          type: 'function' as const,
          function: {
            name: tc.function.name,
            arguments: tc.function.arguments
          }
        }));
        responseMessage.tool_calls = toolCalls;
      }

      return {
        message: responseMessage,
        toolCalls
      };
    } catch (error: any) {
      console.error('OpenAI API error:', error);
      throw new Error(`Failed to create chat completion: ${error.message}`);
    }
  }

  private convertToOpenAIMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => {
      const openaiMessage: any = {
        role: msg.role,
        content: msg.content
      };

      if (msg.tool_calls) {
        openaiMessage.tool_calls = msg.tool_calls;
      }

      if (msg.tool_call_id) {
        openaiMessage.tool_call_id = msg.tool_call_id;
      }

      return openaiMessage;
    });
  }
}
