// OpenAI Function Calling 相关类型定义

export interface FunctionParameter {
  type: string;
  description: string;
  enum?: string[];
  items?: FunctionParameter;
  properties?: Record<string, FunctionParameter>;
  required?: string[];
}

export interface FunctionDefinition {
  name: string;
  description: string;
  parameters: {
    type: "object";
    properties: Record<string, FunctionParameter>;
    required?: string[];
  };
}

export interface ToolCall {
  id: string;
  type: "function";
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolCallResult {
  tool_call_id: string;
  content: string;
}

// 聊天消息类型
export interface ChatMessage {
  id: string;
  role: "user" | "assistant" | "system" | "tool";
  content: string;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
  timestamp: number;
}

// API 请求和响应类型
export interface ChatRequest {
  message: string;
  conversation_id?: string;
}

export interface ChatResponse {
  message: ChatMessage;
  conversation_id: string;
  tool_calls?: ToolCall[];
}

export interface ToolExecutionRequest {
  tool_call: ToolCall;
  conversation_id: string;
}

export interface ToolExecutionResponse {
  result: ToolCallResult;
  success: boolean;
  error?: string;
}

// 工具执行结果类型
export interface WeatherResult {
  city: string;
  temperature: number;
  description: string;
  humidity: number;
  wind_speed: number;
  feels_like: number;
}

export interface TranslationResult {
  original_text: string;
  translated_text: string;
  source_language: string;
  target_language: string;
}

export interface SummaryResult {
  original_text: string;
  summary: string;
  word_count: {
    original: number;
    summary: number;
  };
}

// 工具类型枚举
export enum ToolType {
  WEATHER = "weather_query",
  TRANSLATION = "translate_text", 
  SUMMARY = "summarize_text"
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// 对话状态
export interface ConversationState {
  id: string;
  messages: ChatMessage[];
  created_at: number;
  updated_at: number;
}
