/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/trpc/[trpc]/route";
exports.ids = ["app/api/trpc/[trpc]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Users_chaoxin_Documents_work_space_other_code_function_calling_tools_function_calling_tools_nextjs_src_app_api_trpc_trpc_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/trpc/[trpc]/route.ts */ \"(rsc)/./src/app/api/trpc/[trpc]/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/trpc/[trpc]/route\",\n        pathname: \"/api/trpc/[trpc]\",\n        filename: \"route\",\n        bundlePath: \"app/api/trpc/[trpc]/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/api/trpc/[trpc]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_chaoxin_Documents_work_space_other_code_function_calling_tools_function_calling_tools_nextjs_src_app_api_trpc_trpc_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/trpc/[trpc]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/trpc/[trpc]/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/trpc/[trpc]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_adapters_fetch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/server/adapters/fetch */ \"(rsc)/./node_modules/@trpc/server/dist/adapters/fetch/index.mjs\");\n/* harmony import */ var _server_api_root__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../server/api/root */ \"(rsc)/./src/server/api/root.ts\");\n\n\nconst handler = (req)=>(0,_trpc_server_adapters_fetch__WEBPACK_IMPORTED_MODULE_1__.fetchRequestHandler)({\n        endpoint: '/api/trpc',\n        req,\n        router: _server_api_root__WEBPACK_IMPORTED_MODULE_0__.appRouter,\n        createContext: ()=>({})\n    });\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS90cnBjL1t0cnBjXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWtFO0FBQ1Y7QUFFeEQsTUFBTUUsVUFBVSxDQUFDQyxNQUNmSCxnRkFBbUJBLENBQUM7UUFDbEJJLFVBQVU7UUFDVkQ7UUFDQUUsUUFBUUosdURBQVNBO1FBQ2pCSyxlQUFlLElBQU8sRUFBQztJQUN6QjtBQUV5QyIsInNvdXJjZXMiOlsiL1VzZXJzL2NoYW94aW4vRG9jdW1lbnRzL3dvcmtfc3BhY2Uvb3RoZXJfY29kZS9mdW5jdGlvbi1jYWxsaW5nLXRvb2xzL2Z1bmN0aW9uLWNhbGxpbmctdG9vbHMtbmV4dGpzL3NyYy9hcHAvYXBpL3RycGMvW3RycGNdL3JvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZldGNoUmVxdWVzdEhhbmRsZXIgfSBmcm9tICdAdHJwYy9zZXJ2ZXIvYWRhcHRlcnMvZmV0Y2gnO1xuaW1wb3J0IHsgYXBwUm91dGVyIH0gZnJvbSAnLi4vLi4vLi4vLi4vc2VydmVyL2FwaS9yb290JztcblxuY29uc3QgaGFuZGxlciA9IChyZXE6IFJlcXVlc3QpID0+XG4gIGZldGNoUmVxdWVzdEhhbmRsZXIoe1xuICAgIGVuZHBvaW50OiAnL2FwaS90cnBjJyxcbiAgICByZXEsXG4gICAgcm91dGVyOiBhcHBSb3V0ZXIsXG4gICAgY3JlYXRlQ29udGV4dDogKCkgPT4gKHt9KSxcbiAgfSk7XG5cbmV4cG9ydCB7IGhhbmRsZXIgYXMgR0VULCBoYW5kbGVyIGFzIFBPU1QgfTtcbiJdLCJuYW1lcyI6WyJmZXRjaFJlcXVlc3RIYW5kbGVyIiwiYXBwUm91dGVyIiwiaGFuZGxlciIsInJlcSIsImVuZHBvaW50Iiwicm91dGVyIiwiY3JlYXRlQ29udGV4dCIsIkdFVCIsIlBPU1QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/trpc/[trpc]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/api/root.ts":
/*!********************************!*\
  !*** ./src/server/api/root.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appRouter: () => (/* binding */ appRouter)\n/* harmony export */ });\n/* harmony import */ var _trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./trpc */ \"(rsc)/./src/server/api/trpc.ts\");\n/* harmony import */ var _routers_chat__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./routers/chat */ \"(rsc)/./src/server/api/routers/chat.ts\");\n/* harmony import */ var _routers_tools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./routers/tools */ \"(rsc)/./src/server/api/routers/tools.ts\");\n\n\n\nconst appRouter = (0,_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    chat: _routers_chat__WEBPACK_IMPORTED_MODULE_1__.chatRouter,\n    tools: _routers_tools__WEBPACK_IMPORTED_MODULE_2__.toolsRouter\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmVyL2FwaS9yb290LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEM7QUFDRTtBQUNFO0FBRXZDLE1BQU1HLFlBQVlILHVEQUFnQkEsQ0FBQztJQUN4Q0ksTUFBTUgscURBQVVBO0lBQ2hCSSxPQUFPSCx1REFBV0E7QUFDcEIsR0FBRyIsInNvdXJjZXMiOlsiL1VzZXJzL2NoYW94aW4vRG9jdW1lbnRzL3dvcmtfc3BhY2Uvb3RoZXJfY29kZS9mdW5jdGlvbi1jYWxsaW5nLXRvb2xzL2Z1bmN0aW9uLWNhbGxpbmctdG9vbHMtbmV4dGpzL3NyYy9zZXJ2ZXIvYXBpL3Jvb3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlVFJQQ1JvdXRlciB9IGZyb20gJy4vdHJwYyc7XG5pbXBvcnQgeyBjaGF0Um91dGVyIH0gZnJvbSAnLi9yb3V0ZXJzL2NoYXQnO1xuaW1wb3J0IHsgdG9vbHNSb3V0ZXIgfSBmcm9tICcuL3JvdXRlcnMvdG9vbHMnO1xuXG5leHBvcnQgY29uc3QgYXBwUm91dGVyID0gY3JlYXRlVFJQQ1JvdXRlcih7XG4gIGNoYXQ6IGNoYXRSb3V0ZXIsXG4gIHRvb2xzOiB0b29sc1JvdXRlcixcbn0pO1xuXG5leHBvcnQgdHlwZSBBcHBSb3V0ZXIgPSB0eXBlb2YgYXBwUm91dGVyO1xuIl0sIm5hbWVzIjpbImNyZWF0ZVRSUENSb3V0ZXIiLCJjaGF0Um91dGVyIiwidG9vbHNSb3V0ZXIiLCJhcHBSb3V0ZXIiLCJjaGF0IiwidG9vbHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/root.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/api/routers/chat.ts":
/*!****************************************!*\
  !*** ./src/server/api/routers/chat.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatRouter: () => (/* binding */ chatRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../trpc */ \"(rsc)/./src/server/api/trpc.ts\");\n/* harmony import */ var _services_openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/openai */ \"(rsc)/./src/server/services/openai.ts\");\n/* harmony import */ var _tools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../tools */ \"(rsc)/./src/server/tools/index.ts\");\n\n\n\n\n// 内存中的对话存储（实际项目中应该使用数据库）\nconst conversations = new Map();\nconst openaiService = new _services_openai__WEBPACK_IMPORTED_MODULE_1__.OpenAIService();\n// 工具执行器映射\nconst toolExecutors = {\n    weather_query: _tools__WEBPACK_IMPORTED_MODULE_2__.weatherTool.execute,\n    translate_text: _tools__WEBPACK_IMPORTED_MODULE_2__.translationTool.execute,\n    summarize_text: _tools__WEBPACK_IMPORTED_MODULE_2__.summaryTool.execute\n};\nasync function executeToolCall(toolCall) {\n    try {\n        const { function: func } = toolCall;\n        const executor = toolExecutors[func.name];\n        if (!executor) {\n            throw new Error(`Unknown tool: ${func.name}`);\n        }\n        // 解析参数\n        let args;\n        try {\n            args = JSON.parse(func.arguments);\n        } catch (error) {\n            throw new Error('Invalid JSON in function arguments');\n        }\n        console.log(`🔧 Executing tool: ${func.name} with args:`, args);\n        // 执行工具函数\n        const result = await executor(args);\n        // 构建工具调用结果\n        const toolResult = {\n            tool_call_id: toolCall.id,\n            content: JSON.stringify(result, null, 2)\n        };\n        console.log(`✅ Tool execution completed: ${func.name}`);\n        return toolResult;\n    } catch (error) {\n        console.error(`❌ Tool execution failed: ${toolCall.function.name}`, error);\n        // 返回错误结果\n        const errorResult = {\n            tool_call_id: toolCall.id,\n            content: JSON.stringify({\n                error: true,\n                message: error.message,\n                code: 'TOOL_EXECUTION_FAILED'\n            }, null, 2)\n        };\n        return errorResult;\n    }\n}\nasync function executeToolCalls(toolCalls) {\n    const results = [];\n    for (const toolCall of toolCalls){\n        const result = await executeToolCall(toolCall);\n        results.push(result);\n    }\n    return results;\n}\nfunction generateId() {\n    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\nconst chatRouter = (0,_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    // 发送聊天消息\n    sendMessage: _trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_3__.object({\n        message: zod__WEBPACK_IMPORTED_MODULE_3__.string(),\n        conversation_id: zod__WEBPACK_IMPORTED_MODULE_3__.string().optional()\n    })).mutation(async ({ input })=>{\n        const { message, conversation_id } = input;\n        // 生成或获取对话ID\n        const conversationId = conversation_id || `conv_${generateId()}`;\n        // 获取对话历史\n        const conversationHistory = conversations.get(conversationId) || [];\n        // 创建用户消息\n        const userMessage = {\n            id: `msg_${generateId()}`,\n            role: 'user',\n            content: message,\n            timestamp: Date.now()\n        };\n        // 添加系统消息（如果是新对话）\n        if (conversationHistory.length === 0) {\n            const systemMessage = {\n                id: `sys_${Date.now()}`,\n                role: 'system',\n                content: `你是一个智能助手，可以使用多种工具来帮助用户。你有以下工具可用：\n1. 天气查询 - 获取城市天气信息\n2. 翻译工具 - 多语言文本翻译\n3. 文本摘要 - 生成文本摘要\n\n请根据用户的需求选择合适的工具，并提供有用的回答。`,\n                timestamp: Date.now()\n            };\n            conversationHistory.push(systemMessage);\n        }\n        // 添加用户消息到历史\n        conversationHistory.push(userMessage);\n        // 调用OpenAI获取响应\n        const { message: assistantMessage, toolCalls } = await openaiService.createChatCompletion(conversationHistory, true);\n        // 添加助手消息到历史\n        conversationHistory.push(assistantMessage);\n        let finalResponse = assistantMessage;\n        // 如果有工具调用，执行工具并获取最终响应\n        if (toolCalls && toolCalls.length > 0) {\n            console.log(`🔧 Executing ${toolCalls.length} tool call(s)`);\n            // 执行工具调用\n            const toolResults = await executeToolCalls(toolCalls);\n            // 添加工具结果到对话历史\n            for (const result of toolResults){\n                const toolMessage = {\n                    id: `tool_${generateId()}`,\n                    role: 'tool',\n                    content: result.content,\n                    tool_call_id: result.tool_call_id,\n                    timestamp: Date.now()\n                };\n                conversationHistory.push(toolMessage);\n            }\n            // 获取基于工具结果的最终响应\n            const { message: finalAssistantMessage } = await openaiService.createChatCompletion(conversationHistory, false // 不再使用工具\n            );\n            conversationHistory.push(finalAssistantMessage);\n            finalResponse = finalAssistantMessage;\n        }\n        // 保存对话历史\n        conversations.set(conversationId, conversationHistory);\n        console.log(`💬 Chat response sent for conversation: ${conversationId}`);\n        return {\n            message: finalResponse,\n            conversation_id: conversationId,\n            tool_calls: toolCalls\n        };\n    }),\n    // 获取对话历史\n    getConversation: _trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_3__.object({\n        conversation_id: zod__WEBPACK_IMPORTED_MODULE_3__.string()\n    })).query(async ({ input })=>{\n        const { conversation_id } = input;\n        const conversationHistory = conversations.get(conversation_id);\n        if (!conversationHistory) {\n            throw new Error('Conversation not found');\n        }\n        return {\n            conversation_id,\n            messages: conversationHistory,\n            message_count: conversationHistory.length\n        };\n    }),\n    // 获取所有对话列表\n    getConversations: _trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.query(async ()=>{\n        const conversationList = Array.from(conversations.entries()).map(([id, messages])=>({\n                conversation_id: id,\n                message_count: messages.length,\n                last_message: messages[messages.length - 1],\n                created_at: messages[0]?.timestamp,\n                updated_at: messages[messages.length - 1]?.timestamp\n            }));\n        return {\n            conversations: conversationList,\n            total_count: conversationList.length\n        };\n    }),\n    // 删除对话\n    deleteConversation: _trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_3__.object({\n        conversation_id: zod__WEBPACK_IMPORTED_MODULE_3__.string()\n    })).mutation(async ({ input })=>{\n        const { conversation_id } = input;\n        const existed = conversations.has(conversation_id);\n        conversations.delete(conversation_id);\n        return {\n            success: true,\n            message: existed ? 'Conversation deleted' : 'Conversation not found',\n            conversation_id\n        };\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmVyL2FwaS9yb3V0ZXJzL2NoYXQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBd0I7QUFDb0M7QUFDTjtBQUVrQjtBQUV4RSx5QkFBeUI7QUFDekIsTUFBTU8sZ0JBQWdCLElBQUlDO0FBRTFCLE1BQU1DLGdCQUFnQixJQUFJTiwyREFBYUE7QUFFdkMsVUFBVTtBQUNWLE1BQU1PLGdCQUFnQjtJQUNwQkMsZUFBZVAsK0NBQVdBLENBQUNRLE9BQU87SUFDbENDLGdCQUFnQlIsbURBQWVBLENBQUNPLE9BQU87SUFDdkNFLGdCQUFnQlIsK0NBQVdBLENBQUNNLE9BQU87QUFDckM7QUFFQSxlQUFlRyxnQkFBZ0JDLFFBQWtCO0lBQy9DLElBQUk7UUFDRixNQUFNLEVBQUVDLFVBQVVDLElBQUksRUFBRSxHQUFHRjtRQUMzQixNQUFNRyxXQUFXVCxhQUFhLENBQUNRLEtBQUtFLElBQUksQ0FBK0I7UUFFdkUsSUFBSSxDQUFDRCxVQUFVO1lBQ2IsTUFBTSxJQUFJRSxNQUFNLENBQUMsY0FBYyxFQUFFSCxLQUFLRSxJQUFJLEVBQUU7UUFDOUM7UUFFQSxPQUFPO1FBQ1AsSUFBSUU7UUFDSixJQUFJO1lBQ0ZBLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ04sS0FBS08sU0FBUztRQUNsQyxFQUFFLE9BQU9DLE9BQU87WUFDZCxNQUFNLElBQUlMLE1BQU07UUFDbEI7UUFFQU0sUUFBUUMsR0FBRyxDQUFDLENBQUMsbUJBQW1CLEVBQUVWLEtBQUtFLElBQUksQ0FBQyxXQUFXLENBQUMsRUFBRUU7UUFFMUQsU0FBUztRQUNULE1BQU1PLFNBQVMsTUFBTVYsU0FBU0c7UUFFOUIsV0FBVztRQUNYLE1BQU1RLGFBQTZCO1lBQ2pDQyxjQUFjZixTQUFTZ0IsRUFBRTtZQUN6QkMsU0FBU1YsS0FBS1csU0FBUyxDQUFDTCxRQUFRLE1BQU07UUFDeEM7UUFFQUYsUUFBUUMsR0FBRyxDQUFDLENBQUMsNEJBQTRCLEVBQUVWLEtBQUtFLElBQUksRUFBRTtRQUV0RCxPQUFPVTtJQUNULEVBQUUsT0FBT0osT0FBWTtRQUNuQkMsUUFBUUQsS0FBSyxDQUFDLENBQUMseUJBQXlCLEVBQUVWLFNBQVNDLFFBQVEsQ0FBQ0csSUFBSSxFQUFFLEVBQUVNO1FBRXBFLFNBQVM7UUFDVCxNQUFNUyxjQUE4QjtZQUNsQ0osY0FBY2YsU0FBU2dCLEVBQUU7WUFDekJDLFNBQVNWLEtBQUtXLFNBQVMsQ0FBQztnQkFDdEJSLE9BQU87Z0JBQ1BVLFNBQVNWLE1BQU1VLE9BQU87Z0JBQ3RCQyxNQUFNO1lBQ1IsR0FBRyxNQUFNO1FBQ1g7UUFFQSxPQUFPRjtJQUNUO0FBQ0Y7QUFFQSxlQUFlRyxpQkFBaUJDLFNBQXFCO0lBQ25ELE1BQU1DLFVBQTRCLEVBQUU7SUFFcEMsS0FBSyxNQUFNeEIsWUFBWXVCLFVBQVc7UUFDaEMsTUFBTVYsU0FBUyxNQUFNZCxnQkFBZ0JDO1FBQ3JDd0IsUUFBUUMsSUFBSSxDQUFDWjtJQUNmO0lBRUEsT0FBT1c7QUFDVDtBQUVBLFNBQVNFO0lBQ1AsT0FBTyxHQUFHQyxLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxJQUFJO0FBQ25FO0FBRU8sTUFBTUMsYUFBYWhELHVEQUFnQkEsQ0FBQztJQUN6QyxTQUFTO0lBQ1RpRCxhQUFhaEQsa0RBQWVBLENBQ3pCaUQsS0FBSyxDQUFDbkQsdUNBQVEsQ0FBQztRQUNkb0MsU0FBU3BDLHVDQUFRO1FBQ2pCc0QsaUJBQWlCdEQsdUNBQVEsR0FBR3VELFFBQVE7SUFDdEMsSUFDQ0MsUUFBUSxDQUFDLE9BQU8sRUFBRUwsS0FBSyxFQUFFO1FBQ3hCLE1BQU0sRUFBRWYsT0FBTyxFQUFFa0IsZUFBZSxFQUFFLEdBQUdIO1FBRXJDLFlBQVk7UUFDWixNQUFNTSxpQkFBaUJILG1CQUFtQixDQUFDLEtBQUssRUFBRVosY0FBYztRQUVoRSxTQUFTO1FBQ1QsTUFBTWdCLHNCQUFzQm5ELGNBQWNvRCxHQUFHLENBQUNGLG1CQUFtQixFQUFFO1FBRW5FLFNBQVM7UUFDVCxNQUFNRyxjQUEyQjtZQUMvQjVCLElBQUksQ0FBQyxJQUFJLEVBQUVVLGNBQWM7WUFDekJtQixNQUFNO1lBQ041QixTQUFTRztZQUNUMEIsV0FBV25CLEtBQUtDLEdBQUc7UUFDckI7UUFFQSxpQkFBaUI7UUFDakIsSUFBSWMsb0JBQW9CSyxNQUFNLEtBQUssR0FBRztZQUNwQyxNQUFNQyxnQkFBNkI7Z0JBQ2pDaEMsSUFBSSxDQUFDLElBQUksRUFBRVcsS0FBS0MsR0FBRyxJQUFJO2dCQUN2QmlCLE1BQU07Z0JBQ041QixTQUFTLENBQUM7Ozs7O3lCQUtLLENBQUM7Z0JBQ2hCNkIsV0FBV25CLEtBQUtDLEdBQUc7WUFDckI7WUFDQWMsb0JBQW9CakIsSUFBSSxDQUFDdUI7UUFDM0I7UUFFQSxZQUFZO1FBQ1pOLG9CQUFvQmpCLElBQUksQ0FBQ21CO1FBRXpCLGVBQWU7UUFDZixNQUFNLEVBQUV4QixTQUFTNkIsZ0JBQWdCLEVBQUUxQixTQUFTLEVBQUUsR0FBRyxNQUFNOUIsY0FBY3lELG9CQUFvQixDQUN2RlIscUJBQ0E7UUFHRixZQUFZO1FBQ1pBLG9CQUFvQmpCLElBQUksQ0FBQ3dCO1FBRXpCLElBQUlFLGdCQUFnQkY7UUFFcEIsc0JBQXNCO1FBQ3RCLElBQUkxQixhQUFhQSxVQUFVd0IsTUFBTSxHQUFHLEdBQUc7WUFDckNwQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxhQUFhLEVBQUVXLFVBQVV3QixNQUFNLENBQUMsYUFBYSxDQUFDO1lBRTNELFNBQVM7WUFDVCxNQUFNSyxjQUFjLE1BQU05QixpQkFBaUJDO1lBRTNDLGNBQWM7WUFDZCxLQUFLLE1BQU1WLFVBQVV1QyxZQUFhO2dCQUNoQyxNQUFNQyxjQUEyQjtvQkFDL0JyQyxJQUFJLENBQUMsS0FBSyxFQUFFVSxjQUFjO29CQUMxQm1CLE1BQU07b0JBQ041QixTQUFTSixPQUFPSSxPQUFPO29CQUN2QkYsY0FBY0YsT0FBT0UsWUFBWTtvQkFDakMrQixXQUFXbkIsS0FBS0MsR0FBRztnQkFDckI7Z0JBQ0FjLG9CQUFvQmpCLElBQUksQ0FBQzRCO1lBQzNCO1lBRUEsZ0JBQWdCO1lBQ2hCLE1BQU0sRUFBRWpDLFNBQVNrQyxxQkFBcUIsRUFBRSxHQUFHLE1BQU03RCxjQUFjeUQsb0JBQW9CLENBQ2pGUixxQkFDQSxNQUFNLFNBQVM7O1lBR2pCQSxvQkFBb0JqQixJQUFJLENBQUM2QjtZQUN6QkgsZ0JBQWdCRztRQUNsQjtRQUVBLFNBQVM7UUFDVC9ELGNBQWNnRSxHQUFHLENBQUNkLGdCQUFnQkM7UUFFbEMvQixRQUFRQyxHQUFHLENBQUMsQ0FBQyx3Q0FBd0MsRUFBRTZCLGdCQUFnQjtRQUV2RSxPQUFPO1lBQ0xyQixTQUFTK0I7WUFDVGIsaUJBQWlCRztZQUNqQmUsWUFBWWpDO1FBQ2Q7SUFDRjtJQUVGLFNBQVM7SUFDVGtDLGlCQUFpQnZFLGtEQUFlQSxDQUM3QmlELEtBQUssQ0FBQ25ELHVDQUFRLENBQUM7UUFDZHNELGlCQUFpQnRELHVDQUFRO0lBQzNCLElBQ0MwRSxLQUFLLENBQUMsT0FBTyxFQUFFdkIsS0FBSyxFQUFFO1FBQ3JCLE1BQU0sRUFBRUcsZUFBZSxFQUFFLEdBQUdIO1FBRTVCLE1BQU1PLHNCQUFzQm5ELGNBQWNvRCxHQUFHLENBQUNMO1FBRTlDLElBQUksQ0FBQ0kscUJBQXFCO1lBQ3hCLE1BQU0sSUFBSXJDLE1BQU07UUFDbEI7UUFFQSxPQUFPO1lBQ0xpQztZQUNBcUIsVUFBVWpCO1lBQ1ZrQixlQUFlbEIsb0JBQW9CSyxNQUFNO1FBQzNDO0lBQ0Y7SUFFRixXQUFXO0lBQ1hjLGtCQUFrQjNFLGtEQUFlQSxDQUM5QndFLEtBQUssQ0FBQztRQUNMLE1BQU1JLG1CQUFtQkMsTUFBTUMsSUFBSSxDQUFDekUsY0FBYzBFLE9BQU8sSUFBSUMsR0FBRyxDQUFDLENBQUMsQ0FBQ2xELElBQUkyQyxTQUFTLEdBQU07Z0JBQ3BGckIsaUJBQWlCdEI7Z0JBQ2pCNEMsZUFBZUQsU0FBU1osTUFBTTtnQkFDOUJvQixjQUFjUixRQUFRLENBQUNBLFNBQVNaLE1BQU0sR0FBRyxFQUFFO2dCQUMzQ3FCLFlBQVlULFFBQVEsQ0FBQyxFQUFFLEVBQUViO2dCQUN6QnVCLFlBQVlWLFFBQVEsQ0FBQ0EsU0FBU1osTUFBTSxHQUFHLEVBQUUsRUFBRUQ7WUFDN0M7UUFFQSxPQUFPO1lBQ0x2RCxlQUFldUU7WUFDZlEsYUFBYVIsaUJBQWlCZixNQUFNO1FBQ3RDO0lBQ0Y7SUFFRixPQUFPO0lBQ1B3QixvQkFBb0JyRixrREFBZUEsQ0FDaENpRCxLQUFLLENBQUNuRCx1Q0FBUSxDQUFDO1FBQ2RzRCxpQkFBaUJ0RCx1Q0FBUTtJQUMzQixJQUNDd0QsUUFBUSxDQUFDLE9BQU8sRUFBRUwsS0FBSyxFQUFFO1FBQ3hCLE1BQU0sRUFBRUcsZUFBZSxFQUFFLEdBQUdIO1FBRTVCLE1BQU1xQyxVQUFVakYsY0FBY2tGLEdBQUcsQ0FBQ25DO1FBQ2xDL0MsY0FBY21GLE1BQU0sQ0FBQ3BDO1FBRXJCLE9BQU87WUFDTHFDLFNBQVM7WUFDVHZELFNBQVNvRCxVQUFVLHlCQUF5QjtZQUM1Q2xDO1FBQ0Y7SUFDRjtBQUNKLEdBQUciLCJzb3VyY2VzIjpbIi9Vc2Vycy9jaGFveGluL0RvY3VtZW50cy93b3JrX3NwYWNlL290aGVyX2NvZGUvZnVuY3Rpb24tY2FsbGluZy10b29scy9mdW5jdGlvbi1jYWxsaW5nLXRvb2xzLW5leHRqcy9zcmMvc2VydmVyL2FwaS9yb3V0ZXJzL2NoYXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgeiB9IGZyb20gJ3pvZCc7XG5pbXBvcnQgeyBjcmVhdGVUUlBDUm91dGVyLCBwdWJsaWNQcm9jZWR1cmUgfSBmcm9tICcuLi90cnBjJztcbmltcG9ydCB7IE9wZW5BSVNlcnZpY2UgfSBmcm9tICcuLi8uLi9zZXJ2aWNlcy9vcGVuYWknO1xuaW1wb3J0IHsgQ2hhdE1lc3NhZ2UsIFRvb2xDYWxsLCBUb29sQ2FsbFJlc3VsdCB9IGZyb20gJy4uLy4uLy4uL3R5cGVzJztcbmltcG9ydCB7IHdlYXRoZXJUb29sLCB0cmFuc2xhdGlvblRvb2wsIHN1bW1hcnlUb29sIH0gZnJvbSAnLi4vLi4vdG9vbHMnO1xuXG4vLyDlhoXlrZjkuK3nmoTlr7nor53lrZjlgqjvvIjlrp7pmYXpobnnm67kuK3lupTor6Xkvb/nlKjmlbDmja7lupPvvIlcbmNvbnN0IGNvbnZlcnNhdGlvbnMgPSBuZXcgTWFwPHN0cmluZywgQ2hhdE1lc3NhZ2VbXT4oKTtcblxuY29uc3Qgb3BlbmFpU2VydmljZSA9IG5ldyBPcGVuQUlTZXJ2aWNlKCk7XG5cbi8vIOW3peWFt+aJp+ihjOWZqOaYoOWwhFxuY29uc3QgdG9vbEV4ZWN1dG9ycyA9IHtcbiAgd2VhdGhlcl9xdWVyeTogd2VhdGhlclRvb2wuZXhlY3V0ZSxcbiAgdHJhbnNsYXRlX3RleHQ6IHRyYW5zbGF0aW9uVG9vbC5leGVjdXRlLFxuICBzdW1tYXJpemVfdGV4dDogc3VtbWFyeVRvb2wuZXhlY3V0ZVxufTtcblxuYXN5bmMgZnVuY3Rpb24gZXhlY3V0ZVRvb2xDYWxsKHRvb2xDYWxsOiBUb29sQ2FsbCk6IFByb21pc2U8VG9vbENhbGxSZXN1bHQ+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGZ1bmN0aW9uOiBmdW5jIH0gPSB0b29sQ2FsbDtcbiAgICBjb25zdCBleGVjdXRvciA9IHRvb2xFeGVjdXRvcnNbZnVuYy5uYW1lIGFzIGtleW9mIHR5cGVvZiB0b29sRXhlY3V0b3JzXTtcbiAgICBcbiAgICBpZiAoIWV4ZWN1dG9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFVua25vd24gdG9vbDogJHtmdW5jLm5hbWV9YCk7XG4gICAgfVxuXG4gICAgLy8g6Kej5p6Q5Y+C5pWwXG4gICAgbGV0IGFyZ3M6IGFueTtcbiAgICB0cnkge1xuICAgICAgYXJncyA9IEpTT04ucGFyc2UoZnVuYy5hcmd1bWVudHMpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgSlNPTiBpbiBmdW5jdGlvbiBhcmd1bWVudHMnKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhg8J+UpyBFeGVjdXRpbmcgdG9vbDogJHtmdW5jLm5hbWV9IHdpdGggYXJnczpgLCBhcmdzKTtcblxuICAgIC8vIOaJp+ihjOW3peWFt+WHveaVsFxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGV4ZWN1dG9yKGFyZ3MpO1xuXG4gICAgLy8g5p6E5bu65bel5YW36LCD55So57uT5p6cXG4gICAgY29uc3QgdG9vbFJlc3VsdDogVG9vbENhbGxSZXN1bHQgPSB7XG4gICAgICB0b29sX2NhbGxfaWQ6IHRvb2xDYWxsLmlkLFxuICAgICAgY29udGVudDogSlNPTi5zdHJpbmdpZnkocmVzdWx0LCBudWxsLCAyKVxuICAgIH07XG5cbiAgICBjb25zb2xlLmxvZyhg4pyFIFRvb2wgZXhlY3V0aW9uIGNvbXBsZXRlZDogJHtmdW5jLm5hbWV9YCk7XG5cbiAgICByZXR1cm4gdG9vbFJlc3VsdDtcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoYOKdjCBUb29sIGV4ZWN1dGlvbiBmYWlsZWQ6ICR7dG9vbENhbGwuZnVuY3Rpb24ubmFtZX1gLCBlcnJvcik7XG4gICAgXG4gICAgLy8g6L+U5Zue6ZSZ6K+v57uT5p6cXG4gICAgY29uc3QgZXJyb3JSZXN1bHQ6IFRvb2xDYWxsUmVzdWx0ID0ge1xuICAgICAgdG9vbF9jYWxsX2lkOiB0b29sQ2FsbC5pZCxcbiAgICAgIGNvbnRlbnQ6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgZXJyb3I6IHRydWUsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIGNvZGU6ICdUT09MX0VYRUNVVElPTl9GQUlMRUQnXG4gICAgICB9LCBudWxsLCAyKVxuICAgIH07XG5cbiAgICByZXR1cm4gZXJyb3JSZXN1bHQ7XG4gIH1cbn1cblxuYXN5bmMgZnVuY3Rpb24gZXhlY3V0ZVRvb2xDYWxscyh0b29sQ2FsbHM6IFRvb2xDYWxsW10pOiBQcm9taXNlPFRvb2xDYWxsUmVzdWx0W10+IHtcbiAgY29uc3QgcmVzdWx0czogVG9vbENhbGxSZXN1bHRbXSA9IFtdO1xuICBcbiAgZm9yIChjb25zdCB0b29sQ2FsbCBvZiB0b29sQ2FsbHMpIHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBleGVjdXRlVG9vbENhbGwodG9vbENhbGwpO1xuICAgIHJlc3VsdHMucHVzaChyZXN1bHQpO1xuICB9XG4gIFxuICByZXR1cm4gcmVzdWx0cztcbn1cblxuZnVuY3Rpb24gZ2VuZXJhdGVJZCgpOiBzdHJpbmcge1xuICByZXR1cm4gYCR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YDtcbn1cblxuZXhwb3J0IGNvbnN0IGNoYXRSb3V0ZXIgPSBjcmVhdGVUUlBDUm91dGVyKHtcbiAgLy8g5Y+R6YCB6IGK5aSp5raI5oGvXG4gIHNlbmRNZXNzYWdlOiBwdWJsaWNQcm9jZWR1cmVcbiAgICAuaW5wdXQoei5vYmplY3Qoe1xuICAgICAgbWVzc2FnZTogei5zdHJpbmcoKSxcbiAgICAgIGNvbnZlcnNhdGlvbl9pZDogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICAgIH0pKVxuICAgIC5tdXRhdGlvbihhc3luYyAoeyBpbnB1dCB9KSA9PiB7XG4gICAgICBjb25zdCB7IG1lc3NhZ2UsIGNvbnZlcnNhdGlvbl9pZCB9ID0gaW5wdXQ7XG5cbiAgICAgIC8vIOeUn+aIkOaIluiOt+WPluWvueivnUlEXG4gICAgICBjb25zdCBjb252ZXJzYXRpb25JZCA9IGNvbnZlcnNhdGlvbl9pZCB8fCBgY29udl8ke2dlbmVyYXRlSWQoKX1gO1xuICAgICAgXG4gICAgICAvLyDojrflj5blr7nor53ljoblj7JcbiAgICAgIGNvbnN0IGNvbnZlcnNhdGlvbkhpc3RvcnkgPSBjb252ZXJzYXRpb25zLmdldChjb252ZXJzYXRpb25JZCkgfHwgW107XG5cbiAgICAgIC8vIOWIm+W7uueUqOaIt+a2iOaBr1xuICAgICAgY29uc3QgdXNlck1lc3NhZ2U6IENoYXRNZXNzYWdlID0ge1xuICAgICAgICBpZDogYG1zZ18ke2dlbmVyYXRlSWQoKX1gLFxuICAgICAgICByb2xlOiAndXNlcicsXG4gICAgICAgIGNvbnRlbnQ6IG1lc3NhZ2UsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgICAgfTtcblxuICAgICAgLy8g5re75Yqg57O757uf5raI5oGv77yI5aaC5p6c5piv5paw5a+56K+d77yJXG4gICAgICBpZiAoY29udmVyc2F0aW9uSGlzdG9yeS5sZW5ndGggPT09IDApIHtcbiAgICAgICAgY29uc3Qgc3lzdGVtTWVzc2FnZTogQ2hhdE1lc3NhZ2UgPSB7XG4gICAgICAgICAgaWQ6IGBzeXNfJHtEYXRlLm5vdygpfWAsXG4gICAgICAgICAgcm9sZTogJ3N5c3RlbScsXG4gICAgICAgICAgY29udGVudDogYOS9oOaYr+S4gOS4quaZuuiDveWKqeaJi++8jOWPr+S7peS9v+eUqOWkmuenjeW3peWFt+adpeW4ruWKqeeUqOaIt+OAguS9oOacieS7peS4i+W3peWFt+WPr+eUqO+8mlxuMS4g5aSp5rCU5p+l6K+iIC0g6I635Y+W5Z+O5biC5aSp5rCU5L+h5oGvXG4yLiDnv7vor5Hlt6XlhbcgLSDlpJror63oqIDmlofmnKznv7vor5FcbjMuIOaWh+acrOaRmOimgSAtIOeUn+aIkOaWh+acrOaRmOimgVxuXG7or7fmoLnmja7nlKjmiLfnmoTpnIDmsYLpgInmi6nlkIjpgILnmoTlt6XlhbfvvIzlubbmj5DkvpvmnInnlKjnmoTlm57nrZTjgIJgLFxuICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgICAgICB9O1xuICAgICAgICBjb252ZXJzYXRpb25IaXN0b3J5LnB1c2goc3lzdGVtTWVzc2FnZSk7XG4gICAgICB9XG5cbiAgICAgIC8vIOa3u+WKoOeUqOaIt+a2iOaBr+WIsOWOhuWPslxuICAgICAgY29udmVyc2F0aW9uSGlzdG9yeS5wdXNoKHVzZXJNZXNzYWdlKTtcblxuICAgICAgLy8g6LCD55SoT3BlbkFJ6I635Y+W5ZON5bqUXG4gICAgICBjb25zdCB7IG1lc3NhZ2U6IGFzc2lzdGFudE1lc3NhZ2UsIHRvb2xDYWxscyB9ID0gYXdhaXQgb3BlbmFpU2VydmljZS5jcmVhdGVDaGF0Q29tcGxldGlvbihcbiAgICAgICAgY29udmVyc2F0aW9uSGlzdG9yeSxcbiAgICAgICAgdHJ1ZVxuICAgICAgKTtcblxuICAgICAgLy8g5re75Yqg5Yqp5omL5raI5oGv5Yiw5Y6G5Y+yXG4gICAgICBjb252ZXJzYXRpb25IaXN0b3J5LnB1c2goYXNzaXN0YW50TWVzc2FnZSk7XG5cbiAgICAgIGxldCBmaW5hbFJlc3BvbnNlID0gYXNzaXN0YW50TWVzc2FnZTtcblxuICAgICAgLy8g5aaC5p6c5pyJ5bel5YW36LCD55So77yM5omn6KGM5bel5YW35bm26I635Y+W5pyA57uI5ZON5bqUXG4gICAgICBpZiAodG9vbENhbGxzICYmIHRvb2xDYWxscy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SnIEV4ZWN1dGluZyAke3Rvb2xDYWxscy5sZW5ndGh9IHRvb2wgY2FsbChzKWApO1xuXG4gICAgICAgIC8vIOaJp+ihjOW3peWFt+iwg+eUqFxuICAgICAgICBjb25zdCB0b29sUmVzdWx0cyA9IGF3YWl0IGV4ZWN1dGVUb29sQ2FsbHModG9vbENhbGxzKTtcblxuICAgICAgICAvLyDmt7vliqDlt6Xlhbfnu5PmnpzliLDlr7nor53ljoblj7JcbiAgICAgICAgZm9yIChjb25zdCByZXN1bHQgb2YgdG9vbFJlc3VsdHMpIHtcbiAgICAgICAgICBjb25zdCB0b29sTWVzc2FnZTogQ2hhdE1lc3NhZ2UgPSB7XG4gICAgICAgICAgICBpZDogYHRvb2xfJHtnZW5lcmF0ZUlkKCl9YCxcbiAgICAgICAgICAgIHJvbGU6ICd0b29sJyxcbiAgICAgICAgICAgIGNvbnRlbnQ6IHJlc3VsdC5jb250ZW50LFxuICAgICAgICAgICAgdG9vbF9jYWxsX2lkOiByZXN1bHQudG9vbF9jYWxsX2lkLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgICAgICAgfTtcbiAgICAgICAgICBjb252ZXJzYXRpb25IaXN0b3J5LnB1c2godG9vbE1lc3NhZ2UpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g6I635Y+W5Z+65LqO5bel5YW357uT5p6c55qE5pyA57uI5ZON5bqUXG4gICAgICAgIGNvbnN0IHsgbWVzc2FnZTogZmluYWxBc3Npc3RhbnRNZXNzYWdlIH0gPSBhd2FpdCBvcGVuYWlTZXJ2aWNlLmNyZWF0ZUNoYXRDb21wbGV0aW9uKFxuICAgICAgICAgIGNvbnZlcnNhdGlvbkhpc3RvcnksXG4gICAgICAgICAgZmFsc2UgLy8g5LiN5YaN5L2/55So5bel5YW3XG4gICAgICAgICk7XG5cbiAgICAgICAgY29udmVyc2F0aW9uSGlzdG9yeS5wdXNoKGZpbmFsQXNzaXN0YW50TWVzc2FnZSk7XG4gICAgICAgIGZpbmFsUmVzcG9uc2UgPSBmaW5hbEFzc2lzdGFudE1lc3NhZ2U7XG4gICAgICB9XG5cbiAgICAgIC8vIOS/neWtmOWvueivneWOhuWPslxuICAgICAgY29udmVyc2F0aW9ucy5zZXQoY29udmVyc2F0aW9uSWQsIGNvbnZlcnNhdGlvbkhpc3RvcnkpO1xuXG4gICAgICBjb25zb2xlLmxvZyhg8J+SrCBDaGF0IHJlc3BvbnNlIHNlbnQgZm9yIGNvbnZlcnNhdGlvbjogJHtjb252ZXJzYXRpb25JZH1gKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbWVzc2FnZTogZmluYWxSZXNwb25zZSxcbiAgICAgICAgY29udmVyc2F0aW9uX2lkOiBjb252ZXJzYXRpb25JZCxcbiAgICAgICAgdG9vbF9jYWxsczogdG9vbENhbGxzXG4gICAgICB9O1xuICAgIH0pLFxuXG4gIC8vIOiOt+WPluWvueivneWOhuWPslxuICBnZXRDb252ZXJzYXRpb246IHB1YmxpY1Byb2NlZHVyZVxuICAgIC5pbnB1dCh6Lm9iamVjdCh7XG4gICAgICBjb252ZXJzYXRpb25faWQ6IHouc3RyaW5nKCksXG4gICAgfSkpXG4gICAgLnF1ZXJ5KGFzeW5jICh7IGlucHV0IH0pID0+IHtcbiAgICAgIGNvbnN0IHsgY29udmVyc2F0aW9uX2lkIH0gPSBpbnB1dDtcbiAgICAgIFxuICAgICAgY29uc3QgY29udmVyc2F0aW9uSGlzdG9yeSA9IGNvbnZlcnNhdGlvbnMuZ2V0KGNvbnZlcnNhdGlvbl9pZCk7XG4gICAgICBcbiAgICAgIGlmICghY29udmVyc2F0aW9uSGlzdG9yeSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NvbnZlcnNhdGlvbiBub3QgZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgY29udmVyc2F0aW9uX2lkLFxuICAgICAgICBtZXNzYWdlczogY29udmVyc2F0aW9uSGlzdG9yeSxcbiAgICAgICAgbWVzc2FnZV9jb3VudDogY29udmVyc2F0aW9uSGlzdG9yeS5sZW5ndGhcbiAgICAgIH07XG4gICAgfSksXG5cbiAgLy8g6I635Y+W5omA5pyJ5a+56K+d5YiX6KGoXG4gIGdldENvbnZlcnNhdGlvbnM6IHB1YmxpY1Byb2NlZHVyZVxuICAgIC5xdWVyeShhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCBjb252ZXJzYXRpb25MaXN0ID0gQXJyYXkuZnJvbShjb252ZXJzYXRpb25zLmVudHJpZXMoKSkubWFwKChbaWQsIG1lc3NhZ2VzXSkgPT4gKHtcbiAgICAgICAgY29udmVyc2F0aW9uX2lkOiBpZCxcbiAgICAgICAgbWVzc2FnZV9jb3VudDogbWVzc2FnZXMubGVuZ3RoLFxuICAgICAgICBsYXN0X21lc3NhZ2U6IG1lc3NhZ2VzW21lc3NhZ2VzLmxlbmd0aCAtIDFdLFxuICAgICAgICBjcmVhdGVkX2F0OiBtZXNzYWdlc1swXT8udGltZXN0YW1wLFxuICAgICAgICB1cGRhdGVkX2F0OiBtZXNzYWdlc1ttZXNzYWdlcy5sZW5ndGggLSAxXT8udGltZXN0YW1wXG4gICAgICB9KSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNvbnZlcnNhdGlvbnM6IGNvbnZlcnNhdGlvbkxpc3QsXG4gICAgICAgIHRvdGFsX2NvdW50OiBjb252ZXJzYXRpb25MaXN0Lmxlbmd0aFxuICAgICAgfTtcbiAgICB9KSxcblxuICAvLyDliKDpmaTlr7nor51cbiAgZGVsZXRlQ29udmVyc2F0aW9uOiBwdWJsaWNQcm9jZWR1cmVcbiAgICAuaW5wdXQoei5vYmplY3Qoe1xuICAgICAgY29udmVyc2F0aW9uX2lkOiB6LnN0cmluZygpLFxuICAgIH0pKVxuICAgIC5tdXRhdGlvbihhc3luYyAoeyBpbnB1dCB9KSA9PiB7XG4gICAgICBjb25zdCB7IGNvbnZlcnNhdGlvbl9pZCB9ID0gaW5wdXQ7XG4gICAgICBcbiAgICAgIGNvbnN0IGV4aXN0ZWQgPSBjb252ZXJzYXRpb25zLmhhcyhjb252ZXJzYXRpb25faWQpO1xuICAgICAgY29udmVyc2F0aW9ucy5kZWxldGUoY29udmVyc2F0aW9uX2lkKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgbWVzc2FnZTogZXhpc3RlZCA/ICdDb252ZXJzYXRpb24gZGVsZXRlZCcgOiAnQ29udmVyc2F0aW9uIG5vdCBmb3VuZCcsXG4gICAgICAgIGNvbnZlcnNhdGlvbl9pZFxuICAgICAgfTtcbiAgICB9KSxcbn0pO1xuIl0sIm5hbWVzIjpbInoiLCJjcmVhdGVUUlBDUm91dGVyIiwicHVibGljUHJvY2VkdXJlIiwiT3BlbkFJU2VydmljZSIsIndlYXRoZXJUb29sIiwidHJhbnNsYXRpb25Ub29sIiwic3VtbWFyeVRvb2wiLCJjb252ZXJzYXRpb25zIiwiTWFwIiwib3BlbmFpU2VydmljZSIsInRvb2xFeGVjdXRvcnMiLCJ3ZWF0aGVyX3F1ZXJ5IiwiZXhlY3V0ZSIsInRyYW5zbGF0ZV90ZXh0Iiwic3VtbWFyaXplX3RleHQiLCJleGVjdXRlVG9vbENhbGwiLCJ0b29sQ2FsbCIsImZ1bmN0aW9uIiwiZnVuYyIsImV4ZWN1dG9yIiwibmFtZSIsIkVycm9yIiwiYXJncyIsIkpTT04iLCJwYXJzZSIsImFyZ3VtZW50cyIsImVycm9yIiwiY29uc29sZSIsImxvZyIsInJlc3VsdCIsInRvb2xSZXN1bHQiLCJ0b29sX2NhbGxfaWQiLCJpZCIsImNvbnRlbnQiLCJzdHJpbmdpZnkiLCJlcnJvclJlc3VsdCIsIm1lc3NhZ2UiLCJjb2RlIiwiZXhlY3V0ZVRvb2xDYWxscyIsInRvb2xDYWxscyIsInJlc3VsdHMiLCJwdXNoIiwiZ2VuZXJhdGVJZCIsIkRhdGUiLCJub3ciLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJjaGF0Um91dGVyIiwic2VuZE1lc3NhZ2UiLCJpbnB1dCIsIm9iamVjdCIsInN0cmluZyIsImNvbnZlcnNhdGlvbl9pZCIsIm9wdGlvbmFsIiwibXV0YXRpb24iLCJjb252ZXJzYXRpb25JZCIsImNvbnZlcnNhdGlvbkhpc3RvcnkiLCJnZXQiLCJ1c2VyTWVzc2FnZSIsInJvbGUiLCJ0aW1lc3RhbXAiLCJsZW5ndGgiLCJzeXN0ZW1NZXNzYWdlIiwiYXNzaXN0YW50TWVzc2FnZSIsImNyZWF0ZUNoYXRDb21wbGV0aW9uIiwiZmluYWxSZXNwb25zZSIsInRvb2xSZXN1bHRzIiwidG9vbE1lc3NhZ2UiLCJmaW5hbEFzc2lzdGFudE1lc3NhZ2UiLCJzZXQiLCJ0b29sX2NhbGxzIiwiZ2V0Q29udmVyc2F0aW9uIiwicXVlcnkiLCJtZXNzYWdlcyIsIm1lc3NhZ2VfY291bnQiLCJnZXRDb252ZXJzYXRpb25zIiwiY29udmVyc2F0aW9uTGlzdCIsIkFycmF5IiwiZnJvbSIsImVudHJpZXMiLCJtYXAiLCJsYXN0X21lc3NhZ2UiLCJjcmVhdGVkX2F0IiwidXBkYXRlZF9hdCIsInRvdGFsX2NvdW50IiwiZGVsZXRlQ29udmVyc2F0aW9uIiwiZXhpc3RlZCIsImhhcyIsImRlbGV0ZSIsInN1Y2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/routers/chat.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/api/routers/tools.ts":
/*!*****************************************!*\
  !*** ./src/server/api/routers/tools.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toolsRouter: () => (/* binding */ toolsRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../trpc */ \"(rsc)/./src/server/api/trpc.ts\");\n/* harmony import */ var _tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../tools */ \"(rsc)/./src/server/tools/index.ts\");\n\n\n\nconst toolsRouter = (0,_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    // 获取所有可用工具\n    getAll: _trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.query(async ()=>{\n        return {\n            tools: (0,_tools__WEBPACK_IMPORTED_MODULE_1__.getAvailableTools)(),\n            total_count: (0,_tools__WEBPACK_IMPORTED_MODULE_1__.getAvailableTools)().length\n        };\n    }),\n    // 天气查询\n    getWeather: _trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        city: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n        country: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional()\n    })).query(async ({ input })=>{\n        return await _tools__WEBPACK_IMPORTED_MODULE_1__.weatherTool.execute(input);\n    }),\n    // 翻译\n    translate: _trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        text: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n        target_language: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n        source_language: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional()\n    })).mutation(async ({ input })=>{\n        return await _tools__WEBPACK_IMPORTED_MODULE_1__.translationTool.execute({\n            text: input.text,\n            target_language: input.target_language,\n            source_language: input.source_language\n        });\n    }),\n    // 文本摘要\n    summarize: _trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        text: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n        max_length: zod__WEBPACK_IMPORTED_MODULE_2__.number().optional(),\n        style: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\n            'concise',\n            'detailed',\n            'bullet_points'\n        ]).optional()\n    })).mutation(async ({ input })=>{\n        return await _tools__WEBPACK_IMPORTED_MODULE_1__.summaryTool.execute(input);\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/routers/tools.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/api/trpc.ts":
/*!********************************!*\
  !*** ./src/server/api/trpc.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTRPCRouter: () => (/* binding */ createTRPCRouter),\n/* harmony export */   publicProcedure: () => (/* binding */ publicProcedure)\n/* harmony export */ });\n/* harmony import */ var _trpc_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server */ \"(rsc)/./node_modules/@trpc/server/dist/initTRPC-IT_6ZYJd.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\nconst t = _trpc_server__WEBPACK_IMPORTED_MODULE_0__.initTRPC.create({\n    errorFormatter ({ shape, error }) {\n        return {\n            ...shape,\n            data: {\n                ...shape.data,\n                zodError: error.cause instanceof zod__WEBPACK_IMPORTED_MODULE_1__.ZodError ? error.cause.flatten() : null\n            }\n        };\n    }\n});\nconst createTRPCRouter = t.router;\nconst publicProcedure = t.procedure;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmVyL2FwaS90cnBjLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0M7QUFDVDtBQUUvQixNQUFNRSxJQUFJRixrREFBUUEsQ0FBQ0csTUFBTSxDQUFDO0lBQ3hCQyxnQkFBZSxFQUFFQyxLQUFLLEVBQUVDLEtBQUssRUFBRTtRQUM3QixPQUFPO1lBQ0wsR0FBR0QsS0FBSztZQUNSRSxNQUFNO2dCQUNKLEdBQUdGLE1BQU1FLElBQUk7Z0JBQ2JDLFVBQ0VGLE1BQU1HLEtBQUssWUFBWVIseUNBQVFBLEdBQUdLLE1BQU1HLEtBQUssQ0FBQ0MsT0FBTyxLQUFLO1lBQzlEO1FBQ0Y7SUFDRjtBQUNGO0FBRU8sTUFBTUMsbUJBQW1CVCxFQUFFVSxNQUFNLENBQUM7QUFDbEMsTUFBTUMsa0JBQWtCWCxFQUFFWSxTQUFTLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9jaGFveGluL0RvY3VtZW50cy93b3JrX3NwYWNlL290aGVyX2NvZGUvZnVuY3Rpb24tY2FsbGluZy10b29scy9mdW5jdGlvbi1jYWxsaW5nLXRvb2xzLW5leHRqcy9zcmMvc2VydmVyL2FwaS90cnBjLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGluaXRUUlBDIH0gZnJvbSAnQHRycGMvc2VydmVyJztcbmltcG9ydCB7IFpvZEVycm9yIH0gZnJvbSAnem9kJztcblxuY29uc3QgdCA9IGluaXRUUlBDLmNyZWF0ZSh7XG4gIGVycm9yRm9ybWF0dGVyKHsgc2hhcGUsIGVycm9yIH0pIHtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uc2hhcGUsXG4gICAgICBkYXRhOiB7XG4gICAgICAgIC4uLnNoYXBlLmRhdGEsXG4gICAgICAgIHpvZEVycm9yOlxuICAgICAgICAgIGVycm9yLmNhdXNlIGluc3RhbmNlb2YgWm9kRXJyb3IgPyBlcnJvci5jYXVzZS5mbGF0dGVuKCkgOiBudWxsLFxuICAgICAgfSxcbiAgICB9O1xuICB9LFxufSk7XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVUUlBDUm91dGVyID0gdC5yb3V0ZXI7XG5leHBvcnQgY29uc3QgcHVibGljUHJvY2VkdXJlID0gdC5wcm9jZWR1cmU7XG4iXSwibmFtZXMiOlsiaW5pdFRSUEMiLCJab2RFcnJvciIsInQiLCJjcmVhdGUiLCJlcnJvckZvcm1hdHRlciIsInNoYXBlIiwiZXJyb3IiLCJkYXRhIiwiem9kRXJyb3IiLCJjYXVzZSIsImZsYXR0ZW4iLCJjcmVhdGVUUlBDUm91dGVyIiwicm91dGVyIiwicHVibGljUHJvY2VkdXJlIiwicHJvY2VkdXJlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/trpc.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/services/openai.ts":
/*!***************************************!*\
  !*** ./src/server/services/openai.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenAIService: () => (/* binding */ OpenAIService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n// 工具定义\nconst toolDefinitions = [\n    {\n        name: 'weather_query',\n        description: '获取指定城市的天气信息',\n        parameters: {\n            type: 'object',\n            properties: {\n                city: {\n                    type: 'string',\n                    description: '城市名称'\n                },\n                country: {\n                    type: 'string',\n                    description: '国家代码（可选）'\n                }\n            },\n            required: [\n                'city'\n            ]\n        }\n    },\n    {\n        name: 'translate_text',\n        description: '文本翻译服务',\n        parameters: {\n            type: 'object',\n            properties: {\n                text: {\n                    type: 'string',\n                    description: '要翻译的文本'\n                },\n                target_language: {\n                    type: 'string',\n                    description: '目标语言代码（如：en, zh, ja）'\n                },\n                source_language: {\n                    type: 'string',\n                    description: '源语言代码（可选，自动检测）'\n                }\n            },\n            required: [\n                'text',\n                'target_language'\n            ]\n        }\n    },\n    {\n        name: 'summarize_text',\n        description: '文本摘要生成',\n        parameters: {\n            type: 'object',\n            properties: {\n                text: {\n                    type: 'string',\n                    description: '要生成摘要的文本'\n                },\n                max_length: {\n                    type: 'number',\n                    description: '摘要最大长度（可选）'\n                },\n                style: {\n                    type: 'string',\n                    description: '摘要风格',\n                    enum: [\n                        'concise',\n                        'detailed',\n                        'bullet_points'\n                    ]\n                }\n            },\n            required: [\n                'text'\n            ]\n        }\n    }\n];\nclass OpenAIService {\n    constructor(){\n        this.client = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            apiKey: process.env.OPENAI_API_KEY || 'sk-holeazkksyigknkqzozszsgvgzmumdlajxjczwrecwtsdgxw',\n            baseURL: 'https://api.siliconflow.cn/v1'\n        });\n    }\n    async createChatCompletion(messages, useTools = true) {\n        try {\n            // 转换消息格式为 OpenAI 格式\n            const openaiMessages = this.convertToOpenAIMessages(messages);\n            const completion = await this.client.chat.completions.create({\n                model: 'Qwen/Qwen2.5-32B-Instruct',\n                messages: openaiMessages,\n                max_tokens: 2000,\n                temperature: 0.7,\n                tools: useTools ? toolDefinitions.map((tool)=>({\n                        type: 'function',\n                        function: tool\n                    })) : undefined,\n                tool_choice: useTools ? 'auto' : undefined\n            });\n            const choice = completion.choices[0];\n            if (!choice?.message) {\n                throw new Error('No response from OpenAI');\n            }\n            const responseMessage = {\n                id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                role: 'assistant',\n                content: choice.message.content || '',\n                timestamp: Date.now()\n            };\n            // 处理工具调用\n            let toolCalls;\n            if (choice.message.tool_calls) {\n                toolCalls = choice.message.tool_calls.map((tc)=>({\n                        id: tc.id,\n                        type: 'function',\n                        function: {\n                            name: tc.function.name,\n                            arguments: tc.function.arguments\n                        }\n                    }));\n                responseMessage.tool_calls = toolCalls;\n            }\n            return {\n                message: responseMessage,\n                toolCalls\n            };\n        } catch (error) {\n            console.error('OpenAI API error:', error);\n            throw new Error(`Failed to create chat completion: ${error.message}`);\n        }\n    }\n    convertToOpenAIMessages(messages) {\n        return messages.map((msg)=>{\n            const openaiMessage = {\n                role: msg.role,\n                content: msg.content\n            };\n            if (msg.tool_calls) {\n                openaiMessage.tool_calls = msg.tool_calls;\n            }\n            if (msg.tool_call_id) {\n                openaiMessage.tool_call_id = msg.tool_call_id;\n            }\n            return openaiMessage;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/services/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/tools/index.ts":
/*!***********************************!*\
  !*** ./src/server/tools/index.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allTools: () => (/* binding */ allTools),\n/* harmony export */   getAvailableTools: () => (/* binding */ getAvailableTools),\n/* harmony export */   summaryTool: () => (/* binding */ summaryTool),\n/* harmony export */   translationTool: () => (/* binding */ translationTool),\n/* harmony export */   weatherTool: () => (/* binding */ weatherTool)\n/* harmony export */ });\n/* harmony import */ var _weather__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./weather */ \"(rsc)/./src/server/tools/weather.ts\");\n/* harmony import */ var _translation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./translation */ \"(rsc)/./src/server/tools/translation.ts\");\n/* harmony import */ var _summary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./summary */ \"(rsc)/./src/server/tools/summary.ts\");\n\n\n\nconst weatherTool = {\n    name: 'weather_query',\n    description: '获取指定城市的天气信息',\n    execute: _weather__WEBPACK_IMPORTED_MODULE_0__.executeWeatherQuery\n};\nconst translationTool = {\n    name: 'translate_text',\n    description: '文本翻译服务',\n    execute: _translation__WEBPACK_IMPORTED_MODULE_1__.executeTranslation\n};\nconst summaryTool = {\n    name: 'summarize_text',\n    description: '文本摘要生成',\n    execute: _summary__WEBPACK_IMPORTED_MODULE_2__.executeSummary\n};\n// 所有工具的集合\nconst allTools = [\n    weatherTool,\n    translationTool,\n    summaryTool\n];\n// 获取所有可用工具的信息\nfunction getAvailableTools() {\n    return allTools.map((tool)=>({\n            name: tool.name,\n            description: tool.description,\n            available: true\n        }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/tools/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/tools/summary.ts":
/*!*************************************!*\
  !*** ./src/server/tools/summary.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   executeSummary: () => (/* binding */ executeSummary)\n/* harmony export */ });\nasync function executeSummary(args) {\n    try {\n        const { text, max_length = 100, style = 'concise' } = args;\n        // 计算原文字数\n        const originalWordCount = countWords(text);\n        // 生成摘要\n        let summary = '';\n        if (style === 'bullet_points') {\n            summary = generateBulletPointSummary(text, max_length);\n        } else if (style === 'detailed') {\n            summary = generateDetailedSummary(text, max_length);\n        } else {\n            summary = generateConciseSummary(text, max_length);\n        }\n        // 计算摘要字数\n        const summaryWordCount = countWords(summary);\n        const result = {\n            original_text: text,\n            summary,\n            word_count: {\n                original: originalWordCount,\n                summary: summaryWordCount\n            }\n        };\n        console.log(`📝 Summary generated: ${originalWordCount} words -> ${summaryWordCount} words`);\n        return result;\n    } catch (error) {\n        console.error('Summary generation error:', error);\n        throw new Error(`Failed to generate summary: ${error.message}`);\n    }\n}\nfunction countWords(text) {\n    // 简单的字数统计\n    return text.split(/\\s+/).filter((word)=>word.length > 0).length;\n}\nfunction generateConciseSummary(text, maxLength) {\n    // 简单的摘要生成 - 实际项目中应使用NLP或AI模型\n    // 提取前几句话\n    const sentences = text.split(/[.!?。！？]+/).filter((s)=>s.trim().length > 0);\n    let summary = '';\n    let currentLength = 0;\n    for (const sentence of sentences){\n        const sentenceWords = countWords(sentence);\n        if (currentLength + sentenceWords <= maxLength) {\n            summary += sentence.trim() + '. ';\n            currentLength += sentenceWords;\n        } else {\n            break;\n        }\n    }\n    return summary.trim();\n}\nfunction generateDetailedSummary(text, maxLength) {\n    // 详细摘要 - 实际项目中应使用NLP或AI模型\n    const conciseSummary = generateConciseSummary(text, maxLength * 0.8);\n    // 添加一些额外的详细信息\n    const detailPrefix = '本文详细讨论了';\n    const detailSuffix = '等内容。';\n    // 提取一些关键词\n    const keywords = extractKeywords(text, 3);\n    return conciseSummary + '\\n\\n' + detailPrefix + keywords.join('、') + detailSuffix;\n}\nfunction generateBulletPointSummary(text, maxLength) {\n    // 要点摘要 - 实际项目中应使用NLP或AI模型\n    const sentences = text.split(/[.!?。！？]+/).filter((s)=>s.trim().length > 0);\n    // 选择前几句作为要点\n    const bulletPoints = sentences.slice(0, Math.min(5, sentences.length)).map((s)=>'• ' + s.trim());\n    return bulletPoints.join('\\n');\n}\nfunction extractKeywords(text, count) {\n    // 简单的关键词提取 - 实际项目中应使用NLP或AI模型\n    const words = text.split(/\\s+/).filter((word)=>word.length > 3);\n    // 去重并选择前几个较长的词\n    const uniqueWords = Array.from(new Set(words)).sort((a, b)=>b.length - a.length).slice(0, count);\n    return uniqueWords;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/tools/summary.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/tools/translation.ts":
/*!*****************************************!*\
  !*** ./src/server/tools/translation.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   executeTranslation: () => (/* binding */ executeTranslation)\n/* harmony export */ });\n// 模拟翻译数据\nconst mockTranslations = {\n    'zh': {\n        'en': {\n            '你好': 'Hello',\n            '世界': 'World',\n            '你好，世界！': 'Hello, World!',\n            '谢谢': 'Thank you',\n            '再见': 'Goodbye',\n            '早上好': 'Good morning',\n            '晚上好': 'Good evening',\n            '我爱你': 'I love you',\n            '今天天气很好': 'The weather is nice today',\n            '我正在学习编程': 'I am learning programming'\n        },\n        'ja': {\n            '你好': 'こんにちは',\n            '世界': '世界',\n            '你好，世界！': 'こんにちは、世界！',\n            '谢谢': 'ありがとう',\n            '再见': 'さようなら',\n            '早上好': 'おはよう',\n            '晚上好': 'こんばんは',\n            '我爱你': '愛してる',\n            '今天天气很好': '今日はいい天気ですね',\n            '我正在学习编程': 'プログラミングを勉強しています'\n        }\n    },\n    'en': {\n        'zh': {\n            'Hello': '你好',\n            'World': '世界',\n            'Hello, World!': '你好，世界！',\n            'Thank you': '谢谢',\n            'Goodbye': '再见',\n            'Good morning': '早上好',\n            'Good evening': '晚上好',\n            'I love you': '我爱你',\n            'The weather is nice today': '今天天气很好',\n            'I am learning programming': '我正在学习编程'\n        },\n        'ja': {\n            'Hello': 'こんにちは',\n            'World': '世界',\n            'Hello, World!': 'こんにちは、世界！',\n            'Thank you': 'ありがとう',\n            'Goodbye': 'さようなら',\n            'Good morning': 'おはよう',\n            'Good evening': 'こんばんは',\n            'I love you': '愛してる',\n            'The weather is nice today': '今日はいい天気ですね',\n            'I am learning programming': 'プログラミングを勉強しています'\n        }\n    },\n    'ja': {\n        'zh': {\n            'こんにちは': '你好',\n            '世界': '世界',\n            'こんにちは、世界！': '你好，世界！',\n            'ありがとう': '谢谢',\n            'さようなら': '再见',\n            'おはよう': '早上好',\n            'こんばんは': '晚上好',\n            '愛してる': '我爱你'\n        },\n        'en': {\n            'こんにちは': 'Hello',\n            '世界': 'World',\n            'こんにちは、世界！': 'Hello, World!',\n            'ありがとう': 'Thank you',\n            'さようなら': 'Goodbye',\n            'おはよう': 'Good morning',\n            'こんばんは': 'Good evening',\n            '愛してる': 'I love you'\n        }\n    }\n};\nasync function executeTranslation(args) {\n    try {\n        const { text, target_language, source_language } = args;\n        // 检测源语言（简单实现）\n        const detectedSourceLang = source_language || detectLanguage(text);\n        // 查找翻译\n        let translatedText = text; // 默认返回原文\n        const sourceTranslations = mockTranslations[detectedSourceLang];\n        if (sourceTranslations && sourceTranslations[target_language]) {\n            const targetTranslations = sourceTranslations[target_language];\n            // 精确匹配\n            if (targetTranslations[text]) {\n                translatedText = targetTranslations[text];\n            } else {\n                // 模糊匹配或生成模拟翻译\n                translatedText = generateMockTranslation(text, target_language);\n            }\n        } else {\n            // 生成模拟翻译\n            translatedText = generateMockTranslation(text, target_language);\n        }\n        const result = {\n            original_text: text,\n            translated_text: translatedText,\n            source_language: detectedSourceLang,\n            target_language: target_language\n        };\n        console.log(`🌐 Translation: \"${text}\" (${detectedSourceLang}) -> \"${translatedText}\" (${target_language})`);\n        return result;\n    } catch (error) {\n        console.error('Translation error:', error);\n        throw new Error(`Failed to translate text: ${error.message}`);\n    }\n}\nfunction detectLanguage(text) {\n    // 简单的语言检测\n    const chineseRegex = /[\\u4e00-\\u9fff]/;\n    const japaneseRegex = /[\\u3040-\\u309f\\u30a0-\\u30ff]/;\n    if (chineseRegex.test(text)) {\n        return 'zh';\n    } else if (japaneseRegex.test(text)) {\n        return 'ja';\n    } else {\n        return 'en';\n    }\n}\nfunction generateMockTranslation(text, targetLang) {\n    // 简单的模拟翻译生成\n    const prefixes = {\n        'zh': '[中文翻译] ',\n        'en': '[English Translation] ',\n        'ja': '[日本語翻訳] '\n    };\n    const prefix = prefixes[targetLang] || '[Translation] ';\n    return prefix + text;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/tools/translation.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/tools/weather.ts":
/*!*************************************!*\
  !*** ./src/server/tools/weather.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   executeWeatherQuery: () => (/* binding */ executeWeatherQuery)\n/* harmony export */ });\n// 模拟天气数据 - 在实际项目中应该调用真实的天气API\nconst mockWeatherData = {\n    '北京': {\n        city: '北京',\n        temperature: 22,\n        description: '晴朗',\n        humidity: 45,\n        wind_speed: 12,\n        feels_like: 24\n    },\n    '上海': {\n        city: '上海',\n        temperature: 26,\n        description: '多云',\n        humidity: 68,\n        wind_speed: 8,\n        feels_like: 28\n    },\n    '广州': {\n        city: '广州',\n        temperature: 29,\n        description: '小雨',\n        humidity: 78,\n        wind_speed: 6,\n        feels_like: 32\n    },\n    'beijing': {\n        city: 'Beijing',\n        temperature: 22,\n        description: 'Sunny',\n        humidity: 45,\n        wind_speed: 12,\n        feels_like: 24\n    },\n    'shanghai': {\n        city: 'Shanghai',\n        temperature: 26,\n        description: 'Cloudy',\n        humidity: 68,\n        wind_speed: 8,\n        feels_like: 28\n    },\n    'new york': {\n        city: 'New York',\n        temperature: 18,\n        description: 'Partly Cloudy',\n        humidity: 52,\n        wind_speed: 15,\n        feels_like: 16\n    },\n    'london': {\n        city: 'London',\n        temperature: 15,\n        description: 'Rainy',\n        humidity: 82,\n        wind_speed: 20,\n        feels_like: 12\n    },\n    'tokyo': {\n        city: 'Tokyo',\n        temperature: 24,\n        description: 'Clear',\n        humidity: 58,\n        wind_speed: 10,\n        feels_like: 26\n    }\n};\nasync function executeWeatherQuery(args) {\n    try {\n        const { city, country } = args;\n        // 标准化城市名称\n        const normalizedCity = city.toLowerCase().trim();\n        // 查找天气数据\n        let weatherData = mockWeatherData[normalizedCity] || mockWeatherData[city];\n        if (!weatherData) {\n            // 如果没有找到精确匹配，尝试模糊匹配\n            const fuzzyMatch = Object.keys(mockWeatherData).find((key)=>key.includes(normalizedCity) || normalizedCity.includes(key));\n            if (fuzzyMatch) {\n                weatherData = mockWeatherData[fuzzyMatch];\n            }\n        }\n        if (!weatherData) {\n            // 生成随机天气数据作为fallback\n            weatherData = generateRandomWeather(city);\n        }\n        // 添加一些随机变化使数据更真实\n        const variation = (Math.random() - 0.5) * 4; // ±2度变化\n        weatherData = {\n            ...weatherData,\n            temperature: Math.round((weatherData.temperature + variation) * 10) / 10,\n            feels_like: Math.round((weatherData.feels_like + variation) * 10) / 10\n        };\n        console.log(`🌤️ Weather query for ${city}: ${weatherData.temperature}°C, ${weatherData.description}`);\n        return weatherData;\n    } catch (error) {\n        console.error('Weather query error:', error);\n        throw new Error(`Failed to get weather for ${args.city}: ${error.message}`);\n    }\n}\nfunction generateRandomWeather(city) {\n    const descriptions = [\n        'Sunny',\n        'Cloudy',\n        'Partly Cloudy',\n        'Rainy',\n        'Clear',\n        'Overcast'\n    ];\n    const baseTemp = Math.floor(Math.random() * 30) + 5; // 5-35°C\n    return {\n        city,\n        temperature: baseTemp,\n        description: descriptions[Math.floor(Math.random() * descriptions.length)],\n        humidity: Math.floor(Math.random() * 40) + 40,\n        wind_speed: Math.floor(Math.random() * 20) + 5,\n        feels_like: baseTemp + (Math.random() - 0.5) * 6 // ±3度\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/tools/weather.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@trpc","vendor-chunks/openai","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();