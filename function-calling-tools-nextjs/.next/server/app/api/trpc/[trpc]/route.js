(()=>{var a={};a.id=841,a.ids=[841],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},4870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6487:()=>{},6559:(a,b,c)=>{"use strict";a.exports=c(4870)},6946:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(898),e=c(2471),f=c(7912);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},8335:()=>{},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9982:(a,b,c)=>{"use strict";let d,e,f,g,h,i;c.r(b),c.d(b,{handler:()=>gq,patchFetch:()=>gp,routeModule:()=>gl,serverHooks:()=>go,workAsyncStorage:()=>gm,workUnitAsyncStorage:()=>gn});var j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ab,ac,ad,ae,af,ag,ah,ai,aj,ak,al,am,an,ao,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aI,aJ,aK,aL,aM,aN,aO={};c.r(aO),c.d(aO,{GET:()=>gk,POST:()=>gk});var aP=c(6559),aQ=c(8088),aR=c(7719),aS=c(6191),aT=c(1289),aU=c(261),aV=c(2603),aW=c(9893),aX=c(4823),aY=c(7220),aZ=c(6946),a$=c(7912),a_=c(9786),a0=c(6143),a1=c(6439),a2=c(3365),a3=c(3274),a4=c(457),a5=c(8761),a6=c(5810),a7=(0,a3.f1)((0,a3.jr)(),1);function a8(a){let b=null,c=Symbol.for("@trpc/server/http/memo"),d=c;return{read:async()=>(d!==c||(null!=b||(b=a().catch(a=>{if(a instanceof a4.gt)throw a;throw new a4.gt({code:"BAD_REQUEST",message:a instanceof Error?a.message:"Invalid input",cause:a})})),d=await b,b=null),d),result:()=>d!==c?d:void 0}}let a9={isMatch(a){var b;return!!(null==(b=a.headers.get("content-type"))?void 0:b.startsWith("application/json"))},async parse(a){var b;let{req:c}=a,d="1"===a.searchParams.get("batch"),e=d?a.path.split(","):[a.path],f=a8(async()=>{let b;if("GET"===c.method){let c=a.searchParams.get("input");c&&(b=JSON.parse(c))}else b=await c.json();if(void 0===b)return{};if(!d)return{0:a.router._def._config.transformer.input.deserialize(b)};if(!(0,a5.Gv)(b))throw new a4.gt({code:"BAD_REQUEST",message:'"input" needs to be an object when doing a batch call'});let f={};for(let c of e.keys()){let d=b[c];void 0!==d&&(f[c]=a.router._def._config.transformer.input.deserialize(d))}return f}),g=await Promise.all(e.map(async(b,c)=>{let d=await (0,a4.Iw)(a.router,b);return{path:b,procedure:d,getRawInput:async()=>{let b=(await f.read())[c];if((null==d?void 0:d._def.type)==="subscription"){var e,g;let c=null!=(e=null!=(g=a.headers.get("last-event-id"))?g:a.searchParams.get("lastEventId"))?e:a.searchParams.get("Last-Event-Id");c&&((0,a5.Gv)(b)?b=(0,a7.default)((0,a7.default)({},b),{},{lastEventId:c}):null!=b||(b={lastEventId:c}))}return b},result:()=>{var a;return null==(a=f.result())?void 0:a[c]}}})),h=new Set(g.map(a=>{var b;return null==(b=a.procedure)?void 0:b._def.type}).filter(Boolean));if(h.size>1)throw new a4.gt({code:"BAD_REQUEST",message:`Cannot mix procedure types in call: ${Array.from(h).join(", ")}`});let i=null!=(b=h.values().next().value)?b:"unknown",j=a.searchParams.get("connectionParams");return{isBatchCall:d,accept:c.headers.get("trpc-accept"),calls:g,type:i,connectionParams:null===j?null:function(a){let b;try{b=JSON.parse(a)}catch(a){throw new a4.gt({code:"PARSE_ERROR",message:"Not JSON-parsable query params",cause:a})}var c=b;try{if(null===c)return null;if(!(0,a5.Gv)(c))throw Error("Expected object");let a=Object.entries(c).filter(([a,b])=>"string"!=typeof b);if(a.length>0)throw Error(`Expected connectionParams to be string values. Got ${a.map(([a,b])=>`${a}: ${typeof b}`).join(", ")}`);return c}catch(a){throw new a4.gt({code:"PARSE_ERROR",message:"Invalid connection params shape",cause:a})}}(j),signal:c.signal,url:a.url}}},ba=[a9,{isMatch(a){var b;return!!(null==(b=a.headers.get("content-type"))?void 0:b.startsWith("multipart/form-data"))},async parse(a){let{req:b}=a;if("POST"!==b.method)throw new a4.gt({code:"METHOD_NOT_SUPPORTED",message:"Only POST requests are supported for multipart/form-data requests"});let c=a8(async()=>await b.formData()),d=await (0,a4.Iw)(a.router,a.path);return{accept:null,calls:[{path:a.path,getRawInput:c.read,result:c.result,procedure:d}],isBatchCall:!1,type:"mutation",connectionParams:null,signal:b.signal,url:a.url}}},{isMatch(a){var b;return!!(null==(b=a.headers.get("content-type"))?void 0:b.startsWith("application/octet-stream"))},async parse(a){let{req:b}=a;if("POST"!==b.method)throw new a4.gt({code:"METHOD_NOT_SUPPORTED",message:"Only POST requests are supported for application/octet-stream requests"});let c=a8(async()=>b.body);return{calls:[{path:a.path,getRawInput:c.read,result:c.result,procedure:await (0,a4.Iw)(a.router,a.path)}],isBatchCall:!1,accept:null,type:"mutation",connectionParams:null,signal:b.signal,url:a.url}}}];async function bb(a){let b=function(a){let b=ba.find(b=>b.isMatch(a));if(b)return b;if(!b&&"GET"===a.method)return a9;throw new a4.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:a.headers.has("content-type")?`Unsupported content-type "${a.headers.get("content-type")}`:"Missing content-type header"})}(a.req);return await b.parse(a)}function bc(a="AbortError"){throw new DOMException(a,"AbortError")}var bd=(0,a3.f1)((0,a3.Vh)(),1);let be=new WeakMap,bf=()=>{};d=Symbol.toStringTag;var bg=class a{constructor(a){(0,bd.default)(this,"promise",void 0),(0,bd.default)(this,"subscribers",[]),(0,bd.default)(this,"settlement",null),(0,bd.default)(this,d,"Unpromise"),"function"==typeof a?this.promise=new Promise(a):this.promise=a;let b=this.promise.then(a=>{let{subscribers:b}=this;this.subscribers=null,this.settlement={status:"fulfilled",value:a},null==b||b.forEach(({resolve:b})=>{b(a)})});"catch"in b&&b.catch(a=>{let{subscribers:b}=this;this.subscribers=null,this.settlement={status:"rejected",reason:a},null==b||b.forEach(({reject:b})=>{b(a)})})}subscribe(){let a,b,{settlement:c}=this;if(null===c){var d;let c,e;if(null===this.subscribers)throw Error("Unpromise settled but still has subscribers");let f={promise:new Promise((a,b)=>{c=a,e=b}),resolve:c,reject:e};this.subscribers=(d=this.subscribers,[...d,f]),a=f.promise,b=()=>{null!==this.subscribers&&(this.subscribers=function(a,b){let c=a.indexOf(b);if(-1!==c)return[...a.slice(0,c),...a.slice(c+1)];return a}(this.subscribers,f))}}else{let{status:d}=c;a="fulfilled"===d?Promise.resolve(c.value):Promise.reject(c.reason),b=bf}return Object.assign(a,{unsubscribe:b})}then(a,b){let c=this.subscribe(),{unsubscribe:d}=c;return Object.assign(c.then(a,b),{unsubscribe:d})}catch(a){let b=this.subscribe(),{unsubscribe:c}=b;return Object.assign(b.catch(a),{unsubscribe:c})}finally(a){let b=this.subscribe(),{unsubscribe:c}=b;return Object.assign(b.finally(a),{unsubscribe:c})}static proxy(b){let c=a.getSubscribablePromise(b);return void 0!==c?c:a.createSubscribablePromise(b)}static createSubscribablePromise(b){let c=new a(b);return be.set(b,c),be.set(c,c),c}static getSubscribablePromise(a){return be.get(a)}static resolve(b){let c="object"==typeof b&&null!==b&&"then"in b&&"function"==typeof b.then?b:Promise.resolve(b);return a.proxy(c).subscribe()}static async any(b){let c=(Array.isArray(b)?b:[...b]).map(a.resolve);try{return await Promise.any(c)}finally{c.forEach(({unsubscribe:a})=>{a()})}}static async race(b){let c=(Array.isArray(b)?b:[...b]).map(a.resolve);try{return await Promise.race(c)}finally{c.forEach(({unsubscribe:a})=>{a()})}}static async raceReferences(a){let b=a.map(bh);try{return await Promise.race(b)}finally{for(let a of b)a.unsubscribe()}}};function bh(a){return bg.proxy(a).then(()=>[a])}function bi(a,b){let c=a[Symbol.asyncDispose];return a[Symbol.asyncDispose]=async()=>{await b(),await (null==c?void 0:c())},a}null!=(j=Symbol).dispose||(j.dispose=Symbol()),null!=(k=Symbol).asyncDispose||(k.asyncDispose=Symbol());let bj=Symbol();function bk(a){let b=null;return function(a,b){let c=a[Symbol.dispose];return a[Symbol.dispose]=()=>{b(),null==c||c()},a}({start(){if(b)throw Error("Timer already started");return new Promise(c=>{b=setTimeout(()=>c(bj),a)})}},()=>{b&&clearTimeout(b)})}var bl=(0,a3.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js"(a,b){b.exports=function(){var a="function"==typeof SuppressedError?SuppressedError:function(a,b){var c=Error();return c.name="SuppressedError",c.error=a,c.suppressed=b,c},b={},c=[];function d(a,b){if(null!=b){if(Object(b)!==b)throw TypeError("using declarations can only be used with objects, functions, null, or undefined.");if(a)var d=b[Symbol.asyncDispose||Symbol.for("Symbol.asyncDispose")];if(void 0===d&&(d=b[Symbol.dispose||Symbol.for("Symbol.dispose")],a))var e=d;if("function"!=typeof d)throw TypeError("Object is not disposable.");e&&(d=function(){try{e.call(b)}catch(a){return Promise.reject(a)}}),c.push({v:b,d:d,a:a})}else a&&c.push({d:b,a:a});return b}return{e:b,u:d.bind(null,!1),a:d.bind(null,!0),d:function(){var d,e=this.e,f=0;function g(){for(;d=c.pop();)try{if(!d.a&&1===f)return f=0,c.push(d),Promise.resolve().then(g);if(d.d){var a=d.d.call(d.v);if(d.a)return f|=2,Promise.resolve(a).then(g,h)}else f|=1}catch(a){return h(a)}if(1===f)return e!==b?Promise.reject(e):Promise.resolve();if(e!==b)throw e}function h(c){return e=e!==b?new a(c,e):c,g()}return g()}}},b.exports.__esModule=!0,b.exports.default=b.exports}}),bm=(0,a3.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js"(a,b){b.exports=function(a,b){this.v=a,this.k=b},b.exports.__esModule=!0,b.exports.default=b.exports}}),bn=(0,a3.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js"(a,b){var c=bm();b.exports=function(a){return new c(a,0)},b.exports.__esModule=!0,b.exports.default=b.exports}}),bo=(0,a3.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js"(a,b){var c=bm();function d(a){var b,d;function e(b,d){try{var g=a[b](d),h=g.value,i=h instanceof c;Promise.resolve(i?h.v:h).then(function(c){if(i){var d="return"===b?"return":"next";if(!h.k||c.done)return e(d,c);c=a[d](c).value}f(g.done?"return":"normal",c)},function(a){e("throw",a)})}catch(a){f("throw",a)}}function f(a,c){switch(a){case"return":b.resolve({value:c,done:!0});break;case"throw":b.reject(c);break;default:b.resolve({value:c,done:!1})}(b=b.next)?e(b.key,b.arg):d=null}this._invoke=function(a,c){return new Promise(function(f,g){var h={key:a,arg:c,resolve:f,reject:g,next:null};d?d=d.next=h:(b=d=h,e(a,c))})},"function"!=typeof a.return&&(this.return=void 0)}d.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},d.prototype.next=function(a){return this._invoke("next",a)},d.prototype.throw=function(a){return this._invoke("throw",a)},d.prototype.return=function(a){return this._invoke("return",a)},b.exports=function(a){return function(){return new d(a.apply(this,arguments))}},b.exports.__esModule=!0,b.exports.default=b.exports}}),bp=(0,a3.f1)(bl(),1),bq=(0,a3.f1)(bn(),1),br=(0,a3.f1)(bo(),1);function bs(a){let b=a[Symbol.asyncIterator]();return b[Symbol.asyncDispose]?b:bi(b,async()=>{var a;await (null==(a=b.return)?void 0:a.call(b))})}function bt(){return(bt=(0,br.default)(function*(a,b){try{let d;var c=(0,bp.default)();let e=c.a(bs(a)),f=c.u(bk(b.maxDurationMs)).start();for(;;){if((d=yield(0,bq.default)(bg.race([e.next(),f])))===bj&&bc(),d.done)return d;yield d.value,d=null}}catch(a){c.e=a}finally{yield(0,bq.default)(c.d())}})).apply(this,arguments)}function bu(){return(bu=(0,br.default)(function*(a,b){try{let d;var c=(0,bp.default)();let e=c.a(bs(a)),f=c.u(bk(b.gracePeriodMs)),g=b.count,h=new Promise(()=>{});for(;;){if((d=yield(0,bq.default)(bg.race([e.next(),h])))===bj&&bc(),d.done)return d.value;yield d.value,0==--g&&(h=f.start()),d=null}}catch(a){c.e=a}finally{yield(0,bq.default)(c.d())}})).apply(this,arguments)}function bv(){let a,b;return{promise:new Promise((c,d)=>{a=c,b=d}),resolve:a,reject:b}}var bw=(0,a3.f1)(bl(),1),bx=(0,a3.f1)(bn(),1),by=(0,a3.f1)(bo(),1);function bz(a){let b=a[Symbol.asyncIterator]();return new ReadableStream({async cancel(){var a;await (null==(a=b.return)?void 0:a.call(b))},async pull(a){let c=await b.next();if(c.done)return void a.close();a.enqueue(c.value)}})}var bA=(0,a3.f1)(bl(),1),bB=(0,a3.f1)(bn(),1),bC=(0,a3.f1)(bo(),1);let bD=Symbol("ping");function bE(a,b){return bF.apply(this,arguments)}function bF(){return(bF=(0,bC.default)(function*(a,b){try{let e;var c=(0,bA.default)();let f=c.a(bs(a)),g=f.next();for(;;)try{var d=(0,bA.default)();let a=d.u(bk(b));if((e=yield(0,bB.default)(bg.race([g,a.start()])))===bj){yield bD;continue}if(e.done)return e.value;g=f.next(),yield e.value,e=null}catch(a){d.e=a}finally{d.d()}}catch(a){c.e=a}finally{yield(0,bB.default)(c.d())}})).apply(this,arguments)}var bG=(0,a3.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(a,b){function c(a){function b(a){if(Object(a)!==a)return Promise.reject(TypeError(a+" is not an object."));var b=a.done;return Promise.resolve(a.value).then(function(a){return{value:a,done:b}})}return(c=function(a){this.s=a,this.n=a.next}).prototype={s:null,n:null,next:function(){return b(this.n.apply(this.s,arguments))},return:function(a){var c=this.s.return;return void 0===c?Promise.resolve({value:a,done:!0}):b(c.apply(this.s,arguments))},throw:function(a){var c=this.s.return;return void 0===c?Promise.reject(a):b(c.apply(this.s,arguments))}},new c(a)}b.exports=function(a){var b,d,e,f=2;for("undefined"!=typeof Symbol&&(d=Symbol.asyncIterator,e=Symbol.iterator);f--;){if(d&&null!=(b=a[d]))return b.call(a);if(e&&null!=(b=a[e]))return new c(b.call(a));d="@@asyncIterator",e="@@iterator"}throw TypeError("Object is not async iterable")},b.exports.__esModule=!0,b.exports.default=b.exports}}),bH=(0,a3.f1)(bn(),1),bI=(0,a3.f1)(bo(),1),bJ=(0,a3.f1)(bl(),1),bK=(0,a3.f1)(bG(),1);function bL(a){return((0,a5.Gv)(a)||(0,a5.Tn)(a))&&"function"==typeof(null==a?void 0:a.then)&&"function"==typeof(null==a?void 0:a.catch)}var bM=class extends Error{constructor(a){super("Max depth reached at path: "+a.join(".")),this.path=a}};function bN(){return(bN=(0,bI.default)(function*(a){let{data:b}=a,c=0,d=function(){let a="idle",b=bv(),c=[],d=new Set,e=[];function f(c){if("pending"!==a)return;let f=function(a,b){let c=a[Symbol.asyncIterator](),d="idle";function e(){d="done",b=()=>{}}return{pull:function(){"idle"===d&&(d="pending",c.next().then(a=>{if(a.done){d="done",b({status:"return",value:a.value}),e();return}d="idle",b({status:"yield",value:a.value})}).catch(a=>{b({status:"error",error:a}),e()}))},destroy:async()=>{var a;e(),await (null==(a=c.return)?void 0:a.call(c))}}}(c,c=>{if("pending"===a){switch(c.status){case"yield":e.push([f,c]);break;case"return":d.delete(f);break;case"error":e.push([f,c]),d.delete(f)}b.resolve()}});d.add(f),f.pull()}return{add(b){switch(a){case"idle":c.push(b);break;case"pending":f(b)}},[Symbol.asyncIterator]:()=>(0,by.default)(function*(){try{var g=(0,bw.default)();if("idle"!==a)throw Error("Cannot iterate twice");for(a="pending",g.a(bi({},async()=>{a="done";let c=[];if(await Promise.all(Array.from(d.values()).map(async a=>{try{await a.destroy()}catch(a){c.push(a)}})),e.length=0,d.clear(),b.resolve(),c.length>0)throw AggregateError(c)}));c.length>0;)f(c.shift());for(;d.size>0;){for(yield(0,bx.default)(b.promise);e.length>0;){let[a,b]=e.shift();switch(b.status){case"yield":yield b.value,a.pull();break;case"error":throw b.error}}b=bv()}}catch(a){g.e=a}finally{yield(0,bx.default)(g.d())}})()}}();function e(a){let b=c++,e=a(b);return d.add(e),b}function f(b){return a.maxDepth&&b.length>a.maxDepth?new bM(b):null}function g(b,c){var d,g,i;if(bL(b))return[0,(d=b,e((g=(0,bI.default)(function*(b){let e=f(c);e&&(d.catch(b=>{var d;null==(d=a.onError)||d.call(a,{error:b,path:c})}),d=Promise.reject(e));try{let a=yield(0,bH.default)(d);yield[b,0,h(a,c)]}catch(d){var g,i;null==(g=a.onError)||g.call(a,{error:d,path:c}),yield[b,1,null==(i=a.formatError)?void 0:i.call(a,{error:d,path:c})]}}),function(a){return g.apply(this,arguments)})))];if((0,a5.Td)(b)){if(a.maxDepth&&c.length>=a.maxDepth)throw Error("Max depth reached");return[1,e((i=(0,bI.default)(function*(d){try{var e,g,i=(0,bJ.default)();let j=f(c);if(j)throw j;let k=i.a(bs(b));try{for(;;){let a=yield(0,bH.default)(k.next());if(a.done){yield[d,0,h(a.value,c)];break}yield[d,1,h(a.value,c)]}}catch(b){null==(e=a.onError)||e.call(a,{error:b,path:c}),yield[d,2,null==(g=a.formatError)?void 0:g.call(a,{error:b,path:c})]}}catch(a){i.e=a}finally{yield(0,bH.default)(i.d())}}),function(a){return i.apply(this,arguments)}))]}return null}function h(a,b){if(void 0===a)return[[]];let c=g(a,b);if(c)return[[0],[null,...c]];if("[object Object]"!==Object.prototype.toString.call(a))return[[a]];let d={},e=[];for(let[c,f]of Object.entries(a)){let a=g(f,[...b,c]);if(!a){d[c]=f;continue}d[c]=0,e.push([c,...a])}return[[d],...e]}let i={};for(let[a,c]of Object.entries(b))i[a]=h(c,[a]);yield i;let j=d;a.pingMs&&(j=bE(d,a.pingMs));var k=!1,l=!1;try{for(var m,n,o=(0,bK.default)(j);k=!(n=yield(0,bH.default)(o.next())).done;k=!1){let a=n.value;yield a}}catch(a){l=!0,m=a}finally{try{k&&null!=o.return&&(yield(0,bH.default)(o.return()))}finally{if(l)throw m}}})).apply(this,arguments)}var bO=(0,a3.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncGeneratorDelegate.js"(a,b){var c=bm();b.exports=function(a){var b={},d=!1;function e(b,e){return d=!0,{done:!1,value:new c(e=new Promise(function(c){c(a[b](e))}),1)}}return b["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},b.next=function(a){return d?(d=!1,a):e("next",a)},"function"==typeof a.throw&&(b.throw=function(a){if(d)throw d=!1,a;return e("throw",a)}),"function"==typeof a.return&&(b.return=function(a){return d?(d=!1,a):e("return",a)}),b},b.exports.__esModule=!0,b.exports.default=b.exports}}),bP=(0,a3.f1)(bG(),1),bQ=(0,a3.f1)(bn(),1),bR=(0,a3.f1)(bo(),1),bS=(0,a3.f1)(bO(),1);(0,a3.f1)(bl(),1);let bT={"Content-Type":"text/event-stream","Cache-Control":"no-cache, no-transform","X-Accel-Buffering":"no",Connection:"keep-alive"};var bU=(0,a3.f1)(bo(),1),bV=(0,a3.f1)((0,a3.jr)(),1);function bW(a){return(0,a5.eF)((0,bU.default)(function*(){throw a}))}let bX={mutation:["POST"],query:["GET"],subscription:["GET"]},bY={mutation:["POST"],query:["GET","POST"],subscription:["GET","POST"]};function bZ(a){var b,c,d;let{ctx:e,info:f,responseMeta:g,untransformedJSON:h,errors:i=[],headers:j}=a,k=h?(0,a3.E$)(h):200,l=!h,m=l?[]:Array.isArray(h)?h:[h],n=null!=(b=null==g?void 0:g({ctx:e,info:f,paths:null==f?void 0:f.calls.map(a=>a.path),data:m,errors:i,eagerGeneration:l,type:null!=(c=null==f||null==(d=f.calls.find(a=>{var b;return null==(b=a.procedure)?void 0:b._def.type}))||null==(d=d.procedure)?void 0:d._def.type)?c:"unknown"}))?b:{};if(n.headers)if(n.headers instanceof Headers)for(let[a,b]of n.headers.entries())j.append(a,b);else for(let[a,b]of Object.entries(n.headers))if(Array.isArray(b))for(let c of b)j.append(a,c);else"string"==typeof b&&j.set(a,b);return n.status&&(k=n.status),{status:k}}function b$(a){return!!(0,a5.Gv)(a)&&(!!(0,a5.Td)(a)||Object.values(a).some(bL)||Object.values(a).some(a5.Td))}async function b_(a){var b,c,d,e,f,g,h;let{router:i,req:j}=a,k=new Headers([["vary","trpc-accept"]]),l=i._def._config,m=new URL(j.url);if("HEAD"===j.method)return new Response(null,{status:204});let n=null==(b=null!=(c=a.allowBatching)?c:null==(d=a.batching)?void 0:d.enabled)||b,o=null!=(e=a.allowMethodOverride)&&e&&"POST"===j.method,p=await (0,a5.eF)(async()=>{try{return[void 0,await bb({req:j,path:decodeURIComponent(a.path),router:i,searchParams:m.searchParams,headers:a.req.headers,url:m})]}catch(a){return[(0,a4.qO)(a),void 0]}}),q=(0,a5.eF)(()=>{let b;return{valueOrUndefined:()=>{if(b)return b[1]},value:()=>{let[a,c]=b;if(a)throw a;return c},create:async c=>{if(b)throw Error("This should only be called once - report a bug in tRPC");try{let d=await a.createContext({info:c});b=[void 0,d]}catch(a){b=[(0,a4.qO)(a),void 0]}}}}),r=o?bY:bX,s="application/jsonl"===j.headers.get("trpc-accept"),t=null==(f=null==(g=l.sse)?void 0:g.enabled)||f;try{let[b,c]=p;if(b)throw b;if(c.isBatchCall&&!n)throw new a4.gt({code:"BAD_REQUEST",message:"Batching is not enabled on the server"});if(s&&!c.isBatchCall)throw new a4.gt({message:"Streaming requests must be batched (you can do a batch of 1)",code:"BAD_REQUEST"});await q.create(c);let d=c.calls.map(async b=>{let d=b.procedure;try{if(a.error)throw a.error;if(!d)throw new a4.gt({code:"NOT_FOUND",message:`No procedure found on path "${b.path}"`});if(!r[d._def.type].includes(j.method))throw new a4.gt({code:"METHOD_NOT_SUPPORTED",message:`Unsupported ${j.method}-request to ${d._def.type} procedure at path "${b.path}"`});if("subscription"===d._def.type&&c.isBatchCall)throw new a4.gt({code:"BAD_REQUEST",message:"Cannot batch subscription calls"});let e=await d({path:b.path,getRawInput:b.getRawInput,ctx:q.value(),type:d._def.type,signal:a.req.signal});return[void 0,{data:e}]}catch(h){var e,f,g;let c=(0,a4.qO)(h),d=b.result();return null==(e=a.onError)||e.call(a,{error:c,path:b.path,input:d,ctx:q.valueOrUndefined(),type:null!=(f=null==(g=b.procedure)?void 0:g._def.type)?f:"unknown",req:a.req}),[c,void 0]}});if(!c.isBatchCall){let[b]=c.calls,[e,f]=await d[0];switch(c.type){case"unknown":case"mutation":case"query":{if(k.set("content-type","application/json"),b$(null==f?void 0:f.data))throw new a4.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:"Cannot use stream-like response in non-streaming request - use httpBatchStreamLink"});let d=e?{error:(0,a3.KQ)({config:l,ctx:q.valueOrUndefined(),error:e,input:b.result(),path:b.path,type:c.type})}:{result:{data:f.data}},g=bZ({ctx:q.valueOrUndefined(),info:c,responseMeta:a.responseMeta,errors:e?[e]:[],headers:k,untransformedJSON:[d]});return new Response(JSON.stringify((0,a4.t9)(l,d)),{status:g.status,headers:k})}case"subscription":{let d=(0,a5.eF)(()=>e?bW(e):t?(0,a6.AT)(f.data)||(0,a5.Td)(f.data)?(0,a6.AT)(f.data)?(0,a6.di)(f.data,a.req.signal):f.data:bW(new a4.gt({message:`Subscription ${b.path} did not return an observable or a AsyncGenerator`,code:"INTERNAL_SERVER_ERROR"})):bW(new a4.gt({code:"METHOD_NOT_SUPPORTED",message:'Missing experimental flag "sseSubscriptions"'}))),g=function(a){var b,c,d,e,f;let{serialize:g=a5.D_}=a,h={enabled:null!=(b=null==(c=a.ping)?void 0:c.enabled)&&b,intervalMs:null!=(d=null==(e=a.ping)?void 0:e.intervalMs)?d:1e3},i=null!=(f=a.client)?f:{};if(h.enabled&&i.reconnectAfterInactivityMs&&h.intervalMs>i.reconnectAfterInactivityMs)throw Error(`Ping interval must be less than client reconnect interval to prevent unnecessary reconnection - ping.intervalMs: ${h.intervalMs} client.reconnectAfterInactivityMs: ${i.reconnectAfterInactivityMs}`);function j(){return(j=(0,bR.default)(function*(){let b,c;yield{event:"connected",data:JSON.stringify(i)};let d=a.data;a.emitAndEndImmediately&&(d=function(a,b){return bu.apply(this,arguments)}(d,{count:1,gracePeriodMs:1})),a.maxDurationMs&&a.maxDurationMs>0&&a.maxDurationMs!==1/0&&(d=function(a,b){return bt.apply(this,arguments)}(d,{maxDurationMs:a.maxDurationMs})),h.enabled&&h.intervalMs!==1/0&&h.intervalMs>0&&(d=bE(d,h.intervalMs));var e=!1,f=!1;try{for(var j,k,l=(0,bP.default)(d);e=!(k=yield(0,bQ.default)(l.next())).done;e=!1){if((b=k.value)===bD){yield{event:"ping",data:""};continue}(c=(0,a4.vJ)(b)?{id:b[0],data:b[1]}:{data:b}).data=JSON.stringify(g(c.data)),yield c,b=null,c=null}}catch(a){f=!0,j=a}finally{try{e&&null!=l.return&&(yield(0,bQ.default)(l.return()))}finally{if(f)throw j}}})).apply(this,arguments)}function k(){return(k=(0,bR.default)(function*(){try{yield*(0,bS.default)((0,bP.default)(function(){return j.apply(this,arguments)}())),yield{event:"return",data:""}}catch(f){var b,c;if((0,a5.Gv)(f)&&"AbortError"===f.name)return;let d=(0,a4.qO)(f),e=null!=(b=null==(c=a.formatError)?void 0:c.call(a,{error:d}))?b:null;yield{event:"serialized-error",data:JSON.stringify(g(e))}}})).apply(this,arguments)}return bz(function(){return k.apply(this,arguments)}()).pipeThrough(new TransformStream({transform(a,b){"event"in a&&b.enqueue(`event: ${a.event}
`),"data"in a&&b.enqueue(`data: ${a.data}
`),"id"in a&&b.enqueue(`id: ${a.id}
`),"comment"in a&&b.enqueue(`: ${a.comment}
`),b.enqueue("\n\n")}})).pipeThrough(new TextEncoderStream)}((0,bV.default)((0,bV.default)({},l.sse),{},{data:d,serialize:a=>l.transformer.output.serialize(a),formatError(c){var d,e,f;let g=(0,a4.qO)(c.error),h=null==b?void 0:b.result(),i=null==b?void 0:b.path,j=null!=(d=null==b||null==(e=b.procedure)?void 0:e._def.type)?d:"unknown";return null==(f=a.onError)||f.call(a,{error:g,path:i,input:h,ctx:q.valueOrUndefined(),req:a.req,type:j}),(0,a3.KQ)({config:l,ctx:q.valueOrUndefined(),error:g,input:h,path:i,type:j})}}));for(let[a,b]of Object.entries(bT))k.set(a,b);let h=bZ({ctx:q.valueOrUndefined(),info:c,responseMeta:a.responseMeta,errors:[],headers:k,untransformedJSON:null});return new Response(g,{headers:k,status:h.status})}}}if("application/jsonl"===c.accept){k.set("content-type","application/json"),k.set("transfer-encoding","chunked");let b=bZ({ctx:q.valueOrUndefined(),info:c,responseMeta:a.responseMeta,errors:[],headers:k,untransformedJSON:null}),e=function(a){let b=bz(function(a){return bN.apply(this,arguments)}(a)),{serialize:c}=a;return c&&(b=b.pipeThrough(new TransformStream({transform(a,b){a===bD?b.enqueue(bD):b.enqueue(c(a))}}))),b.pipeThrough(new TransformStream({transform(a,b){a===bD?b.enqueue(" "):b.enqueue(JSON.stringify(a)+"\n")}})).pipeThrough(new TextEncoderStream)}((0,bV.default)((0,bV.default)({},l.jsonl),{},{maxDepth:1/0,data:d.map(async b=>{let[d,e]=await b,f=c.calls[0];if(d){var g,h;return{error:(0,a3.KQ)({config:l,ctx:q.valueOrUndefined(),error:d,input:f.result(),path:f.path,type:null!=(g=null==(h=f.procedure)?void 0:h._def.type)?g:"unknown"})}}let i=(0,a6.AT)(e.data)?(0,a6.di)(e.data,a.req.signal):Promise.resolve(e.data);return{result:Promise.resolve({data:i})}}),serialize:l.transformer.output.serialize,onError:b=>{var d,e;null==(d=a.onError)||d.call(a,{error:(0,a4.qO)(b),path:void 0,input:void 0,ctx:q.valueOrUndefined(),req:a.req,type:null!=(e=null==c?void 0:c.type)?e:"unknown"})},formatError(a){var b,d;let e=null==c?void 0:c.calls[a.path[0]],f=(0,a4.qO)(a.error),g=null==e?void 0:e.result(),h=null==e?void 0:e.path,i=null!=(b=null==e||null==(d=e.procedure)?void 0:d._def.type)?b:"unknown";return(0,a3.KQ)({config:l,ctx:q.valueOrUndefined(),error:f,input:g,path:h,type:i})}}));return new Response(e,{headers:k,status:b.status})}k.set("content-type","application/json");let e=(await Promise.all(d)).map(a=>{let[b,c]=a;return b?a:b$(c.data)?[new a4.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:"Cannot use stream-like response in non-streaming request - use httpBatchStreamLink"}),void 0]:a}),f=e.map(([a,b],d)=>{let e=c.calls[d];if(a){var f,g;return{error:(0,a3.KQ)({config:l,ctx:q.valueOrUndefined(),error:a,input:e.result(),path:e.path,type:null!=(f=null==(g=e.procedure)?void 0:g._def.type)?f:"unknown"})}}return{result:{data:b.data}}}),g=e.map(([a])=>a).filter(Boolean),h=bZ({ctx:q.valueOrUndefined(),info:c,responseMeta:a.responseMeta,untransformedJSON:f,errors:g,headers:k});return new Response(JSON.stringify((0,a4.t9)(l,f)),{status:h.status,headers:k})}catch(i){let[b,c]=p,d=q.valueOrUndefined(),{error:e,untransformedJSON:f,body:g}=function(a,b){let{router:c,req:d,onError:e}=b.opts,f=(0,a4.qO)(a);null==e||e({error:f,path:b.path,input:b.input,ctx:b.ctx,type:b.type,req:d});let g={error:(0,a3.KQ)({config:c._def._config,error:f,type:b.type,path:b.path,input:b.input,ctx:b.ctx})},h=JSON.stringify((0,a4.t9)(c._def._config,g));return{error:f,untransformedJSON:g,body:h}}(i,{opts:a,ctx:q.valueOrUndefined(),type:null!=(h=null==c?void 0:c.type)?h:"unknown"});return new Response(g,{status:bZ({ctx:d,info:c,responseMeta:a.responseMeta,untransformedJSON:f,errors:[e],headers:k}).status,headers:k})}}var b0=(0,a3.f1)((0,a3.jr)(),1);let b1=a=>a=(a=a.startsWith("/")?a.slice(1):a).endsWith("/")?a.slice(0,-1):a;async function b2(a){let b=new Headers,c=async c=>{var d;return null==(d=a.createContext)?void 0:d.call(a,(0,b0.default)({req:a.req,resHeaders:b},c))},d=b1(new URL(a.req.url).pathname),e=b1(a.endpoint),f=b1(d.slice(e.length));return await b_((0,b0.default)((0,b0.default)({},a),{},{req:a.req,createContext:c,path:f,error:null,onError(b){var c;null==a||null==(c=a.onError)||c.call(a,(0,b0.default)((0,b0.default)({},b),{},{req:a.req}))},responseMeta(c){var d;let e=null==(d=a.responseMeta)?void 0:d.call(a,c);if(null==e?void 0:e.headers)if(e.headers instanceof Headers)for(let[a,c]of e.headers.entries())b.append(a,c);else for(let[a,c]of Object.entries(e.headers))if(Array.isArray(c))for(let d of c)b.append(a,d);else"string"==typeof c&&b.set(a,c);return{headers:b,status:null==e?void 0:e.status}}}))}var b3=(0,a3.f1)((0,a3.jr)(),1);let b4="middlewareMarker";var b5=(0,a3.f1)((0,a3.Vh)(),1),b6=class extends Error{constructor(a){var b;super(null==(b=a[0])?void 0:b.message),(0,b5.default)(this,"issues",void 0),this.name="SchemaError",this.issues=a}};function b7(a){let b="~standard"in a;if("function"==typeof a&&"function"==typeof a.assert)return a.assert.bind(a);if("function"==typeof a&&!b)return a;if("function"==typeof a.parseAsync)return a.parseAsync.bind(a);if("function"==typeof a.parse)return a.parse.bind(a);if("function"==typeof a.validateSync)return a.validateSync.bind(a);if("function"==typeof a.create)return a.create.bind(a);if("function"==typeof a.assert)return b=>(a.assert(b),b);if(b)return async b=>{let c=await a["~standard"].validate(b);if(c.issues)throw new b6(c.issues);return c.value};throw Error("Could not find a validator fn")}var b8=(0,a3.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js"(a,b){b.exports=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(b.includes(d))continue;c[d]=a[d]}return c},b.exports.__esModule=!0,b.exports.default=b.exports}}),b9=(0,a3.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutProperties.js"(a,b){var c=b8();b.exports=function(a,b){if(null==a)return{};var d,e,f=c(a,b);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(a);for(e=0;e<g.length;e++)d=g[e],b.includes(d)||({}).propertyIsEnumerable.call(a,d)&&(f[d]=a[d])}return f},b.exports.__esModule=!0,b.exports.default=b.exports}}),ca=(0,a3.f1)(b9(),1),cb=(0,a3.f1)((0,a3.jr)(),1);let cc=["middlewares","inputs","meta"];function cd(a,b){let{middlewares:c=[],inputs:d,meta:e}=b,f=(0,ca.default)(b,cc);return ce((0,cb.default)((0,cb.default)({},(0,a5.uf)(a,f)),{},{inputs:[...a.inputs,...null!=d?d:[]],middlewares:[...a.middlewares,...c],meta:a.meta&&e?(0,cb.default)((0,cb.default)({},a.meta),e):null!=e?e:a.meta}))}function ce(a={}){let b=(0,cb.default)({procedure:!0,inputs:[],middlewares:[]},a);return{_def:b,input(a){let c=b7(a);return cd(b,{inputs:[a],middlewares:[function(a){let b=async function(b){let c,d=await b.getRawInput();try{c=await a(d)}catch(a){throw new a4.gt({code:"BAD_REQUEST",cause:a})}let e=(0,a5.Gv)(b.input)&&(0,a5.Gv)(c)?(0,b3.default)((0,b3.default)({},b.input),c):c;return b.next({input:e})};return b._type="input",b}(c)]})},output(a){let c=b7(a);return cd(b,{output:a,middlewares:[function(a){let b=async function({next:b}){let c=await b();if(!c.ok)return c;try{let b=await a(c.data);return(0,b3.default)((0,b3.default)({},c),{},{data:b})}catch(a){throw new a4.gt({message:"Output validation failed",code:"INTERNAL_SERVER_ERROR",cause:a})}};return b._type="output",b}(c)]})},meta:a=>cd(b,{meta:a}),use:a=>cd(b,{middlewares:"_middlewares"in a?a._middlewares:[a]}),unstable_concat:a=>cd(b,a._def),concat:a=>cd(b,a._def),query:a=>cf((0,cb.default)((0,cb.default)({},b),{},{type:"query"}),a),mutation:a=>cf((0,cb.default)((0,cb.default)({},b),{},{type:"mutation"}),a),subscription:a=>cf((0,cb.default)((0,cb.default)({},b),{},{type:"subscription"}),a),experimental_caller:a=>cd(b,{caller:a})}}function cf(a,b){let c=cd(a,{resolver:b,middlewares:[async function(a){return{marker:b4,ok:!0,data:await b(a),ctx:a.ctx}}]}),d=(0,cb.default)((0,cb.default)({},c._def),{},{type:a.type,experimental_caller:!!c._def.caller,meta:c._def.meta,$types:null}),e=function(a){async function b(b){if(!b||!("getRawInput"in b))throw Error(cg);let c=await ch(0,a,b);if(!c)throw new a4.gt({code:"INTERNAL_SERVER_ERROR",message:"No result from middlewares - did you forget to `return next()`?"});if(!c.ok)throw c.error;return c.data}return b._def=a,b.procedure=!0,b.meta=a.meta,b}(c._def),f=c._def.caller;if(!f)return e;let g=async(...a)=>await f({args:a,invoke:e,_def:d});return g._def=d,g}let cg=`
This is a client-only function.
If you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls
`.trim();async function ch(a,b,c){try{let d=b.middlewares[a];return await d((0,cb.default)((0,cb.default)({},c),{},{meta:b.meta,input:c.input,next(d){var e;return ch(a+1,b,(0,cb.default)((0,cb.default)({},c),{},{ctx:(null==d?void 0:d.ctx)?(0,cb.default)((0,cb.default)({},c.ctx),d.ctx):c.ctx,input:d&&"input"in d?d.input:c.input,getRawInput:null!=(e=null==d?void 0:d.getRawInput)?e:c.getRawInput}))}}))}catch(a){return{ok:!1,error:(0,a4.qO)(a),marker:b4}}}let ci="undefined"==typeof window||"Deno"in window||(null==(l=globalThis.process)||null==(l=l.env)?void 0:l.NODE_ENV)==="test"||!!(null==(m=globalThis.process)||null==(m=m.env)?void 0:m.JEST_WORKER_ID)||!!(null==(n=globalThis.process)||null==(n=n.env)?void 0:n.VITEST_WORKER_ID);var cj=(0,a3.f1)((0,a3.jr)(),1);let ck=new class a{context(){return new a}meta(){return new a}create(a){var b,c,d,e,f,g,h;let i=(0,cj.default)((0,cj.default)({},a),{},{transformer:(0,a4.u$)(null!=(b=null==a?void 0:a.transformer)?b:a4.bJ),isDev:null!=(c=null==a?void 0:a.isDev)?c:(null==(d=globalThis.process)?void 0:d.env.NODE_ENV)!=="production",allowOutsideOfServer:null!=(e=null==a?void 0:a.allowOutsideOfServer)&&e,errorFormatter:null!=(f=null==a?void 0:a.errorFormatter)?f:a4.EN,isServer:null!=(g=null==a?void 0:a.isServer)?g:ci,$types:null});if(!(null!=(h=null==a?void 0:a.isServer)?h:ci)&&(null==a?void 0:a.allowOutsideOfServer)!==!0)throw Error("You're trying to use @trpc/server in a non-server environment. This is not supported by default.");return{_config:i,procedure:ce({meta:null==a?void 0:a.defaultMeta}),middleware:function(a){return function a(b){return{_middlewares:b,unstable_pipe:c=>a([...b,..."_middlewares"in c?c._middlewares:[c]])}}([a])},router:(0,a4.OX)(i),mergeRouters:a4.ri,createCallerFactory:(0,a4.OA)()}}};!function(a){a.assertEqual=a=>{},a.assertIs=function(a){},a.assertNever=function(a){throw Error()},a.arrayToEnum=a=>{let b={};for(let c of a)b[c]=c;return b},a.getValidEnumValues=b=>{let c=a.objectKeys(b).filter(a=>"number"!=typeof b[b[a]]),d={};for(let a of c)d[a]=b[a];return a.objectValues(d)},a.objectValues=b=>a.objectKeys(b).map(function(a){return b[a]}),a.objectKeys="function"==typeof Object.keys?a=>Object.keys(a):a=>{let b=[];for(let c in a)Object.prototype.hasOwnProperty.call(a,c)&&b.push(c);return b},a.find=(a,b)=>{for(let c of a)if(b(c))return c},a.isInteger="function"==typeof Number.isInteger?a=>Number.isInteger(a):a=>"number"==typeof a&&Number.isFinite(a)&&Math.floor(a)===a,a.joinValues=function(a,b=" | "){return a.map(a=>"string"==typeof a?`'${a}'`:a).join(b)},a.jsonStringifyReplacer=(a,b)=>"bigint"==typeof b?b.toString():b}(o||(o={})),(p||(p={})).mergeShapes=(a,b)=>({...a,...b});let cl=o.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),cm=a=>{switch(typeof a){case"undefined":return cl.undefined;case"string":return cl.string;case"number":return Number.isNaN(a)?cl.nan:cl.number;case"boolean":return cl.boolean;case"function":return cl.function;case"bigint":return cl.bigint;case"symbol":return cl.symbol;case"object":if(Array.isArray(a))return cl.array;if(null===a)return cl.null;if(a.then&&"function"==typeof a.then&&a.catch&&"function"==typeof a.catch)return cl.promise;if("undefined"!=typeof Map&&a instanceof Map)return cl.map;if("undefined"!=typeof Set&&a instanceof Set)return cl.set;if("undefined"!=typeof Date&&a instanceof Date)return cl.date;return cl.object;default:return cl.unknown}},cn=o.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class co extends Error{get errors(){return this.issues}constructor(a){super(),this.issues=[],this.addIssue=a=>{this.issues=[...this.issues,a]},this.addIssues=(a=[])=>{this.issues=[...this.issues,...a]};let b=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,b):this.__proto__=b,this.name="ZodError",this.issues=a}format(a){let b=a||function(a){return a.message},c={_errors:[]},d=a=>{for(let e of a.issues)if("invalid_union"===e.code)e.unionErrors.map(d);else if("invalid_return_type"===e.code)d(e.returnTypeError);else if("invalid_arguments"===e.code)d(e.argumentsError);else if(0===e.path.length)c._errors.push(b(e));else{let a=c,d=0;for(;d<e.path.length;){let c=e.path[d];d===e.path.length-1?(a[c]=a[c]||{_errors:[]},a[c]._errors.push(b(e))):a[c]=a[c]||{_errors:[]},a=a[c],d++}}};return d(this),c}static assert(a){if(!(a instanceof co))throw Error(`Not a ZodError: ${a}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,o.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(a=a=>a.message){let b={},c=[];for(let d of this.issues)if(d.path.length>0){let c=d.path[0];b[c]=b[c]||[],b[c].push(a(d))}else c.push(a(d));return{formErrors:c,fieldErrors:b}}get formErrors(){return this.flatten()}}co.create=a=>new co(a);let cp=ck.create({errorFormatter:({shape:a,error:b})=>({...a,data:{...a.data,zodError:b.cause instanceof co?b.cause.flatten():null}})}),cq=cp.router,cr=cp.procedure,cs=(a,b)=>{let c;switch(a.code){case cn.invalid_type:c=a.received===cl.undefined?"Required":`Expected ${a.expected}, received ${a.received}`;break;case cn.invalid_literal:c=`Invalid literal value, expected ${JSON.stringify(a.expected,o.jsonStringifyReplacer)}`;break;case cn.unrecognized_keys:c=`Unrecognized key(s) in object: ${o.joinValues(a.keys,", ")}`;break;case cn.invalid_union:c="Invalid input";break;case cn.invalid_union_discriminator:c=`Invalid discriminator value. Expected ${o.joinValues(a.options)}`;break;case cn.invalid_enum_value:c=`Invalid enum value. Expected ${o.joinValues(a.options)}, received '${a.received}'`;break;case cn.invalid_arguments:c="Invalid function arguments";break;case cn.invalid_return_type:c="Invalid function return type";break;case cn.invalid_date:c="Invalid date";break;case cn.invalid_string:"object"==typeof a.validation?"includes"in a.validation?(c=`Invalid input: must include "${a.validation.includes}"`,"number"==typeof a.validation.position&&(c=`${c} at one or more positions greater than or equal to ${a.validation.position}`)):"startsWith"in a.validation?c=`Invalid input: must start with "${a.validation.startsWith}"`:"endsWith"in a.validation?c=`Invalid input: must end with "${a.validation.endsWith}"`:o.assertNever(a.validation):c="regex"!==a.validation?`Invalid ${a.validation}`:"Invalid";break;case cn.too_small:c="array"===a.type?`Array must contain ${a.exact?"exactly":a.inclusive?"at least":"more than"} ${a.minimum} element(s)`:"string"===a.type?`String must contain ${a.exact?"exactly":a.inclusive?"at least":"over"} ${a.minimum} character(s)`:"number"===a.type||"bigint"===a.type?`Number must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${a.minimum}`:"date"===a.type?`Date must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(a.minimum))}`:"Invalid input";break;case cn.too_big:c="array"===a.type?`Array must contain ${a.exact?"exactly":a.inclusive?"at most":"less than"} ${a.maximum} element(s)`:"string"===a.type?`String must contain ${a.exact?"exactly":a.inclusive?"at most":"under"} ${a.maximum} character(s)`:"number"===a.type?`Number must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:"bigint"===a.type?`BigInt must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:"date"===a.type?`Date must be ${a.exact?"exactly":a.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(a.maximum))}`:"Invalid input";break;case cn.custom:c="Invalid input";break;case cn.invalid_intersection_types:c="Intersection results could not be merged";break;case cn.not_multiple_of:c=`Number must be a multiple of ${a.multipleOf}`;break;case cn.not_finite:c="Number must be finite";break;default:c=b.defaultError,o.assertNever(a)}return{message:c}};!function(a){a.errToObj=a=>"string"==typeof a?{message:a}:a||{},a.toString=a=>"string"==typeof a?a:a?.message}(q||(q={}));let ct=a=>{let{data:b,path:c,errorMaps:d,issueData:e}=a,f=[...c,...e.path||[]],g={...e,path:f};if(void 0!==e.message)return{...e,path:f,message:e.message};let h="";for(let a of d.filter(a=>!!a).slice().reverse())h=a(g,{data:b,defaultError:h}).message;return{...e,path:f,message:h}};function cu(a,b){let c=ct({issueData:b,data:a.data,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,cs,void 0].filter(a=>!!a)});a.common.issues.push(c)}class cv{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(a,b){let c=[];for(let d of b){if("aborted"===d.status)return cw;"dirty"===d.status&&a.dirty(),c.push(d.value)}return{status:a.value,value:c}}static async mergeObjectAsync(a,b){let c=[];for(let a of b){let b=await a.key,d=await a.value;c.push({key:b,value:d})}return cv.mergeObjectSync(a,c)}static mergeObjectSync(a,b){let c={};for(let d of b){let{key:b,value:e}=d;if("aborted"===b.status||"aborted"===e.status)return cw;"dirty"===b.status&&a.dirty(),"dirty"===e.status&&a.dirty(),"__proto__"!==b.value&&(void 0!==e.value||d.alwaysSet)&&(c[b.value]=e.value)}return{status:a.value,value:c}}}let cw=Object.freeze({status:"aborted"}),cx=a=>({status:"dirty",value:a}),cy=a=>({status:"valid",value:a}),cz=a=>"undefined"!=typeof Promise&&a instanceof Promise;class cA{constructor(a,b,c,d){this._cachedPath=[],this.parent=a,this.data=b,this._path=c,this._key=d}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let cB=(a,b)=>{if("valid"===b.status)return{success:!0,data:b.value};if(!a.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let b=new co(a.common.issues);return this._error=b,this._error}}};function cC(a){if(!a)return{};let{errorMap:b,invalid_type_error:c,required_error:d,description:e}=a;if(b&&(c||d))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return b?{errorMap:b,description:e}:{errorMap:(b,e)=>{let{message:f}=a;return"invalid_enum_value"===b.code?{message:f??e.defaultError}:void 0===e.data?{message:f??d??e.defaultError}:"invalid_type"!==b.code?{message:e.defaultError}:{message:f??c??e.defaultError}},description:e}}class cD{get description(){return this._def.description}_getType(a){return cm(a.data)}_getOrReturnCtx(a,b){return b||{common:a.parent.common,data:a.data,parsedType:cm(a.data),schemaErrorMap:this._def.errorMap,path:a.path,parent:a.parent}}_processInputParams(a){return{status:new cv,ctx:{common:a.parent.common,data:a.data,parsedType:cm(a.data),schemaErrorMap:this._def.errorMap,path:a.path,parent:a.parent}}}_parseSync(a){let b=this._parse(a);if(cz(b))throw Error("Synchronous parse encountered promise.");return b}_parseAsync(a){return Promise.resolve(this._parse(a))}parse(a,b){let c=this.safeParse(a,b);if(c.success)return c.data;throw c.error}safeParse(a,b){let c={common:{issues:[],async:b?.async??!1,contextualErrorMap:b?.errorMap},path:b?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:cm(a)},d=this._parseSync({data:a,path:c.path,parent:c});return cB(c,d)}"~validate"(a){let b={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:cm(a)};if(!this["~standard"].async)try{let c=this._parseSync({data:a,path:[],parent:b});return"valid"===c.status?{value:c.value}:{issues:b.common.issues}}catch(a){a?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),b.common={issues:[],async:!0}}return this._parseAsync({data:a,path:[],parent:b}).then(a=>"valid"===a.status?{value:a.value}:{issues:b.common.issues})}async parseAsync(a,b){let c=await this.safeParseAsync(a,b);if(c.success)return c.data;throw c.error}async safeParseAsync(a,b){let c={common:{issues:[],contextualErrorMap:b?.errorMap,async:!0},path:b?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:cm(a)},d=this._parse({data:a,path:c.path,parent:c});return cB(c,await (cz(d)?d:Promise.resolve(d)))}refine(a,b){return this._refinement((c,d)=>{let e=a(c),f=()=>d.addIssue({code:cn.custom,..."string"==typeof b||void 0===b?{message:b}:"function"==typeof b?b(c):b});return"undefined"!=typeof Promise&&e instanceof Promise?e.then(a=>!!a||(f(),!1)):!!e||(f(),!1)})}refinement(a,b){return this._refinement((c,d)=>!!a(c)||(d.addIssue("function"==typeof b?b(c,d):b),!1))}_refinement(a){return new dm({schema:this,typeName:r.ZodEffects,effect:{type:"refinement",refinement:a}})}superRefine(a){return this._refinement(a)}constructor(a){this.spa=this.safeParseAsync,this._def=a,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:a=>this["~validate"](a)}}optional(){return dn.create(this,this._def)}nullable(){return dp.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return c5.create(this)}promise(){return dl.create(this,this._def)}or(a){return c7.create([this,a],this._def)}and(a){return da.create(this,a,this._def)}transform(a){return new dm({...cC(this._def),schema:this,typeName:r.ZodEffects,effect:{type:"transform",transform:a}})}default(a){return new dq({...cC(this._def),innerType:this,defaultValue:"function"==typeof a?a:()=>a,typeName:r.ZodDefault})}brand(){return new dt({typeName:r.ZodBranded,type:this,...cC(this._def)})}catch(a){return new dr({...cC(this._def),innerType:this,catchValue:"function"==typeof a?a:()=>a,typeName:r.ZodCatch})}describe(a){return new this.constructor({...this._def,description:a})}pipe(a){return du.create(this,a)}readonly(){return dv.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let cE=/^c[^\s-]{8,}$/i,cF=/^[0-9a-z]+$/,cG=/^[0-9A-HJKMNP-TV-Z]{26}$/i,cH=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,cI=/^[a-z0-9_-]{21}$/i,cJ=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,cK=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,cL=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,cM=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,cN=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,cO=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,cP=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,cQ=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,cR=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,cS="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",cT=RegExp(`^${cS}$`);function cU(a){let b="[0-5]\\d";a.precision?b=`${b}\\.\\d{${a.precision}}`:null==a.precision&&(b=`${b}(\\.\\d+)?`);let c=a.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${b})${c}`}class cV extends cD{_parse(a){var b,c,d,f;let g;if(this._def.coerce&&(a.data=String(a.data)),this._getType(a)!==cl.string){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.string,received:b.parsedType}),cw}let h=new cv;for(let i of this._def.checks)if("min"===i.kind)a.data.length<i.value&&(cu(g=this._getOrReturnCtx(a,g),{code:cn.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),h.dirty());else if("max"===i.kind)a.data.length>i.value&&(cu(g=this._getOrReturnCtx(a,g),{code:cn.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),h.dirty());else if("length"===i.kind){let b=a.data.length>i.value,c=a.data.length<i.value;(b||c)&&(g=this._getOrReturnCtx(a,g),b?cu(g,{code:cn.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&cu(g,{code:cn.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),h.dirty())}else if("email"===i.kind)cL.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"email",code:cn.invalid_string,message:i.message}),h.dirty());else if("emoji"===i.kind)e||(e=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),e.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"emoji",code:cn.invalid_string,message:i.message}),h.dirty());else if("uuid"===i.kind)cH.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"uuid",code:cn.invalid_string,message:i.message}),h.dirty());else if("nanoid"===i.kind)cI.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"nanoid",code:cn.invalid_string,message:i.message}),h.dirty());else if("cuid"===i.kind)cE.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"cuid",code:cn.invalid_string,message:i.message}),h.dirty());else if("cuid2"===i.kind)cF.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"cuid2",code:cn.invalid_string,message:i.message}),h.dirty());else if("ulid"===i.kind)cG.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"ulid",code:cn.invalid_string,message:i.message}),h.dirty());else if("url"===i.kind)try{new URL(a.data)}catch{cu(g=this._getOrReturnCtx(a,g),{validation:"url",code:cn.invalid_string,message:i.message}),h.dirty()}else"regex"===i.kind?(i.regex.lastIndex=0,i.regex.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"regex",code:cn.invalid_string,message:i.message}),h.dirty())):"trim"===i.kind?a.data=a.data.trim():"includes"===i.kind?a.data.includes(i.value,i.position)||(cu(g=this._getOrReturnCtx(a,g),{code:cn.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),h.dirty()):"toLowerCase"===i.kind?a.data=a.data.toLowerCase():"toUpperCase"===i.kind?a.data=a.data.toUpperCase():"startsWith"===i.kind?a.data.startsWith(i.value)||(cu(g=this._getOrReturnCtx(a,g),{code:cn.invalid_string,validation:{startsWith:i.value},message:i.message}),h.dirty()):"endsWith"===i.kind?a.data.endsWith(i.value)||(cu(g=this._getOrReturnCtx(a,g),{code:cn.invalid_string,validation:{endsWith:i.value},message:i.message}),h.dirty()):"datetime"===i.kind?(function(a){let b=`${cS}T${cU(a)}`,c=[];return c.push(a.local?"Z?":"Z"),a.offset&&c.push("([+-]\\d{2}:?\\d{2})"),b=`${b}(${c.join("|")})`,RegExp(`^${b}$`)})(i).test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{code:cn.invalid_string,validation:"datetime",message:i.message}),h.dirty()):"date"===i.kind?cT.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{code:cn.invalid_string,validation:"date",message:i.message}),h.dirty()):"time"===i.kind?RegExp(`^${cU(i)}$`).test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{code:cn.invalid_string,validation:"time",message:i.message}),h.dirty()):"duration"===i.kind?cK.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"duration",code:cn.invalid_string,message:i.message}),h.dirty()):"ip"===i.kind?(b=a.data,!(("v4"===(c=i.version)||!c)&&cM.test(b)||("v6"===c||!c)&&cO.test(b))&&1&&(cu(g=this._getOrReturnCtx(a,g),{validation:"ip",code:cn.invalid_string,message:i.message}),h.dirty())):"jwt"===i.kind?!function(a,b){if(!cJ.test(a))return!1;try{let[c]=a.split(".");if(!c)return!1;let d=c.replace(/-/g,"+").replace(/_/g,"/").padEnd(c.length+(4-c.length%4)%4,"="),e=JSON.parse(atob(d));if("object"!=typeof e||null===e||"typ"in e&&e?.typ!=="JWT"||!e.alg||b&&e.alg!==b)return!1;return!0}catch{return!1}}(a.data,i.alg)&&(cu(g=this._getOrReturnCtx(a,g),{validation:"jwt",code:cn.invalid_string,message:i.message}),h.dirty()):"cidr"===i.kind?(d=a.data,!(("v4"===(f=i.version)||!f)&&cN.test(d)||("v6"===f||!f)&&cP.test(d))&&1&&(cu(g=this._getOrReturnCtx(a,g),{validation:"cidr",code:cn.invalid_string,message:i.message}),h.dirty())):"base64"===i.kind?cQ.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"base64",code:cn.invalid_string,message:i.message}),h.dirty()):"base64url"===i.kind?cR.test(a.data)||(cu(g=this._getOrReturnCtx(a,g),{validation:"base64url",code:cn.invalid_string,message:i.message}),h.dirty()):o.assertNever(i);return{status:h.value,value:a.data}}_regex(a,b,c){return this.refinement(b=>a.test(b),{validation:b,code:cn.invalid_string,...q.errToObj(c)})}_addCheck(a){return new cV({...this._def,checks:[...this._def.checks,a]})}email(a){return this._addCheck({kind:"email",...q.errToObj(a)})}url(a){return this._addCheck({kind:"url",...q.errToObj(a)})}emoji(a){return this._addCheck({kind:"emoji",...q.errToObj(a)})}uuid(a){return this._addCheck({kind:"uuid",...q.errToObj(a)})}nanoid(a){return this._addCheck({kind:"nanoid",...q.errToObj(a)})}cuid(a){return this._addCheck({kind:"cuid",...q.errToObj(a)})}cuid2(a){return this._addCheck({kind:"cuid2",...q.errToObj(a)})}ulid(a){return this._addCheck({kind:"ulid",...q.errToObj(a)})}base64(a){return this._addCheck({kind:"base64",...q.errToObj(a)})}base64url(a){return this._addCheck({kind:"base64url",...q.errToObj(a)})}jwt(a){return this._addCheck({kind:"jwt",...q.errToObj(a)})}ip(a){return this._addCheck({kind:"ip",...q.errToObj(a)})}cidr(a){return this._addCheck({kind:"cidr",...q.errToObj(a)})}datetime(a){return"string"==typeof a?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:a}):this._addCheck({kind:"datetime",precision:void 0===a?.precision?null:a?.precision,offset:a?.offset??!1,local:a?.local??!1,...q.errToObj(a?.message)})}date(a){return this._addCheck({kind:"date",message:a})}time(a){return"string"==typeof a?this._addCheck({kind:"time",precision:null,message:a}):this._addCheck({kind:"time",precision:void 0===a?.precision?null:a?.precision,...q.errToObj(a?.message)})}duration(a){return this._addCheck({kind:"duration",...q.errToObj(a)})}regex(a,b){return this._addCheck({kind:"regex",regex:a,...q.errToObj(b)})}includes(a,b){return this._addCheck({kind:"includes",value:a,position:b?.position,...q.errToObj(b?.message)})}startsWith(a,b){return this._addCheck({kind:"startsWith",value:a,...q.errToObj(b)})}endsWith(a,b){return this._addCheck({kind:"endsWith",value:a,...q.errToObj(b)})}min(a,b){return this._addCheck({kind:"min",value:a,...q.errToObj(b)})}max(a,b){return this._addCheck({kind:"max",value:a,...q.errToObj(b)})}length(a,b){return this._addCheck({kind:"length",value:a,...q.errToObj(b)})}nonempty(a){return this.min(1,q.errToObj(a))}trim(){return new cV({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new cV({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new cV({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(a=>"datetime"===a.kind)}get isDate(){return!!this._def.checks.find(a=>"date"===a.kind)}get isTime(){return!!this._def.checks.find(a=>"time"===a.kind)}get isDuration(){return!!this._def.checks.find(a=>"duration"===a.kind)}get isEmail(){return!!this._def.checks.find(a=>"email"===a.kind)}get isURL(){return!!this._def.checks.find(a=>"url"===a.kind)}get isEmoji(){return!!this._def.checks.find(a=>"emoji"===a.kind)}get isUUID(){return!!this._def.checks.find(a=>"uuid"===a.kind)}get isNANOID(){return!!this._def.checks.find(a=>"nanoid"===a.kind)}get isCUID(){return!!this._def.checks.find(a=>"cuid"===a.kind)}get isCUID2(){return!!this._def.checks.find(a=>"cuid2"===a.kind)}get isULID(){return!!this._def.checks.find(a=>"ulid"===a.kind)}get isIP(){return!!this._def.checks.find(a=>"ip"===a.kind)}get isCIDR(){return!!this._def.checks.find(a=>"cidr"===a.kind)}get isBase64(){return!!this._def.checks.find(a=>"base64"===a.kind)}get isBase64url(){return!!this._def.checks.find(a=>"base64url"===a.kind)}get minLength(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxLength(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}}cV.create=a=>new cV({checks:[],typeName:r.ZodString,coerce:a?.coerce??!1,...cC(a)});class cW extends cD{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(a){let b;if(this._def.coerce&&(a.data=Number(a.data)),this._getType(a)!==cl.number){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.number,received:b.parsedType}),cw}let c=new cv;for(let d of this._def.checks)"int"===d.kind?o.isInteger(a.data)||(cu(b=this._getOrReturnCtx(a,b),{code:cn.invalid_type,expected:"integer",received:"float",message:d.message}),c.dirty()):"min"===d.kind?(d.inclusive?a.data<d.value:a.data<=d.value)&&(cu(b=this._getOrReturnCtx(a,b),{code:cn.too_small,minimum:d.value,type:"number",inclusive:d.inclusive,exact:!1,message:d.message}),c.dirty()):"max"===d.kind?(d.inclusive?a.data>d.value:a.data>=d.value)&&(cu(b=this._getOrReturnCtx(a,b),{code:cn.too_big,maximum:d.value,type:"number",inclusive:d.inclusive,exact:!1,message:d.message}),c.dirty()):"multipleOf"===d.kind?0!==function(a,b){let c=(a.toString().split(".")[1]||"").length,d=(b.toString().split(".")[1]||"").length,e=c>d?c:d;return Number.parseInt(a.toFixed(e).replace(".",""))%Number.parseInt(b.toFixed(e).replace(".",""))/10**e}(a.data,d.value)&&(cu(b=this._getOrReturnCtx(a,b),{code:cn.not_multiple_of,multipleOf:d.value,message:d.message}),c.dirty()):"finite"===d.kind?Number.isFinite(a.data)||(cu(b=this._getOrReturnCtx(a,b),{code:cn.not_finite,message:d.message}),c.dirty()):o.assertNever(d);return{status:c.value,value:a.data}}gte(a,b){return this.setLimit("min",a,!0,q.toString(b))}gt(a,b){return this.setLimit("min",a,!1,q.toString(b))}lte(a,b){return this.setLimit("max",a,!0,q.toString(b))}lt(a,b){return this.setLimit("max",a,!1,q.toString(b))}setLimit(a,b,c,d){return new cW({...this._def,checks:[...this._def.checks,{kind:a,value:b,inclusive:c,message:q.toString(d)}]})}_addCheck(a){return new cW({...this._def,checks:[...this._def.checks,a]})}int(a){return this._addCheck({kind:"int",message:q.toString(a)})}positive(a){return this._addCheck({kind:"min",value:0,inclusive:!1,message:q.toString(a)})}negative(a){return this._addCheck({kind:"max",value:0,inclusive:!1,message:q.toString(a)})}nonpositive(a){return this._addCheck({kind:"max",value:0,inclusive:!0,message:q.toString(a)})}nonnegative(a){return this._addCheck({kind:"min",value:0,inclusive:!0,message:q.toString(a)})}multipleOf(a,b){return this._addCheck({kind:"multipleOf",value:a,message:q.toString(b)})}finite(a){return this._addCheck({kind:"finite",message:q.toString(a)})}safe(a){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:q.toString(a)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:q.toString(a)})}get minValue(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxValue(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}get isInt(){return!!this._def.checks.find(a=>"int"===a.kind||"multipleOf"===a.kind&&o.isInteger(a.value))}get isFinite(){let a=null,b=null;for(let c of this._def.checks)if("finite"===c.kind||"int"===c.kind||"multipleOf"===c.kind)return!0;else"min"===c.kind?(null===b||c.value>b)&&(b=c.value):"max"===c.kind&&(null===a||c.value<a)&&(a=c.value);return Number.isFinite(b)&&Number.isFinite(a)}}cW.create=a=>new cW({checks:[],typeName:r.ZodNumber,coerce:a?.coerce||!1,...cC(a)});class cX extends cD{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(a){let b;if(this._def.coerce)try{a.data=BigInt(a.data)}catch{return this._getInvalidInput(a)}if(this._getType(a)!==cl.bigint)return this._getInvalidInput(a);let c=new cv;for(let d of this._def.checks)"min"===d.kind?(d.inclusive?a.data<d.value:a.data<=d.value)&&(cu(b=this._getOrReturnCtx(a,b),{code:cn.too_small,type:"bigint",minimum:d.value,inclusive:d.inclusive,message:d.message}),c.dirty()):"max"===d.kind?(d.inclusive?a.data>d.value:a.data>=d.value)&&(cu(b=this._getOrReturnCtx(a,b),{code:cn.too_big,type:"bigint",maximum:d.value,inclusive:d.inclusive,message:d.message}),c.dirty()):"multipleOf"===d.kind?a.data%d.value!==BigInt(0)&&(cu(b=this._getOrReturnCtx(a,b),{code:cn.not_multiple_of,multipleOf:d.value,message:d.message}),c.dirty()):o.assertNever(d);return{status:c.value,value:a.data}}_getInvalidInput(a){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.bigint,received:b.parsedType}),cw}gte(a,b){return this.setLimit("min",a,!0,q.toString(b))}gt(a,b){return this.setLimit("min",a,!1,q.toString(b))}lte(a,b){return this.setLimit("max",a,!0,q.toString(b))}lt(a,b){return this.setLimit("max",a,!1,q.toString(b))}setLimit(a,b,c,d){return new cX({...this._def,checks:[...this._def.checks,{kind:a,value:b,inclusive:c,message:q.toString(d)}]})}_addCheck(a){return new cX({...this._def,checks:[...this._def.checks,a]})}positive(a){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:q.toString(a)})}negative(a){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:q.toString(a)})}nonpositive(a){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:q.toString(a)})}nonnegative(a){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:q.toString(a)})}multipleOf(a,b){return this._addCheck({kind:"multipleOf",value:a,message:q.toString(b)})}get minValue(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxValue(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}}cX.create=a=>new cX({checks:[],typeName:r.ZodBigInt,coerce:a?.coerce??!1,...cC(a)});class cY extends cD{_parse(a){if(this._def.coerce&&(a.data=!!a.data),this._getType(a)!==cl.boolean){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.boolean,received:b.parsedType}),cw}return cy(a.data)}}cY.create=a=>new cY({typeName:r.ZodBoolean,coerce:a?.coerce||!1,...cC(a)});class cZ extends cD{_parse(a){let b;if(this._def.coerce&&(a.data=new Date(a.data)),this._getType(a)!==cl.date){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.date,received:b.parsedType}),cw}if(Number.isNaN(a.data.getTime()))return cu(this._getOrReturnCtx(a),{code:cn.invalid_date}),cw;let c=new cv;for(let d of this._def.checks)"min"===d.kind?a.data.getTime()<d.value&&(cu(b=this._getOrReturnCtx(a,b),{code:cn.too_small,message:d.message,inclusive:!0,exact:!1,minimum:d.value,type:"date"}),c.dirty()):"max"===d.kind?a.data.getTime()>d.value&&(cu(b=this._getOrReturnCtx(a,b),{code:cn.too_big,message:d.message,inclusive:!0,exact:!1,maximum:d.value,type:"date"}),c.dirty()):o.assertNever(d);return{status:c.value,value:new Date(a.data.getTime())}}_addCheck(a){return new cZ({...this._def,checks:[...this._def.checks,a]})}min(a,b){return this._addCheck({kind:"min",value:a.getTime(),message:q.toString(b)})}max(a,b){return this._addCheck({kind:"max",value:a.getTime(),message:q.toString(b)})}get minDate(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return null!=a?new Date(a):null}get maxDate(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return null!=a?new Date(a):null}}cZ.create=a=>new cZ({checks:[],coerce:a?.coerce||!1,typeName:r.ZodDate,...cC(a)});class c$ extends cD{_parse(a){if(this._getType(a)!==cl.symbol){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.symbol,received:b.parsedType}),cw}return cy(a.data)}}c$.create=a=>new c$({typeName:r.ZodSymbol,...cC(a)});class c_ extends cD{_parse(a){if(this._getType(a)!==cl.undefined){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.undefined,received:b.parsedType}),cw}return cy(a.data)}}c_.create=a=>new c_({typeName:r.ZodUndefined,...cC(a)});class c0 extends cD{_parse(a){if(this._getType(a)!==cl.null){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.null,received:b.parsedType}),cw}return cy(a.data)}}c0.create=a=>new c0({typeName:r.ZodNull,...cC(a)});class c1 extends cD{constructor(){super(...arguments),this._any=!0}_parse(a){return cy(a.data)}}c1.create=a=>new c1({typeName:r.ZodAny,...cC(a)});class c2 extends cD{constructor(){super(...arguments),this._unknown=!0}_parse(a){return cy(a.data)}}c2.create=a=>new c2({typeName:r.ZodUnknown,...cC(a)});class c3 extends cD{_parse(a){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.never,received:b.parsedType}),cw}}c3.create=a=>new c3({typeName:r.ZodNever,...cC(a)});class c4 extends cD{_parse(a){if(this._getType(a)!==cl.undefined){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.void,received:b.parsedType}),cw}return cy(a.data)}}c4.create=a=>new c4({typeName:r.ZodVoid,...cC(a)});class c5 extends cD{_parse(a){let{ctx:b,status:c}=this._processInputParams(a),d=this._def;if(b.parsedType!==cl.array)return cu(b,{code:cn.invalid_type,expected:cl.array,received:b.parsedType}),cw;if(null!==d.exactLength){let a=b.data.length>d.exactLength.value,e=b.data.length<d.exactLength.value;(a||e)&&(cu(b,{code:a?cn.too_big:cn.too_small,minimum:e?d.exactLength.value:void 0,maximum:a?d.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:d.exactLength.message}),c.dirty())}if(null!==d.minLength&&b.data.length<d.minLength.value&&(cu(b,{code:cn.too_small,minimum:d.minLength.value,type:"array",inclusive:!0,exact:!1,message:d.minLength.message}),c.dirty()),null!==d.maxLength&&b.data.length>d.maxLength.value&&(cu(b,{code:cn.too_big,maximum:d.maxLength.value,type:"array",inclusive:!0,exact:!1,message:d.maxLength.message}),c.dirty()),b.common.async)return Promise.all([...b.data].map((a,c)=>d.type._parseAsync(new cA(b,a,b.path,c)))).then(a=>cv.mergeArray(c,a));let e=[...b.data].map((a,c)=>d.type._parseSync(new cA(b,a,b.path,c)));return cv.mergeArray(c,e)}get element(){return this._def.type}min(a,b){return new c5({...this._def,minLength:{value:a,message:q.toString(b)}})}max(a,b){return new c5({...this._def,maxLength:{value:a,message:q.toString(b)}})}length(a,b){return new c5({...this._def,exactLength:{value:a,message:q.toString(b)}})}nonempty(a){return this.min(1,a)}}c5.create=(a,b)=>new c5({type:a,minLength:null,maxLength:null,exactLength:null,typeName:r.ZodArray,...cC(b)});class c6 extends cD{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let a=this._def.shape(),b=o.objectKeys(a);return this._cached={shape:a,keys:b},this._cached}_parse(a){if(this._getType(a)!==cl.object){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.object,received:b.parsedType}),cw}let{status:b,ctx:c}=this._processInputParams(a),{shape:d,keys:e}=this._getCached(),f=[];if(!(this._def.catchall instanceof c3&&"strip"===this._def.unknownKeys))for(let a in c.data)e.includes(a)||f.push(a);let g=[];for(let a of e){let b=d[a],e=c.data[a];g.push({key:{status:"valid",value:a},value:b._parse(new cA(c,e,c.path,a)),alwaysSet:a in c.data})}if(this._def.catchall instanceof c3){let a=this._def.unknownKeys;if("passthrough"===a)for(let a of f)g.push({key:{status:"valid",value:a},value:{status:"valid",value:c.data[a]}});else if("strict"===a)f.length>0&&(cu(c,{code:cn.unrecognized_keys,keys:f}),b.dirty());else if("strip"===a);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let a=this._def.catchall;for(let b of f){let d=c.data[b];g.push({key:{status:"valid",value:b},value:a._parse(new cA(c,d,c.path,b)),alwaysSet:b in c.data})}}return c.common.async?Promise.resolve().then(async()=>{let a=[];for(let b of g){let c=await b.key,d=await b.value;a.push({key:c,value:d,alwaysSet:b.alwaysSet})}return a}).then(a=>cv.mergeObjectSync(b,a)):cv.mergeObjectSync(b,g)}get shape(){return this._def.shape()}strict(a){return q.errToObj,new c6({...this._def,unknownKeys:"strict",...void 0!==a?{errorMap:(b,c)=>{let d=this._def.errorMap?.(b,c).message??c.defaultError;return"unrecognized_keys"===b.code?{message:q.errToObj(a).message??d}:{message:d}}}:{}})}strip(){return new c6({...this._def,unknownKeys:"strip"})}passthrough(){return new c6({...this._def,unknownKeys:"passthrough"})}extend(a){return new c6({...this._def,shape:()=>({...this._def.shape(),...a})})}merge(a){return new c6({unknownKeys:a._def.unknownKeys,catchall:a._def.catchall,shape:()=>({...this._def.shape(),...a._def.shape()}),typeName:r.ZodObject})}setKey(a,b){return this.augment({[a]:b})}catchall(a){return new c6({...this._def,catchall:a})}pick(a){let b={};for(let c of o.objectKeys(a))a[c]&&this.shape[c]&&(b[c]=this.shape[c]);return new c6({...this._def,shape:()=>b})}omit(a){let b={};for(let c of o.objectKeys(this.shape))a[c]||(b[c]=this.shape[c]);return new c6({...this._def,shape:()=>b})}deepPartial(){return function a(b){if(b instanceof c6){let c={};for(let d in b.shape){let e=b.shape[d];c[d]=dn.create(a(e))}return new c6({...b._def,shape:()=>c})}if(b instanceof c5)return new c5({...b._def,type:a(b.element)});if(b instanceof dn)return dn.create(a(b.unwrap()));if(b instanceof dp)return dp.create(a(b.unwrap()));if(b instanceof db)return db.create(b.items.map(b=>a(b)));else return b}(this)}partial(a){let b={};for(let c of o.objectKeys(this.shape)){let d=this.shape[c];a&&!a[c]?b[c]=d:b[c]=d.optional()}return new c6({...this._def,shape:()=>b})}required(a){let b={};for(let c of o.objectKeys(this.shape))if(a&&!a[c])b[c]=this.shape[c];else{let a=this.shape[c];for(;a instanceof dn;)a=a._def.innerType;b[c]=a}return new c6({...this._def,shape:()=>b})}keyof(){return di(o.objectKeys(this.shape))}}c6.create=(a,b)=>new c6({shape:()=>a,unknownKeys:"strip",catchall:c3.create(),typeName:r.ZodObject,...cC(b)}),c6.strictCreate=(a,b)=>new c6({shape:()=>a,unknownKeys:"strict",catchall:c3.create(),typeName:r.ZodObject,...cC(b)}),c6.lazycreate=(a,b)=>new c6({shape:a,unknownKeys:"strip",catchall:c3.create(),typeName:r.ZodObject,...cC(b)});class c7 extends cD{_parse(a){let{ctx:b}=this._processInputParams(a),c=this._def.options;if(b.common.async)return Promise.all(c.map(async a=>{let c={...b,common:{...b.common,issues:[]},parent:null};return{result:await a._parseAsync({data:b.data,path:b.path,parent:c}),ctx:c}})).then(function(a){for(let b of a)if("valid"===b.result.status)return b.result;for(let c of a)if("dirty"===c.result.status)return b.common.issues.push(...c.ctx.common.issues),c.result;let c=a.map(a=>new co(a.ctx.common.issues));return cu(b,{code:cn.invalid_union,unionErrors:c}),cw});{let a,d=[];for(let e of c){let c={...b,common:{...b.common,issues:[]},parent:null},f=e._parseSync({data:b.data,path:b.path,parent:c});if("valid"===f.status)return f;"dirty"!==f.status||a||(a={result:f,ctx:c}),c.common.issues.length&&d.push(c.common.issues)}if(a)return b.common.issues.push(...a.ctx.common.issues),a.result;let e=d.map(a=>new co(a));return cu(b,{code:cn.invalid_union,unionErrors:e}),cw}}get options(){return this._def.options}}c7.create=(a,b)=>new c7({options:a,typeName:r.ZodUnion,...cC(b)});let c8=a=>{if(a instanceof dg)return c8(a.schema);if(a instanceof dm)return c8(a.innerType());if(a instanceof dh)return[a.value];if(a instanceof dj)return a.options;if(a instanceof dk)return o.objectValues(a.enum);else if(a instanceof dq)return c8(a._def.innerType);else if(a instanceof c_)return[void 0];else if(a instanceof c0)return[null];else if(a instanceof dn)return[void 0,...c8(a.unwrap())];else if(a instanceof dp)return[null,...c8(a.unwrap())];else if(a instanceof dt)return c8(a.unwrap());else if(a instanceof dv)return c8(a.unwrap());else if(a instanceof dr)return c8(a._def.innerType);else return[]};class c9 extends cD{_parse(a){let{ctx:b}=this._processInputParams(a);if(b.parsedType!==cl.object)return cu(b,{code:cn.invalid_type,expected:cl.object,received:b.parsedType}),cw;let c=this.discriminator,d=b.data[c],e=this.optionsMap.get(d);return e?b.common.async?e._parseAsync({data:b.data,path:b.path,parent:b}):e._parseSync({data:b.data,path:b.path,parent:b}):(cu(b,{code:cn.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[c]}),cw)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(a,b,c){let d=new Map;for(let c of b){let b=c8(c.shape[a]);if(!b.length)throw Error(`A discriminator value for key \`${a}\` could not be extracted from all schema options`);for(let e of b){if(d.has(e))throw Error(`Discriminator property ${String(a)} has duplicate value ${String(e)}`);d.set(e,c)}}return new c9({typeName:r.ZodDiscriminatedUnion,discriminator:a,options:b,optionsMap:d,...cC(c)})}}class da extends cD{_parse(a){let{status:b,ctx:c}=this._processInputParams(a),d=(a,d)=>{if("aborted"===a.status||"aborted"===d.status)return cw;let e=function a(b,c){let d=cm(b),e=cm(c);if(b===c)return{valid:!0,data:b};if(d===cl.object&&e===cl.object){let d=o.objectKeys(c),e=o.objectKeys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1};f[d]=e.data}return{valid:!0,data:f}}if(d===cl.array&&e===cl.array){if(b.length!==c.length)return{valid:!1};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1};d.push(f.data)}return{valid:!0,data:d}}if(d===cl.date&&e===cl.date&&+b==+c)return{valid:!0,data:b};return{valid:!1}}(a.value,d.value);return e.valid?(("dirty"===a.status||"dirty"===d.status)&&b.dirty(),{status:b.value,value:e.data}):(cu(c,{code:cn.invalid_intersection_types}),cw)};return c.common.async?Promise.all([this._def.left._parseAsync({data:c.data,path:c.path,parent:c}),this._def.right._parseAsync({data:c.data,path:c.path,parent:c})]).then(([a,b])=>d(a,b)):d(this._def.left._parseSync({data:c.data,path:c.path,parent:c}),this._def.right._parseSync({data:c.data,path:c.path,parent:c}))}}da.create=(a,b,c)=>new da({left:a,right:b,typeName:r.ZodIntersection,...cC(c)});class db extends cD{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==cl.array)return cu(c,{code:cn.invalid_type,expected:cl.array,received:c.parsedType}),cw;if(c.data.length<this._def.items.length)return cu(c,{code:cn.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),cw;!this._def.rest&&c.data.length>this._def.items.length&&(cu(c,{code:cn.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),b.dirty());let d=[...c.data].map((a,b)=>{let d=this._def.items[b]||this._def.rest;return d?d._parse(new cA(c,a,c.path,b)):null}).filter(a=>!!a);return c.common.async?Promise.all(d).then(a=>cv.mergeArray(b,a)):cv.mergeArray(b,d)}get items(){return this._def.items}rest(a){return new db({...this._def,rest:a})}}db.create=(a,b)=>{if(!Array.isArray(a))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new db({items:a,typeName:r.ZodTuple,rest:null,...cC(b)})};class dc extends cD{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==cl.object)return cu(c,{code:cn.invalid_type,expected:cl.object,received:c.parsedType}),cw;let d=[],e=this._def.keyType,f=this._def.valueType;for(let a in c.data)d.push({key:e._parse(new cA(c,a,c.path,a)),value:f._parse(new cA(c,c.data[a],c.path,a)),alwaysSet:a in c.data});return c.common.async?cv.mergeObjectAsync(b,d):cv.mergeObjectSync(b,d)}get element(){return this._def.valueType}static create(a,b,c){return new dc(b instanceof cD?{keyType:a,valueType:b,typeName:r.ZodRecord,...cC(c)}:{keyType:cV.create(),valueType:a,typeName:r.ZodRecord,...cC(b)})}}class dd extends cD{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==cl.map)return cu(c,{code:cn.invalid_type,expected:cl.map,received:c.parsedType}),cw;let d=this._def.keyType,e=this._def.valueType,f=[...c.data.entries()].map(([a,b],f)=>({key:d._parse(new cA(c,a,c.path,[f,"key"])),value:e._parse(new cA(c,b,c.path,[f,"value"]))}));if(c.common.async){let a=new Map;return Promise.resolve().then(async()=>{for(let c of f){let d=await c.key,e=await c.value;if("aborted"===d.status||"aborted"===e.status)return cw;("dirty"===d.status||"dirty"===e.status)&&b.dirty(),a.set(d.value,e.value)}return{status:b.value,value:a}})}{let a=new Map;for(let c of f){let d=c.key,e=c.value;if("aborted"===d.status||"aborted"===e.status)return cw;("dirty"===d.status||"dirty"===e.status)&&b.dirty(),a.set(d.value,e.value)}return{status:b.value,value:a}}}}dd.create=(a,b,c)=>new dd({valueType:b,keyType:a,typeName:r.ZodMap,...cC(c)});class de extends cD{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==cl.set)return cu(c,{code:cn.invalid_type,expected:cl.set,received:c.parsedType}),cw;let d=this._def;null!==d.minSize&&c.data.size<d.minSize.value&&(cu(c,{code:cn.too_small,minimum:d.minSize.value,type:"set",inclusive:!0,exact:!1,message:d.minSize.message}),b.dirty()),null!==d.maxSize&&c.data.size>d.maxSize.value&&(cu(c,{code:cn.too_big,maximum:d.maxSize.value,type:"set",inclusive:!0,exact:!1,message:d.maxSize.message}),b.dirty());let e=this._def.valueType;function f(a){let c=new Set;for(let d of a){if("aborted"===d.status)return cw;"dirty"===d.status&&b.dirty(),c.add(d.value)}return{status:b.value,value:c}}let g=[...c.data.values()].map((a,b)=>e._parse(new cA(c,a,c.path,b)));return c.common.async?Promise.all(g).then(a=>f(a)):f(g)}min(a,b){return new de({...this._def,minSize:{value:a,message:q.toString(b)}})}max(a,b){return new de({...this._def,maxSize:{value:a,message:q.toString(b)}})}size(a,b){return this.min(a,b).max(a,b)}nonempty(a){return this.min(1,a)}}de.create=(a,b)=>new de({valueType:a,minSize:null,maxSize:null,typeName:r.ZodSet,...cC(b)});class df extends cD{constructor(){super(...arguments),this.validate=this.implement}_parse(a){let{ctx:b}=this._processInputParams(a);if(b.parsedType!==cl.function)return cu(b,{code:cn.invalid_type,expected:cl.function,received:b.parsedType}),cw;function c(a,c){return ct({data:a,path:b.path,errorMaps:[b.common.contextualErrorMap,b.schemaErrorMap,cs,cs].filter(a=>!!a),issueData:{code:cn.invalid_arguments,argumentsError:c}})}function d(a,c){return ct({data:a,path:b.path,errorMaps:[b.common.contextualErrorMap,b.schemaErrorMap,cs,cs].filter(a=>!!a),issueData:{code:cn.invalid_return_type,returnTypeError:c}})}let e={errorMap:b.common.contextualErrorMap},f=b.data;if(this._def.returns instanceof dl){let a=this;return cy(async function(...b){let g=new co([]),h=await a._def.args.parseAsync(b,e).catch(a=>{throw g.addIssue(c(b,a)),g}),i=await Reflect.apply(f,this,h);return await a._def.returns._def.type.parseAsync(i,e).catch(a=>{throw g.addIssue(d(i,a)),g})})}{let a=this;return cy(function(...b){let g=a._def.args.safeParse(b,e);if(!g.success)throw new co([c(b,g.error)]);let h=Reflect.apply(f,this,g.data),i=a._def.returns.safeParse(h,e);if(!i.success)throw new co([d(h,i.error)]);return i.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...a){return new df({...this._def,args:db.create(a).rest(c2.create())})}returns(a){return new df({...this._def,returns:a})}implement(a){return this.parse(a)}strictImplement(a){return this.parse(a)}static create(a,b,c){return new df({args:a||db.create([]).rest(c2.create()),returns:b||c2.create(),typeName:r.ZodFunction,...cC(c)})}}class dg extends cD{get schema(){return this._def.getter()}_parse(a){let{ctx:b}=this._processInputParams(a);return this._def.getter()._parse({data:b.data,path:b.path,parent:b})}}dg.create=(a,b)=>new dg({getter:a,typeName:r.ZodLazy,...cC(b)});class dh extends cD{_parse(a){if(a.data!==this._def.value){let b=this._getOrReturnCtx(a);return cu(b,{received:b.data,code:cn.invalid_literal,expected:this._def.value}),cw}return{status:"valid",value:a.data}}get value(){return this._def.value}}function di(a,b){return new dj({values:a,typeName:r.ZodEnum,...cC(b)})}dh.create=(a,b)=>new dh({value:a,typeName:r.ZodLiteral,...cC(b)});class dj extends cD{_parse(a){if("string"!=typeof a.data){let b=this._getOrReturnCtx(a),c=this._def.values;return cu(b,{expected:o.joinValues(c),received:b.parsedType,code:cn.invalid_type}),cw}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(a.data)){let b=this._getOrReturnCtx(a),c=this._def.values;return cu(b,{received:b.data,code:cn.invalid_enum_value,options:c}),cw}return cy(a.data)}get options(){return this._def.values}get enum(){let a={};for(let b of this._def.values)a[b]=b;return a}get Values(){let a={};for(let b of this._def.values)a[b]=b;return a}get Enum(){let a={};for(let b of this._def.values)a[b]=b;return a}extract(a,b=this._def){return dj.create(a,{...this._def,...b})}exclude(a,b=this._def){return dj.create(this.options.filter(b=>!a.includes(b)),{...this._def,...b})}}dj.create=di;class dk extends cD{_parse(a){let b=o.getValidEnumValues(this._def.values),c=this._getOrReturnCtx(a);if(c.parsedType!==cl.string&&c.parsedType!==cl.number){let a=o.objectValues(b);return cu(c,{expected:o.joinValues(a),received:c.parsedType,code:cn.invalid_type}),cw}if(this._cache||(this._cache=new Set(o.getValidEnumValues(this._def.values))),!this._cache.has(a.data)){let a=o.objectValues(b);return cu(c,{received:c.data,code:cn.invalid_enum_value,options:a}),cw}return cy(a.data)}get enum(){return this._def.values}}dk.create=(a,b)=>new dk({values:a,typeName:r.ZodNativeEnum,...cC(b)});class dl extends cD{unwrap(){return this._def.type}_parse(a){let{ctx:b}=this._processInputParams(a);return b.parsedType!==cl.promise&&!1===b.common.async?(cu(b,{code:cn.invalid_type,expected:cl.promise,received:b.parsedType}),cw):cy((b.parsedType===cl.promise?b.data:Promise.resolve(b.data)).then(a=>this._def.type.parseAsync(a,{path:b.path,errorMap:b.common.contextualErrorMap})))}}dl.create=(a,b)=>new dl({type:a,typeName:r.ZodPromise,...cC(b)});class dm extends cD{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===r.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(a){let{status:b,ctx:c}=this._processInputParams(a),d=this._def.effect||null,e={addIssue:a=>{cu(c,a),a.fatal?b.abort():b.dirty()},get path(){return c.path}};if(e.addIssue=e.addIssue.bind(e),"preprocess"===d.type){let a=d.transform(c.data,e);if(c.common.async)return Promise.resolve(a).then(async a=>{if("aborted"===b.value)return cw;let d=await this._def.schema._parseAsync({data:a,path:c.path,parent:c});return"aborted"===d.status?cw:"dirty"===d.status||"dirty"===b.value?cx(d.value):d});{if("aborted"===b.value)return cw;let d=this._def.schema._parseSync({data:a,path:c.path,parent:c});return"aborted"===d.status?cw:"dirty"===d.status||"dirty"===b.value?cx(d.value):d}}if("refinement"===d.type){let a=a=>{let b=d.refinement(a,e);if(c.common.async)return Promise.resolve(b);if(b instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(!1!==c.common.async)return this._def.schema._parseAsync({data:c.data,path:c.path,parent:c}).then(c=>"aborted"===c.status?cw:("dirty"===c.status&&b.dirty(),a(c.value).then(()=>({status:b.value,value:c.value}))));{let d=this._def.schema._parseSync({data:c.data,path:c.path,parent:c});return"aborted"===d.status?cw:("dirty"===d.status&&b.dirty(),a(d.value),{status:b.value,value:d.value})}}if("transform"===d.type)if(!1!==c.common.async)return this._def.schema._parseAsync({data:c.data,path:c.path,parent:c}).then(a=>"valid"!==a.status?cw:Promise.resolve(d.transform(a.value,e)).then(a=>({status:b.value,value:a})));else{let a=this._def.schema._parseSync({data:c.data,path:c.path,parent:c});if("valid"!==a.status)return cw;let f=d.transform(a.value,e);if(f instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:b.value,value:f}}o.assertNever(d)}}dm.create=(a,b,c)=>new dm({schema:a,typeName:r.ZodEffects,effect:b,...cC(c)}),dm.createWithPreprocess=(a,b,c)=>new dm({schema:b,effect:{type:"preprocess",transform:a},typeName:r.ZodEffects,...cC(c)});class dn extends cD{_parse(a){return this._getType(a)===cl.undefined?cy(void 0):this._def.innerType._parse(a)}unwrap(){return this._def.innerType}}dn.create=(a,b)=>new dn({innerType:a,typeName:r.ZodOptional,...cC(b)});class dp extends cD{_parse(a){return this._getType(a)===cl.null?cy(null):this._def.innerType._parse(a)}unwrap(){return this._def.innerType}}dp.create=(a,b)=>new dp({innerType:a,typeName:r.ZodNullable,...cC(b)});class dq extends cD{_parse(a){let{ctx:b}=this._processInputParams(a),c=b.data;return b.parsedType===cl.undefined&&(c=this._def.defaultValue()),this._def.innerType._parse({data:c,path:b.path,parent:b})}removeDefault(){return this._def.innerType}}dq.create=(a,b)=>new dq({innerType:a,typeName:r.ZodDefault,defaultValue:"function"==typeof b.default?b.default:()=>b.default,...cC(b)});class dr extends cD{_parse(a){let{ctx:b}=this._processInputParams(a),c={...b,common:{...b.common,issues:[]}},d=this._def.innerType._parse({data:c.data,path:c.path,parent:{...c}});return cz(d)?d.then(a=>({status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new co(c.common.issues)},input:c.data})})):{status:"valid",value:"valid"===d.status?d.value:this._def.catchValue({get error(){return new co(c.common.issues)},input:c.data})}}removeCatch(){return this._def.innerType}}dr.create=(a,b)=>new dr({innerType:a,typeName:r.ZodCatch,catchValue:"function"==typeof b.catch?b.catch:()=>b.catch,...cC(b)});class ds extends cD{_parse(a){if(this._getType(a)!==cl.nan){let b=this._getOrReturnCtx(a);return cu(b,{code:cn.invalid_type,expected:cl.nan,received:b.parsedType}),cw}return{status:"valid",value:a.data}}}ds.create=a=>new ds({typeName:r.ZodNaN,...cC(a)}),Symbol("zod_brand");class dt extends cD{_parse(a){let{ctx:b}=this._processInputParams(a),c=b.data;return this._def.type._parse({data:c,path:b.path,parent:b})}unwrap(){return this._def.type}}class du extends cD{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.common.async)return(async()=>{let a=await this._def.in._parseAsync({data:c.data,path:c.path,parent:c});return"aborted"===a.status?cw:"dirty"===a.status?(b.dirty(),cx(a.value)):this._def.out._parseAsync({data:a.value,path:c.path,parent:c})})();{let a=this._def.in._parseSync({data:c.data,path:c.path,parent:c});return"aborted"===a.status?cw:"dirty"===a.status?(b.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:c.path,parent:c})}}static create(a,b){return new du({in:a,out:b,typeName:r.ZodPipeline})}}class dv extends cD{_parse(a){let b=this._def.innerType._parse(a),c=a=>("valid"===a.status&&(a.value=Object.freeze(a.value)),a);return cz(b)?b.then(a=>c(a)):c(b)}unwrap(){return this._def.innerType}}dv.create=(a,b)=>new dv({innerType:a,typeName:r.ZodReadonly,...cC(b)}),c6.lazycreate,function(a){a.ZodString="ZodString",a.ZodNumber="ZodNumber",a.ZodNaN="ZodNaN",a.ZodBigInt="ZodBigInt",a.ZodBoolean="ZodBoolean",a.ZodDate="ZodDate",a.ZodSymbol="ZodSymbol",a.ZodUndefined="ZodUndefined",a.ZodNull="ZodNull",a.ZodAny="ZodAny",a.ZodUnknown="ZodUnknown",a.ZodNever="ZodNever",a.ZodVoid="ZodVoid",a.ZodArray="ZodArray",a.ZodObject="ZodObject",a.ZodUnion="ZodUnion",a.ZodDiscriminatedUnion="ZodDiscriminatedUnion",a.ZodIntersection="ZodIntersection",a.ZodTuple="ZodTuple",a.ZodRecord="ZodRecord",a.ZodMap="ZodMap",a.ZodSet="ZodSet",a.ZodFunction="ZodFunction",a.ZodLazy="ZodLazy",a.ZodLiteral="ZodLiteral",a.ZodEnum="ZodEnum",a.ZodEffects="ZodEffects",a.ZodNativeEnum="ZodNativeEnum",a.ZodOptional="ZodOptional",a.ZodNullable="ZodNullable",a.ZodDefault="ZodDefault",a.ZodCatch="ZodCatch",a.ZodPromise="ZodPromise",a.ZodBranded="ZodBranded",a.ZodPipeline="ZodPipeline",a.ZodReadonly="ZodReadonly"}(r||(r={}));let dw=cV.create,dx=cW.create;ds.create,cX.create,cY.create,cZ.create,c$.create,c_.create,c0.create,c1.create,c2.create,c3.create,c4.create,c5.create;let dy=c6.create;c6.strictCreate,c7.create,c9.create,da.create,db.create,dc.create,dd.create,de.create,df.create,dg.create,dh.create;let dz=dj.create;function dA(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c}function dB(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)}dk.create,dl.create,dm.create,dn.create,dp.create,dm.createWithPreprocess,du.create;let dC=function(){let{crypto:a}=globalThis;if(a?.randomUUID)return dC=a.randomUUID.bind(a),a.randomUUID();let b=new Uint8Array(1),c=a?()=>a.getRandomValues(b)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,a=>(a^c()&15>>a/4).toString(16))};function dD(a){return"object"==typeof a&&null!==a&&("name"in a&&"AbortError"===a.name||"message"in a&&String(a.message).includes("FetchRequestCanceledException"))}let dE=a=>{if(a instanceof Error)return a;if("object"==typeof a&&null!==a){try{if("[object Error]"===Object.prototype.toString.call(a)){let b=Error(a.message,a.cause?{cause:a.cause}:{});return a.stack&&(b.stack=a.stack),a.cause&&!b.cause&&(b.cause=a.cause),a.name&&(b.name=a.name),b}}catch{}try{return Error(JSON.stringify(a))}catch{}}return Error(a)};class dF extends Error{}class dG extends dF{constructor(a,b,c,d){super(`${dG.makeMessage(a,b,c)}`),this.status=a,this.headers=d,this.requestID=d?.get("x-request-id"),this.error=b,this.code=b?.code,this.param=b?.param,this.type=b?.type}static makeMessage(a,b,c){let d=b?.message?"string"==typeof b.message?b.message:JSON.stringify(b.message):b?JSON.stringify(b):c;return a&&d?`${a} ${d}`:a?`${a} status code (no body)`:d||"(no status code or body)"}static generate(a,b,c,d){if(!a||!d)return new dI({message:c,cause:dE(b)});let e=b?.error;return 400===a?new dK(a,e,c,d):401===a?new dL(a,e,c,d):403===a?new dM(a,e,c,d):404===a?new dN(a,e,c,d):409===a?new dO(a,e,c,d):422===a?new dP(a,e,c,d):429===a?new dQ(a,e,c,d):a>=500?new dR(a,e,c,d):new dG(a,e,c,d)}}class dH extends dG{constructor({message:a}={}){super(void 0,void 0,a||"Request was aborted.",void 0)}}class dI extends dG{constructor({message:a,cause:b}){super(void 0,void 0,a||"Connection error.",void 0),b&&(this.cause=b)}}class dJ extends dI{constructor({message:a}={}){super({message:a??"Request timed out."})}}class dK extends dG{}class dL extends dG{}class dM extends dG{}class dN extends dG{}class dO extends dG{}class dP extends dG{}class dQ extends dG{}class dR extends dG{}class dS extends dF{constructor(){super("Could not parse response content as the length limit was reached")}}class dT extends dF{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class dU extends Error{constructor(a){super(a)}}let dV=/^[a-z][a-z0-9+.-]*:/i,dW=a=>(dW=Array.isArray)(a),dX=dW;function dY(a){return null!=a&&"object"==typeof a&&!Array.isArray(a)}let dZ=a=>new Promise(b=>setTimeout(b,a)),d$="5.9.0",d_=a=>"x32"===a?"x32":"x86_64"===a||"x64"===a?"x64":"arm"===a?"arm":"aarch64"===a||"arm64"===a?"arm64":a?`other:${a}`:"unknown",d0=a=>(a=a.toLowerCase()).includes("ios")?"iOS":"android"===a?"Android":"darwin"===a?"MacOS":"win32"===a?"Windows":"freebsd"===a?"FreeBSD":"openbsd"===a?"OpenBSD":"linux"===a?"Linux":a?`Other:${a}`:"Unknown";function d1(...a){let b=globalThis.ReadableStream;if(void 0===b)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new b(...a)}function d2(a){let b=Symbol.asyncIterator in a?a[Symbol.asyncIterator]():a[Symbol.iterator]();return d1({start(){},async pull(a){let{done:c,value:d}=await b.next();c?a.close():a.enqueue(d)},async cancel(){await b.return?.()}})}function d3(a){if(a[Symbol.asyncIterator])return a;let b=a.getReader();return{async next(){try{let a=await b.read();return a?.done&&b.releaseLock(),a}catch(a){throw b.releaseLock(),a}},async return(){let a=b.cancel();return b.releaseLock(),await a,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function d4(a){if(null===a||"object"!=typeof a)return;if(a[Symbol.asyncIterator])return void await a[Symbol.asyncIterator]().return?.();let b=a.getReader(),c=b.cancel();b.releaseLock(),await c}let d5=({headers:a,body:b})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(b)}),d6="RFC3986",d7=a=>String(a),d8={RFC1738:a=>String(a).replace(/%20/g,"+"),RFC3986:d7},d9=(a,b)=>(d9=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(a,b),ea=(()=>{let a=[];for(let b=0;b<256;++b)a.push("%"+((b<16?"0":"")+b.toString(16)).toUpperCase());return a})();function eb(a,b){if(dW(a)){let c=[];for(let d=0;d<a.length;d+=1)c.push(b(a[d]));return c}return b(a)}let ec={brackets:a=>String(a)+"[]",comma:"comma",indices:(a,b)=>String(a)+"["+b+"]",repeat:a=>String(a)},ed=function(a,b){Array.prototype.push.apply(a,dW(b)?b:[b])},ee={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(a,b,c,d,e)=>{if(0===a.length)return a;let f=a;if("symbol"==typeof a?f=Symbol.prototype.toString.call(a):"string"!=typeof a&&(f=String(a)),"iso-8859-1"===c)return escape(f).replace(/%u[0-9a-f]{4}/gi,function(a){return"%26%23"+parseInt(a.slice(2),16)+"%3B"});let g="";for(let a=0;a<f.length;a+=1024){let b=f.length>=1024?f.slice(a,a+1024):f,c=[];for(let a=0;a<b.length;++a){let d=b.charCodeAt(a);if(45===d||46===d||95===d||126===d||d>=48&&d<=57||d>=65&&d<=90||d>=97&&d<=122||"RFC1738"===e&&(40===d||41===d)){c[c.length]=b.charAt(a);continue}if(d<128){c[c.length]=ea[d];continue}if(d<2048){c[c.length]=ea[192|d>>6]+ea[128|63&d];continue}if(d<55296||d>=57344){c[c.length]=ea[224|d>>12]+ea[128|d>>6&63]+ea[128|63&d];continue}a+=1,d=65536+((1023&d)<<10|1023&b.charCodeAt(a)),c[c.length]=ea[240|d>>18]+ea[128|d>>12&63]+ea[128|d>>6&63]+ea[128|63&d]}g+=c.join("")}return g},encodeValuesOnly:!1,format:d6,formatter:d7,indices:!1,serializeDate:a=>(g??(g=Function.prototype.call.bind(Date.prototype.toISOString)))(a),skipNulls:!1,strictNullHandling:!1},ef={};function eg(a){let b;return(h??(h=(b=new globalThis.TextEncoder).encode.bind(b)))(a)}function eh(a){let b;return(i??(i=(b=new globalThis.TextDecoder).decode.bind(b)))(a)}class ei{constructor(){s.set(this,void 0),t.set(this,void 0),dA(this,s,new Uint8Array,"f"),dA(this,t,null,"f")}decode(a){let b;if(null==a)return[];let c=a instanceof ArrayBuffer?new Uint8Array(a):"string"==typeof a?eg(a):a;dA(this,s,function(a){let b=0;for(let c of a)b+=c.length;let c=new Uint8Array(b),d=0;for(let b of a)c.set(b,d),d+=b.length;return c}([dB(this,s,"f"),c]),"f");let d=[];for(;null!=(b=function(a,b){for(let c=b??0;c<a.length;c++){if(10===a[c])return{preceding:c,index:c+1,carriage:!1};if(13===a[c])return{preceding:c,index:c+1,carriage:!0}}return null}(dB(this,s,"f"),dB(this,t,"f")));){if(b.carriage&&null==dB(this,t,"f")){dA(this,t,b.index,"f");continue}if(null!=dB(this,t,"f")&&(b.index!==dB(this,t,"f")+1||b.carriage)){d.push(eh(dB(this,s,"f").subarray(0,dB(this,t,"f")-1))),dA(this,s,dB(this,s,"f").subarray(dB(this,t,"f")),"f"),dA(this,t,null,"f");continue}let a=null!==dB(this,t,"f")?b.preceding-1:b.preceding,c=eh(dB(this,s,"f").subarray(0,a));d.push(c),dA(this,s,dB(this,s,"f").subarray(b.index),"f"),dA(this,t,null,"f")}return d}flush(){return dB(this,s,"f").length?this.decode("\n"):[]}}s=new WeakMap,t=new WeakMap,ei.NEWLINE_CHARS=new Set(["\n","\r"]),ei.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let ej={off:0,error:200,warn:300,info:400,debug:500},ek=(a,b,c)=>{if(a){if(Object.prototype.hasOwnProperty.call(ej,a))return a;ep(c).warn(`${b} was set to ${JSON.stringify(a)}, expected one of ${JSON.stringify(Object.keys(ej))}`)}};function el(){}function em(a,b,c){return!b||ej[a]>ej[c]?el:b[a].bind(b)}let en={error:el,warn:el,info:el,debug:el},eo=new WeakMap;function ep(a){let b=a.logger,c=a.logLevel??"off";if(!b)return en;let d=eo.get(b);if(d&&d[0]===c)return d[1];let e={error:em("error",b,c),warn:em("warn",b,c),info:em("info",b,c),debug:em("debug",b,c)};return eo.set(b,[c,e]),e}let eq=a=>(a.options&&(a.options={...a.options},delete a.options.headers),a.headers&&(a.headers=Object.fromEntries((a.headers instanceof Headers?[...a.headers]:Object.entries(a.headers)).map(([a,b])=>[a,"authorization"===a.toLowerCase()||"cookie"===a.toLowerCase()||"set-cookie"===a.toLowerCase()?"***":b]))),"retryOfRequestLogID"in a&&(a.retryOfRequestLogID&&(a.retryOf=a.retryOfRequestLogID),delete a.retryOfRequestLogID),a);class er{constructor(a,b,c){this.iterator=a,u.set(this,void 0),this.controller=b,dA(this,u,c,"f")}static fromSSEResponse(a,b,c){let d=!1,e=c?ep(c):console;async function*f(){if(d)throw new dF("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let c=!1;try{for await(let d of es(a,b))if(!c){if(d.data.startsWith("[DONE]")){c=!0;continue}if(null===d.event||d.event.startsWith("response.")||d.event.startsWith("transcript.")){let b;try{b=JSON.parse(d.data)}catch(a){throw e.error("Could not parse message into JSON:",d.data),e.error("From chunk:",d.raw),a}if(b&&b.error)throw new dG(void 0,b.error,void 0,a.headers);yield b}else{let a;try{a=JSON.parse(d.data)}catch(a){throw console.error("Could not parse message into JSON:",d.data),console.error("From chunk:",d.raw),a}if("error"==d.event)throw new dG(void 0,a.error,a.message,void 0);yield{event:d.event,data:a}}}c=!0}catch(a){if(dD(a))return;throw a}finally{c||b.abort()}}return new er(f,b,c)}static fromReadableStream(a,b,c){let d=!1;async function*e(){let b=new ei;for await(let c of d3(a))for(let a of b.decode(c))yield a;for(let a of b.flush())yield a}return new er(async function*(){if(d)throw new dF("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let a=!1;try{for await(let b of e())!a&&b&&(yield JSON.parse(b));a=!0}catch(a){if(dD(a))return;throw a}finally{a||b.abort()}},b,c)}[(u=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let a=[],b=[],c=this.iterator(),d=d=>({next:()=>{if(0===d.length){let d=c.next();a.push(d),b.push(d)}return d.shift()}});return[new er(()=>d(a),this.controller,dB(this,u,"f")),new er(()=>d(b),this.controller,dB(this,u,"f"))]}toReadableStream(){let a,b=this;return d1({async start(){a=b[Symbol.asyncIterator]()},async pull(b){try{let{value:c,done:d}=await a.next();if(d)return b.close();let e=eg(JSON.stringify(c)+"\n");b.enqueue(e)}catch(a){b.error(a)}},async cancel(){await a.return?.()}})}}async function*es(a,b){if(!a.body){if(b.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new dF("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new dF("Attempted to iterate over a response with no body")}let c=new eu,d=new ei;for await(let b of et(d3(a.body)))for(let a of d.decode(b)){let b=c.decode(a);b&&(yield b)}for(let a of d.flush()){let b=c.decode(a);b&&(yield b)}}async function*et(a){let b=new Uint8Array;for await(let c of a){let a;if(null==c)continue;let d=c instanceof ArrayBuffer?new Uint8Array(c):"string"==typeof c?eg(c):c,e=new Uint8Array(b.length+d.length);for(e.set(b),e.set(d,b.length),b=e;-1!==(a=function(a){for(let b=0;b<a.length-1;b++){if(10===a[b]&&10===a[b+1]||13===a[b]&&13===a[b+1])return b+2;if(13===a[b]&&10===a[b+1]&&b+3<a.length&&13===a[b+2]&&10===a[b+3])return b+4}return -1}(b));)yield b.slice(0,a),b=b.slice(a)}b.length>0&&(yield b)}class eu{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(a){if(a.endsWith("\r")&&(a=a.substring(0,a.length-1)),!a){if(!this.event&&!this.data.length)return null;let a={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(a),a.startsWith(":"))return null;let[b,c,d]=function(a,b){let c=a.indexOf(":");return -1!==c?[a.substring(0,c),b,a.substring(c+b.length)]:[a,"",""]}(a,":");return d.startsWith(" ")&&(d=d.substring(1)),"event"===b?this.event=d:"data"===b&&this.data.push(d),null}}async function ev(a,b){let{response:c,requestLogID:d,retryOfRequestLogID:e,startTime:f}=b,g=await (async()=>{if(b.options.stream)return(ep(a).debug("response",c.status,c.url,c.headers,c.body),b.options.__streamClass)?b.options.__streamClass.fromSSEResponse(c,b.controller,a):er.fromSSEResponse(c,b.controller,a);if(204===c.status)return null;if(b.options.__binaryResponse)return c;let d=c.headers.get("content-type"),e=d?.split(";")[0]?.trim();return e?.includes("application/json")||e?.endsWith("+json")?ew(await c.json(),c):await c.text()})();return ep(a).debug(`[${d}] response parsed`,eq({retryOfRequestLogID:e,url:c.url,status:c.status,body:g,durationMs:Date.now()-f})),g}function ew(a,b){return!a||"object"!=typeof a||Array.isArray(a)?a:Object.defineProperty(a,"_request_id",{value:b.headers.get("x-request-id"),enumerable:!1})}class ex extends Promise{constructor(a,b,c=ev){super(a=>{a(null)}),this.responsePromise=b,this.parseResponse=c,v.set(this,void 0),dA(this,v,a,"f")}_thenUnwrap(a){return new ex(dB(this,v,"f"),this.responsePromise,async(b,c)=>ew(a(await this.parseResponse(b,c),c),c.response))}asResponse(){return this.responsePromise.then(a=>a.response)}async withResponse(){let[a,b]=await Promise.all([this.parse(),this.asResponse()]);return{data:a,response:b,request_id:b.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(a=>this.parseResponse(dB(this,v,"f"),a))),this.parsedPromise}then(a,b){return this.parse().then(a,b)}catch(a){return this.parse().catch(a)}finally(a){return this.parse().finally(a)}}v=new WeakMap;class ey{constructor(a,b,c,d){w.set(this,void 0),dA(this,w,a,"f"),this.options=d,this.response=b,this.body=c}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let a=this.nextPageRequestOptions();if(!a)throw new dF("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await dB(this,w,"f").requestAPIList(this.constructor,a)}async *iterPages(){let a=this;for(yield a;a.hasNextPage();)a=await a.getNextPage(),yield a}async *[(w=new WeakMap,Symbol.asyncIterator)](){for await(let a of this.iterPages())for(let b of a.getPaginatedItems())yield b}}class ez extends ex{constructor(a,b,c){super(a,b,async(a,b)=>new c(a,b.response,await ev(a,b),b.options))}async *[Symbol.asyncIterator](){for await(let a of(await this))yield a}}class eA extends ey{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.object=c.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class eB extends ey{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.has_more=c.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var a;let b=this.getPaginatedItems(),c=b[b.length-1]?.id;return c?{...this.options,query:{..."object"!=typeof(a=this.options.query)?{}:a??{},after:c}}:null}}let eC=()=>{if("undefined"==typeof File){let{process:a}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof a?.versions?.node&&20>parseInt(a.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function eD(a,b,c){return eC(),new File(a,b??"unknown_file",c)}function eE(a){return("object"==typeof a&&null!==a&&("name"in a&&a.name&&String(a.name)||"url"in a&&a.url&&String(a.url)||"filename"in a&&a.filename&&String(a.filename)||"path"in a&&a.path&&String(a.path))||"").split(/[\\/]/).pop()||void 0}let eF=a=>null!=a&&"object"==typeof a&&"function"==typeof a[Symbol.asyncIterator],eG=async(a,b)=>({...a,body:await eI(a.body,b)}),eH=new WeakMap,eI=async(a,b)=>{if(!await function(a){let b="function"==typeof a?a:a.fetch,c=eH.get(b);if(c)return c;let d=(async()=>{try{let a="Response"in b?b.Response:(await b("data:,")).constructor,c=new FormData;if(c.toString()===await new a(c).text())return!1;return!0}catch{return!0}})();return eH.set(b,d),d}(b))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let c=new FormData;return await Promise.all(Object.entries(a||{}).map(([a,b])=>eL(c,a,b))),c},eJ=a=>a instanceof Blob&&"name"in a,eK=a=>{if((a=>"object"==typeof a&&null!==a&&(a instanceof Response||eF(a)||eJ(a)))(a))return!0;if(Array.isArray(a))return a.some(eK);if(a&&"object"==typeof a){for(let b in a)if(eK(a[b]))return!0}return!1},eL=async(a,b,c)=>{if(void 0!==c){if(null==c)throw TypeError(`Received null for "${b}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof c||"number"==typeof c||"boolean"==typeof c)a.append(b,String(c));else if(c instanceof Response)a.append(b,eD([await c.blob()],eE(c)));else if(eF(c))a.append(b,eD([await new Response(d2(c)).blob()],eE(c)));else if(eJ(c))a.append(b,c,eE(c));else if(Array.isArray(c))await Promise.all(c.map(c=>eL(a,b+"[]",c)));else if("object"==typeof c)await Promise.all(Object.entries(c).map(([c,d])=>eL(a,`${b}[${c}]`,d)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${c} instead`)}},eM=a=>null!=a&&"object"==typeof a&&"number"==typeof a.size&&"string"==typeof a.type&&"function"==typeof a.text&&"function"==typeof a.slice&&"function"==typeof a.arrayBuffer;async function eN(a,b,c){let d,e;if(eC(),null!=(d=a=await a)&&"object"==typeof d&&"string"==typeof d.name&&"number"==typeof d.lastModified&&eM(d))return a instanceof File?a:eD([await a.arrayBuffer()],a.name);if(null!=(e=a)&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob){let d=await a.blob();return b||(b=new URL(a.url).pathname.split(/[\\/]/).pop()),eD(await eO(d),b,c)}let f=await eO(a);if(b||(b=eE(a)),!c?.type){let a=f.find(a=>"object"==typeof a&&"type"in a&&a.type);"string"==typeof a&&(c={...c,type:a})}return eD(f,b,c)}async function eO(a){let b=[];if("string"==typeof a||ArrayBuffer.isView(a)||a instanceof ArrayBuffer)b.push(a);else if(eM(a))b.push(a instanceof Blob?a:await a.arrayBuffer());else if(eF(a))for await(let c of a)b.push(...await eO(c));else{let b=a?.constructor?.name;throw Error(`Unexpected data type: ${typeof a}${b?`; constructor: ${b}`:""}${function(a){if("object"!=typeof a||null===a)return"";let b=Object.getOwnPropertyNames(a);return`; props: [${b.map(a=>`"${a}"`).join(", ")}]`}(a)}`)}return b}class eP{constructor(a){this._client=a}}function eQ(a){return a.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let eR=Object.freeze(Object.create(null)),eS=((a=eQ)=>function(b,...c){let d;if(1===b.length)return b[0];let e=!1,f=[],g=b.reduce((b,d,g)=>{/[?#]/.test(d)&&(e=!0);let h=c[g],i=(e?encodeURIComponent:a)(""+h);return g!==c.length&&(null==h||"object"==typeof h&&h.toString===Object.getPrototypeOf(Object.getPrototypeOf(h.hasOwnProperty??eR)??eR)?.toString)&&(i=h+"",f.push({start:b.length+d.length,length:i.length,error:`Value of type ${Object.prototype.toString.call(h).slice(8,-1)} is not a valid path parameter`})),b+d+(g===c.length?"":i)},""),h=g.split(/[?#]/,1)[0],i=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(d=i.exec(h));)f.push({start:d.index,length:d[0].length,error:`Value "${d[0]}" can't be safely passed as a path parameter`});if(f.sort((a,b)=>a.start-b.start),f.length>0){let a=0,b=f.reduce((b,c)=>{let d=" ".repeat(c.start-a),e="^".repeat(c.length);return a=c.start+c.length,b+d+e},"");throw new dF(`Path parameters result in path with invalid segments:
${f.map(a=>a.error).join("\n")}
${g}
${b}`)}return g})(eQ);class eT extends eP{list(a,b={},c){return this._client.getAPIList(eS`/chat/completions/${a}/messages`,eB,{query:b,...c})}}let eU=a=>a?.role==="assistant",eV=a=>a?.role==="tool";class eW{constructor(){x.add(this),this.controller=new AbortController,y.set(this,void 0),z.set(this,()=>{}),A.set(this,()=>{}),B.set(this,void 0),C.set(this,()=>{}),D.set(this,()=>{}),E.set(this,{}),F.set(this,!1),G.set(this,!1),H.set(this,!1),I.set(this,!1),dA(this,y,new Promise((a,b)=>{dA(this,z,a,"f"),dA(this,A,b,"f")}),"f"),dA(this,B,new Promise((a,b)=>{dA(this,C,a,"f"),dA(this,D,b,"f")}),"f"),dB(this,y,"f").catch(()=>{}),dB(this,B,"f").catch(()=>{})}_run(a){setTimeout(()=>{a().then(()=>{this._emitFinal(),this._emit("end")},dB(this,x,"m",J).bind(this))},0)}_connected(){this.ended||(dB(this,z,"f").call(this),this._emit("connect"))}get ended(){return dB(this,F,"f")}get errored(){return dB(this,G,"f")}get aborted(){return dB(this,H,"f")}abort(){this.controller.abort()}on(a,b){return(dB(this,E,"f")[a]||(dB(this,E,"f")[a]=[])).push({listener:b}),this}off(a,b){let c=dB(this,E,"f")[a];if(!c)return this;let d=c.findIndex(a=>a.listener===b);return d>=0&&c.splice(d,1),this}once(a,b){return(dB(this,E,"f")[a]||(dB(this,E,"f")[a]=[])).push({listener:b,once:!0}),this}emitted(a){return new Promise((b,c)=>{dA(this,I,!0,"f"),"error"!==a&&this.once("error",c),this.once(a,b)})}async done(){dA(this,I,!0,"f"),await dB(this,B,"f")}_emit(a,...b){if(dB(this,F,"f"))return;"end"===a&&(dA(this,F,!0,"f"),dB(this,C,"f").call(this));let c=dB(this,E,"f")[a];if(c&&(dB(this,E,"f")[a]=c.filter(a=>!a.once),c.forEach(({listener:a})=>a(...b))),"abort"===a){let a=b[0];dB(this,I,"f")||c?.length||Promise.reject(a),dB(this,A,"f").call(this,a),dB(this,D,"f").call(this,a),this._emit("end");return}if("error"===a){let a=b[0];dB(this,I,"f")||c?.length||Promise.reject(a),dB(this,A,"f").call(this,a),dB(this,D,"f").call(this,a),this._emit("end")}}_emitFinal(){}}function eX(a){return a?.$brand==="auto-parseable-response-format"}function eY(a){return a?.$brand==="auto-parseable-tool"}function eZ(a,b){let c=a.choices.map(a=>{var c,d;if("length"===a.finish_reason)throw new dS;if("content_filter"===a.finish_reason)throw new dT;return{...a,message:{...a.message,...a.message.tool_calls?{tool_calls:a.message.tool_calls?.map(a=>(function(a,b){let c=a.tools?.find(a=>a.function?.name===b.function.name);return{...b,function:{...b.function,parsed_arguments:eY(c)?c.$parseRaw(b.function.arguments):c?.function.strict?JSON.parse(b.function.arguments):null}}})(b,a))??void 0}:void 0,parsed:a.message.content&&!a.message.refusal?(c=b,d=a.message.content,c.response_format?.type!=="json_schema"?null:c.response_format?.type==="json_schema"?"$parseRaw"in c.response_format?c.response_format.$parseRaw(d):JSON.parse(d):null):null}}});return{...a,choices:c}}function e$(a){return!!eX(a.response_format)||(a.tools?.some(a=>eY(a)||"function"===a.type&&!0===a.function.strict)??!1)}y=new WeakMap,z=new WeakMap,A=new WeakMap,B=new WeakMap,C=new WeakMap,D=new WeakMap,E=new WeakMap,F=new WeakMap,G=new WeakMap,H=new WeakMap,I=new WeakMap,x=new WeakSet,J=function(a){if(dA(this,G,!0,"f"),a instanceof Error&&"AbortError"===a.name&&(a=new dH),a instanceof dH)return dA(this,H,!0,"f"),this._emit("abort",a);if(a instanceof dF)return this._emit("error",a);if(a instanceof Error){let b=new dF(a.message);return b.cause=a,this._emit("error",b)}return this._emit("error",new dF(String(a)))};class e_ extends eW{constructor(){super(...arguments),K.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(a){this._chatCompletions.push(a),this._emit("chatCompletion",a);let b=a.choices[0]?.message;return b&&this._addMessage(b),a}_addMessage(a,b=!0){if("content"in a||(a.content=null),this.messages.push(a),b){if(this._emit("message",a),eV(a)&&a.content)this._emit("functionToolCallResult",a.content);else if(eU(a)&&a.tool_calls)for(let b of a.tool_calls)"function"===b.type&&this._emit("functionToolCall",b.function)}}async finalChatCompletion(){await this.done();let a=this._chatCompletions[this._chatCompletions.length-1];if(!a)throw new dF("stream ended without producing a ChatCompletion");return a}async finalContent(){return await this.done(),dB(this,K,"m",L).call(this)}async finalMessage(){return await this.done(),dB(this,K,"m",M).call(this)}async finalFunctionToolCall(){return await this.done(),dB(this,K,"m",N).call(this)}async finalFunctionToolCallResult(){return await this.done(),dB(this,K,"m",O).call(this)}async totalUsage(){return await this.done(),dB(this,K,"m",P).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let a=this._chatCompletions[this._chatCompletions.length-1];a&&this._emit("finalChatCompletion",a);let b=dB(this,K,"m",M).call(this);b&&this._emit("finalMessage",b);let c=dB(this,K,"m",L).call(this);c&&this._emit("finalContent",c);let d=dB(this,K,"m",N).call(this);d&&this._emit("finalFunctionToolCall",d);let e=dB(this,K,"m",O).call(this);null!=e&&this._emit("finalFunctionToolCallResult",e),this._chatCompletions.some(a=>a.usage)&&this._emit("totalUsage",dB(this,K,"m",P).call(this))}async _createChatCompletion(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),dB(this,K,"m",Q).call(this,b);let e=await a.chat.completions.create({...b,stream:!1},{...c,signal:this.controller.signal});return this._connected(),this._addChatCompletion(eZ(e,b))}async _runChatCompletion(a,b,c){for(let a of b.messages)this._addMessage(a,!1);return await this._createChatCompletion(a,b,c)}async _runTools(a,b,c){let d="tool",{tool_choice:e="auto",stream:f,...g}=b,h="string"!=typeof e&&e?.function?.name,{maxChatCompletions:i=10}=c||{},j=b.tools.map(a=>{if(eY(a)){if(!a.$callback)throw new dF("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:a.$callback,name:a.function.name,description:a.function.description||"",parameters:a.function.parameters,parse:a.$parseRaw,strict:!0}}}return a}),k={};for(let a of j)"function"===a.type&&(k[a.function.name||a.function.function.name]=a.function);let l="tools"in b?j.map(a=>"function"===a.type?{type:"function",function:{name:a.function.name||a.function.function.name,parameters:a.function.parameters,description:a.function.description,strict:a.function.strict}}:a):void 0;for(let a of b.messages)this._addMessage(a,!1);for(let b=0;b<i;++b){let b=await this._createChatCompletion(a,{...g,tool_choice:e,tools:l,messages:[...this.messages]},c),f=b.choices[0]?.message;if(!f)throw new dF("missing message in ChatCompletion response");if(!f.tool_calls?.length)break;for(let a of f.tool_calls){let b;if("function"!==a.type)continue;let c=a.id,{name:e,arguments:f}=a.function,g=k[e];if(g){if(h&&h!==e){let a=`Invalid tool_call: ${JSON.stringify(e)}. ${JSON.stringify(h)} requested. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}}else{let a=`Invalid tool_call: ${JSON.stringify(e)}. Available options are: ${Object.keys(k).map(a=>JSON.stringify(a)).join(", ")}. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}try{b="function"==typeof g.parse?await g.parse(f):f}catch(b){let a=b instanceof Error?b.message:String(b);this._addMessage({role:d,tool_call_id:c,content:a});continue}let i=await g.function(b,this),j=dB(this,K,"m",R).call(this,i);if(this._addMessage({role:d,tool_call_id:c,content:j}),h)return}}}}K=new WeakSet,L=function(){return dB(this,K,"m",M).call(this).content??null},M=function(){let a=this.messages.length;for(;a-- >0;){let b=this.messages[a];if(eU(b))return{...b,content:b.content??null,refusal:b.refusal??null}}throw new dF("stream ended without producing a ChatCompletionMessage with role=assistant")},N=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(eU(b)&&b?.tool_calls?.length)return b.tool_calls.at(-1)?.function}},O=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(eV(b)&&null!=b.content&&"string"==typeof b.content&&this.messages.some(a=>"assistant"===a.role&&a.tool_calls?.some(a=>"function"===a.type&&a.id===b.tool_call_id)))return b.content}},P=function(){let a={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:b}of this._chatCompletions)b&&(a.completion_tokens+=b.completion_tokens,a.prompt_tokens+=b.prompt_tokens,a.total_tokens+=b.total_tokens);return a},Q=function(a){if(null!=a.n&&a.n>1)throw new dF("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},R=function(a){return"string"==typeof a?a:void 0===a?"undefined":JSON.stringify(a)};class e0 extends e_{static runTools(a,b,c){let d=new e0,e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}_addMessage(a,b=!0){super._addMessage(a,b),eU(a)&&a.content&&this._emit("content",a.content)}}let e1={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,ALL:511};class e2 extends Error{}class e3 extends Error{}let e4=a=>(function(a,b=e1.ALL){if("string"!=typeof a)throw TypeError(`expecting str, got ${typeof a}`);if(!a.trim())throw Error(`${a} is empty`);return((a,b)=>{let c=a.length,d=0,e=a=>{throw new e2(`${a} at position ${d}`)},f=a=>{throw new e3(`${a} at position ${d}`)},g=()=>(l(),d>=c&&e("Unexpected end of input"),'"'===a[d])?h():"{"===a[d]?i():"["===a[d]?j():"null"===a.substring(d,d+4)||e1.NULL&b&&c-d<4&&"null".startsWith(a.substring(d))?(d+=4,null):"true"===a.substring(d,d+4)||e1.BOOL&b&&c-d<4&&"true".startsWith(a.substring(d))?(d+=4,!0):"false"===a.substring(d,d+5)||e1.BOOL&b&&c-d<5&&"false".startsWith(a.substring(d))?(d+=5,!1):"Infinity"===a.substring(d,d+8)||e1.INFINITY&b&&c-d<8&&"Infinity".startsWith(a.substring(d))?(d+=8,1/0):"-Infinity"===a.substring(d,d+9)||e1.MINUS_INFINITY&b&&1<c-d&&c-d<9&&"-Infinity".startsWith(a.substring(d))?(d+=9,-1/0):"NaN"===a.substring(d,d+3)||e1.NAN&b&&c-d<3&&"NaN".startsWith(a.substring(d))?(d+=3,NaN):k(),h=()=>{let g=d,h=!1;for(d++;d<c&&('"'!==a[d]||h&&"\\"===a[d-1]);)h="\\"===a[d]&&!h,d++;if('"'==a.charAt(d))try{return JSON.parse(a.substring(g,++d-Number(h)))}catch(a){f(String(a))}else if(e1.STR&b)try{return JSON.parse(a.substring(g,d-Number(h))+'"')}catch(b){return JSON.parse(a.substring(g,a.lastIndexOf("\\"))+'"')}e("Unterminated string literal")},i=()=>{d++,l();let f={};try{for(;"}"!==a[d];){if(l(),d>=c&&e1.OBJ&b)return f;let e=h();l(),d++;try{let a=g();Object.defineProperty(f,e,{value:a,writable:!0,enumerable:!0,configurable:!0})}catch(a){if(e1.OBJ&b)return f;throw a}l(),","===a[d]&&d++}}catch(a){if(e1.OBJ&b)return f;e("Expected '}' at end of object")}return d++,f},j=()=>{d++;let c=[];try{for(;"]"!==a[d];)c.push(g()),l(),","===a[d]&&d++}catch(a){if(e1.ARR&b)return c;e("Expected ']' at end of array")}return d++,c},k=()=>{if(0===d){"-"===a&&e1.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a)}catch(c){if(e1.NUM&b)try{if("."===a[a.length-1])return JSON.parse(a.substring(0,a.lastIndexOf(".")));return JSON.parse(a.substring(0,a.lastIndexOf("e")))}catch(a){}f(String(c))}}let g=d;for("-"===a[d]&&d++;a[d]&&!",]}".includes(a[d]);)d++;d!=c||e1.NUM&b||e("Unterminated number literal");try{return JSON.parse(a.substring(g,d))}catch(c){"-"===a.substring(g,d)&&e1.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a.substring(g,a.lastIndexOf("e")))}catch(a){f(String(a))}}},l=()=>{for(;d<c&&" \n\r	".includes(a[d]);)d++};return g()})(a.trim(),b)})(a,e1.ALL^e1.NUM);class e5 extends e_{constructor(a){super(),S.add(this),T.set(this,void 0),U.set(this,void 0),V.set(this,void 0),dA(this,T,a,"f"),dA(this,U,[],"f")}get currentChatCompletionSnapshot(){return dB(this,V,"f")}static fromReadableStream(a){let b=new e5(null);return b._run(()=>b._fromReadableStream(a)),b}static createChatCompletion(a,b,c){let d=new e5(b);return d._run(()=>d._runChatCompletion(a,{...b,stream:!0},{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createChatCompletion(a,b,c){super._createChatCompletion;let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),dB(this,S,"m",W).call(this);let e=await a.chat.completions.create({...b,stream:!0},{...c,signal:this.controller.signal});for await(let a of(this._connected(),e))dB(this,S,"m",Y).call(this,a);if(e.controller.signal?.aborted)throw new dH;return this._addChatCompletion(dB(this,S,"m",_).call(this))}async _fromReadableStream(a,b){let c,d=b?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),dB(this,S,"m",W).call(this),this._connected();let e=er.fromReadableStream(a,this.controller);for await(let a of e)c&&c!==a.id&&this._addChatCompletion(dB(this,S,"m",_).call(this)),dB(this,S,"m",Y).call(this,a),c=a.id;if(e.controller.signal?.aborted)throw new dH;return this._addChatCompletion(dB(this,S,"m",_).call(this))}[(T=new WeakMap,U=new WeakMap,V=new WeakMap,S=new WeakSet,W=function(){this.ended||dA(this,V,void 0,"f")},X=function(a){let b=dB(this,U,"f")[a.index];return b||(b={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},dB(this,U,"f")[a.index]=b),b},Y=function(a){if(this.ended)return;let b=dB(this,S,"m",ab).call(this,a);for(let c of(this._emit("chunk",a,b),a.choices)){let a=b.choices[c.index];null!=c.delta.content&&a.message?.role==="assistant"&&a.message?.content&&(this._emit("content",c.delta.content,a.message.content),this._emit("content.delta",{delta:c.delta.content,snapshot:a.message.content,parsed:a.message.parsed})),null!=c.delta.refusal&&a.message?.role==="assistant"&&a.message?.refusal&&this._emit("refusal.delta",{delta:c.delta.refusal,snapshot:a.message.refusal}),c.logprobs?.content!=null&&a.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:c.logprobs?.content,snapshot:a.logprobs?.content??[]}),c.logprobs?.refusal!=null&&a.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:c.logprobs?.refusal,snapshot:a.logprobs?.refusal??[]});let d=dB(this,S,"m",X).call(this,a);for(let b of(a.finish_reason&&(dB(this,S,"m",$).call(this,a),null!=d.current_tool_call_index&&dB(this,S,"m",Z).call(this,a,d.current_tool_call_index)),c.delta.tool_calls??[]))d.current_tool_call_index!==b.index&&(dB(this,S,"m",$).call(this,a),null!=d.current_tool_call_index&&dB(this,S,"m",Z).call(this,a,d.current_tool_call_index)),d.current_tool_call_index=b.index;for(let b of c.delta.tool_calls??[]){let c=a.message.tool_calls?.[b.index];c?.type&&(c?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:c.function?.name,index:b.index,arguments:c.function.arguments,parsed_arguments:c.function.parsed_arguments,arguments_delta:b.function?.arguments??""}):c?.type)}}},Z=function(a,b){if(dB(this,S,"m",X).call(this,a).done_tool_calls.has(b))return;let c=a.message.tool_calls?.[b];if(!c)throw Error("no tool call snapshot");if(!c.type)throw Error("tool call snapshot missing `type`");if("function"===c.type){let a=dB(this,T,"f")?.tools?.find(a=>"function"===a.type&&a.function.name===c.function.name);this._emit("tool_calls.function.arguments.done",{name:c.function.name,index:b,arguments:c.function.arguments,parsed_arguments:eY(a)?a.$parseRaw(c.function.arguments):a?.function.strict?JSON.parse(c.function.arguments):null})}else c.type},$=function(a){let b=dB(this,S,"m",X).call(this,a);if(a.message.content&&!b.content_done){b.content_done=!0;let c=dB(this,S,"m",aa).call(this);this._emit("content.done",{content:a.message.content,parsed:c?c.$parseRaw(a.message.content):null})}a.message.refusal&&!b.refusal_done&&(b.refusal_done=!0,this._emit("refusal.done",{refusal:a.message.refusal})),a.logprobs?.content&&!b.logprobs_content_done&&(b.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:a.logprobs.content})),a.logprobs?.refusal&&!b.logprobs_refusal_done&&(b.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:a.logprobs.refusal}))},_=function(){if(this.ended)throw new dF("stream has ended, this shouldn't happen");let a=dB(this,V,"f");if(!a)throw new dF("request ended without sending any chunks");return dA(this,V,void 0,"f"),dA(this,U,[],"f"),function(a,b){var c;let{id:d,choices:e,created:f,model:g,system_fingerprint:h,...i}=a;return c={...i,id:d,choices:e.map(({message:b,finish_reason:c,index:d,logprobs:e,...f})=>{if(!c)throw new dF(`missing finish_reason for choice ${d}`);let{content:g=null,function_call:h,tool_calls:i,...j}=b,k=b.role;if(!k)throw new dF(`missing role for choice ${d}`);if(h){let{arguments:a,name:i}=h;if(null==a)throw new dF(`missing function_call.arguments for choice ${d}`);if(!i)throw new dF(`missing function_call.name for choice ${d}`);return{...f,message:{content:g,function_call:{arguments:a,name:i},role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}return i?{...f,index:d,finish_reason:c,logprobs:e,message:{...j,role:k,content:g,refusal:b.refusal??null,tool_calls:i.map((b,c)=>{let{function:e,type:f,id:g,...h}=b,{arguments:i,name:j,...k}=e||{};if(null==g)throw new dF(`missing choices[${d}].tool_calls[${c}].id
${e6(a)}`);if(null==f)throw new dF(`missing choices[${d}].tool_calls[${c}].type
${e6(a)}`);if(null==j)throw new dF(`missing choices[${d}].tool_calls[${c}].function.name
${e6(a)}`);if(null==i)throw new dF(`missing choices[${d}].tool_calls[${c}].function.arguments
${e6(a)}`);return{...h,id:g,type:f,function:{...k,name:j,arguments:i}}})}}:{...f,message:{...j,content:g,role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}),created:f,model:g,object:"chat.completion",...h?{system_fingerprint:h}:{}},b&&e$(b)?eZ(c,b):{...c,choices:c.choices.map(a=>({...a,message:{...a.message,parsed:null,...a.message.tool_calls?{tool_calls:a.message.tool_calls}:void 0}}))}}(a,dB(this,T,"f"))},aa=function(){let a=dB(this,T,"f")?.response_format;return eX(a)?a:null},ab=function(a){var b,c,d,e;let f=dB(this,V,"f"),{choices:g,...h}=a;for(let{delta:g,finish_reason:i,index:j,logprobs:k=null,...l}of(f?Object.assign(f,h):f=dA(this,V,{...h,choices:[]},"f"),a.choices)){let a=f.choices[j];if(a||(a=f.choices[j]={finish_reason:i,index:j,message:{},logprobs:k,...l}),k)if(a.logprobs){let{content:d,refusal:e,...f}=k;Object.assign(a.logprobs,f),d&&((b=a.logprobs).content??(b.content=[]),a.logprobs.content.push(...d)),e&&((c=a.logprobs).refusal??(c.refusal=[]),a.logprobs.refusal.push(...e))}else a.logprobs=Object.assign({},k);if(i&&(a.finish_reason=i,dB(this,T,"f")&&e$(dB(this,T,"f")))){if("length"===i)throw new dS;if("content_filter"===i)throw new dT}if(Object.assign(a,l),!g)continue;let{content:h,refusal:m,function_call:n,role:o,tool_calls:p,...q}=g;if(Object.assign(a.message,q),m&&(a.message.refusal=(a.message.refusal||"")+m),o&&(a.message.role=o),n&&(a.message.function_call?(n.name&&(a.message.function_call.name=n.name),n.arguments&&((d=a.message.function_call).arguments??(d.arguments=""),a.message.function_call.arguments+=n.arguments)):a.message.function_call=n),h&&(a.message.content=(a.message.content||"")+h,!a.message.refusal&&dB(this,S,"m",aa).call(this)&&(a.message.parsed=e4(a.message.content))),p)for(let{index:b,id:c,type:d,function:f,...g}of(a.message.tool_calls||(a.message.tool_calls=[]),p)){let h=(e=a.message.tool_calls)[b]??(e[b]={});Object.assign(h,g),c&&(h.id=c),d&&(h.type=d),f&&(h.function??(h.function={name:f.name??"",arguments:""})),f?.name&&(h.function.name=f.name),f?.arguments&&(h.function.arguments+=f.arguments,function(a,b){if(!a)return!1;let c=a.tools?.find(a=>a.function?.name===b.function.name);return eY(c)||c?.function.strict||!1}(dB(this,T,"f"),h)&&(h.function.parsed_arguments=e4(h.function.arguments)))}}return f},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("chunk",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new er(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function e6(a){return JSON.stringify(a)}class e7 extends e5{static fromReadableStream(a){let b=new e7(null);return b._run(()=>b._fromReadableStream(a)),b}static runTools(a,b,c){let d=new e7(b),e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}}class e8 extends eP{constructor(){super(...arguments),this.messages=new eT(this._client)}create(a,b){return this._client.post("/chat/completions",{body:a,...b,stream:a.stream??!1})}retrieve(a,b){return this._client.get(eS`/chat/completions/${a}`,b)}update(a,b,c){return this._client.post(eS`/chat/completions/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/chat/completions",eB,{query:a,...b})}delete(a,b){return this._client.delete(eS`/chat/completions/${a}`,b)}parse(a,b){for(let b of a.tools??[]){if("function"!==b.type)throw new dF(`Currently only \`function\` tool types support auto-parsing; Received \`${b.type}\``);if(!0!==b.function.strict)throw new dF(`The \`${b.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(a,{...b,headers:{...b?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(b=>eZ(b,a))}runTools(a,b){return a.stream?e7.runTools(this._client,a,b):e0.runTools(this._client,a,b)}stream(a,b){return e5.createChatCompletion(this._client,a,b)}}e8.Messages=eT;class e9 extends eP{constructor(){super(...arguments),this.completions=new e8(this._client)}}e9.Completions=e8;let fa=Symbol("brand.privateNullableHeaders"),fb=a=>{let b=new Headers,c=new Set;for(let d of a){let a=new Set;for(let[e,f]of function*(a){let b;if(!a)return;if(fa in a){let{values:b,nulls:c}=a;for(let a of(yield*b.entries(),c))yield[a,null];return}let c=!1;for(let d of(a instanceof Headers?b=a.entries():dX(a)?b=a:(c=!0,b=Object.entries(a??{})),b)){let a=d[0];if("string"!=typeof a)throw TypeError("expected header name to be a string");let b=dX(d[1])?d[1]:[d[1]],e=!1;for(let d of b)void 0!==d&&(c&&!e&&(e=!0,yield[a,null]),yield[a,d])}}(d)){let d=e.toLowerCase();a.has(d)||(b.delete(e),a.add(d)),null===f?(b.delete(e),c.add(d)):(b.append(e,f),c.delete(d))}}return{[fa]:!0,values:b,nulls:c}};class fc extends eP{create(a,b){return this._client.post("/audio/speech",{body:a,...b,headers:fb([{Accept:"application/octet-stream"},b?.headers]),__binaryResponse:!0})}}class fd extends eP{create(a,b){return this._client.post("/audio/transcriptions",eG({body:a,...b,stream:a.stream??!1,__metadata:{model:a.model}},this._client))}}class fe extends eP{create(a,b){return this._client.post("/audio/translations",eG({body:a,...b,__metadata:{model:a.model}},this._client))}}class ff extends eP{constructor(){super(...arguments),this.transcriptions=new fd(this._client),this.translations=new fe(this._client),this.speech=new fc(this._client)}}ff.Transcriptions=fd,ff.Translations=fe,ff.Speech=fc;class fg extends eP{create(a,b){return this._client.post("/batches",{body:a,...b})}retrieve(a,b){return this._client.get(eS`/batches/${a}`,b)}list(a={},b){return this._client.getAPIList("/batches",eB,{query:a,...b})}cancel(a,b){return this._client.post(eS`/batches/${a}/cancel`,b)}}class fh extends eP{create(a,b){return this._client.post("/assistants",{body:a,...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(eS`/assistants/${a}`,{...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(eS`/assistants/${a}`,{body:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/assistants",eB,{query:a,...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(eS`/assistants/${a}`,{...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class fi extends eP{create(a,b){return this._client.post("/realtime/sessions",{body:a,...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class fj extends eP{create(a,b){return this._client.post("/realtime/transcription_sessions",{body:a,...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class fk extends eP{constructor(){super(...arguments),this.sessions=new fi(this._client),this.transcriptionSessions=new fj(this._client)}}fk.Sessions=fi,fk.TranscriptionSessions=fj;class fl extends eP{create(a,b,c){return this._client.post(eS`/threads/${a}/messages`,{body:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(eS`/threads/${d}/messages/${a}`,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(eS`/threads/${d}/messages/${a}`,{body:e,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(eS`/threads/${a}/messages`,eB,{query:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{thread_id:d}=b;return this._client.delete(eS`/threads/${d}/messages/${a}`,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class fm extends eP{retrieve(a,b,c){let{thread_id:d,run_id:e,...f}=b;return this._client.get(eS`/threads/${d}/runs/${e}/steps/${a}`,{query:f,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b,c){let{thread_id:d,...e}=b;return this._client.getAPIList(eS`/threads/${d}/runs/${a}/steps`,eB,{query:e,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}let fn=a=>void 0!==globalThis.process?globalThis.process.env?.[a]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(a)?.trim():void 0;class fo extends eW{constructor(){super(...arguments),ac.add(this),ae.set(this,[]),af.set(this,{}),ag.set(this,{}),ah.set(this,void 0),ai.set(this,void 0),aj.set(this,void 0),ak.set(this,void 0),al.set(this,void 0),am.set(this,void 0),an.set(this,void 0),ao.set(this,void 0),ap.set(this,void 0)}[(ae=new WeakMap,af=new WeakMap,ag=new WeakMap,ah=new WeakMap,ai=new WeakMap,aj=new WeakMap,ak=new WeakMap,al=new WeakMap,am=new WeakMap,an=new WeakMap,ao=new WeakMap,ap=new WeakMap,ac=new WeakSet,Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(a){let b=new ad;return b._run(()=>b._fromReadableStream(a)),b}async _fromReadableStream(a,b){let c=b?.signal;c&&(c.aborted&&this.controller.abort(),c.addEventListener("abort",()=>this.controller.abort())),this._connected();let d=er.fromReadableStream(a,this.controller);for await(let a of d)dB(this,ac,"m",aq).call(this,a);if(d.controller.signal?.aborted)throw new dH;return this._addRun(dB(this,ac,"m",ar).call(this))}toReadableStream(){return new er(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(a,b,c,d){let e=new ad;return e._run(()=>e._runToolAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}async _createToolAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.submitToolOutputs(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))dB(this,ac,"m",aq).call(this,a);if(g.controller.signal?.aborted)throw new dH;return this._addRun(dB(this,ac,"m",ar).call(this))}static createThreadAssistantStream(a,b,c){let d=new ad;return d._run(()=>d._threadAssistantStream(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}static createAssistantStream(a,b,c,d){let e=new ad;return e._run(()=>e._runAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}currentEvent(){return dB(this,an,"f")}currentRun(){return dB(this,ao,"f")}currentMessageSnapshot(){return dB(this,ah,"f")}currentRunStepSnapshot(){return dB(this,ap,"f")}async finalRunSteps(){return await this.done(),Object.values(dB(this,af,"f"))}async finalMessages(){return await this.done(),Object.values(dB(this,ag,"f"))}async finalRun(){if(await this.done(),!dB(this,ai,"f"))throw Error("Final run was not received.");return dB(this,ai,"f")}async _createThreadAssistantStream(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort()));let e={...b,stream:!0},f=await a.createAndRun(e,{...c,signal:this.controller.signal});for await(let a of(this._connected(),f))dB(this,ac,"m",aq).call(this,a);if(f.controller.signal?.aborted)throw new dH;return this._addRun(dB(this,ac,"m",ar).call(this))}async _createAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.create(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))dB(this,ac,"m",aq).call(this,a);if(g.controller.signal?.aborted)throw new dH;return this._addRun(dB(this,ac,"m",ar).call(this))}static accumulateDelta(a,b){for(let[c,d]of Object.entries(b)){if(!a.hasOwnProperty(c)){a[c]=d;continue}let b=a[c];if(null==b||"index"===c||"type"===c){a[c]=d;continue}if("string"==typeof b&&"string"==typeof d)b+=d;else if("number"==typeof b&&"number"==typeof d)b+=d;else if(dY(b)&&dY(d))b=this.accumulateDelta(b,d);else if(Array.isArray(b)&&Array.isArray(d)){if(b.every(a=>"string"==typeof a||"number"==typeof a)){b.push(...d);continue}for(let a of d){if(!dY(a))throw Error(`Expected array delta entry to be an object but got: ${a}`);let c=a.index;if(null==c)throw console.error(a),Error("Expected array delta entry to have an `index` property");if("number"!=typeof c)throw Error(`Expected array delta entry \`index\` property to be a number but got ${c}`);let d=b[c];null==d?b.push(a):b[c]=this.accumulateDelta(d,a)}continue}else throw Error(`Unhandled record type: ${c}, deltaValue: ${d}, accValue: ${b}`);a[c]=b}return a}_addRun(a){return a}async _threadAssistantStream(a,b,c){return await this._createThreadAssistantStream(b,a,c)}async _runAssistantStream(a,b,c,d){return await this._createAssistantStream(b,a,c,d)}async _runToolAssistantStream(a,b,c,d){return await this._createToolAssistantStream(b,a,c,d)}}ad=fo,aq=function(a){if(!this.ended)switch(dA(this,an,a,"f"),dB(this,ac,"m",au).call(this,a),a.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":dB(this,ac,"m",ay).call(this,a);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":dB(this,ac,"m",at).call(this,a);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":dB(this,ac,"m",as).call(this,a);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ar=function(){if(this.ended)throw new dF("stream has ended, this shouldn't happen");if(!dB(this,ai,"f"))throw Error("Final run has not been received");return dB(this,ai,"f")},as=function(a){let[b,c]=dB(this,ac,"m",aw).call(this,a,dB(this,ah,"f"));for(let a of(dA(this,ah,b,"f"),dB(this,ag,"f")[b.id]=b,c)){let c=b.content[a.index];c?.type=="text"&&this._emit("textCreated",c.text)}switch(a.event){case"thread.message.created":this._emit("messageCreated",a.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",a.data.delta,b),a.data.delta.content)for(let c of a.data.delta.content){if("text"==c.type&&c.text){let a=c.text,d=b.content[c.index];if(d&&"text"==d.type)this._emit("textDelta",a,d.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(c.index!=dB(this,aj,"f")){if(dB(this,ak,"f"))switch(dB(this,ak,"f").type){case"text":this._emit("textDone",dB(this,ak,"f").text,dB(this,ah,"f"));break;case"image_file":this._emit("imageFileDone",dB(this,ak,"f").image_file,dB(this,ah,"f"))}dA(this,aj,c.index,"f")}dA(this,ak,b.content[c.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==dB(this,aj,"f")){let b=a.data.content[dB(this,aj,"f")];if(b)switch(b.type){case"image_file":this._emit("imageFileDone",b.image_file,dB(this,ah,"f"));break;case"text":this._emit("textDone",b.text,dB(this,ah,"f"))}}dB(this,ah,"f")&&this._emit("messageDone",a.data),dA(this,ah,void 0,"f")}},at=function(a){let b=dB(this,ac,"m",av).call(this,a);switch(dA(this,ap,b,"f"),a.event){case"thread.run.step.created":this._emit("runStepCreated",a.data);break;case"thread.run.step.delta":let c=a.data.delta;if(c.step_details&&"tool_calls"==c.step_details.type&&c.step_details.tool_calls&&"tool_calls"==b.step_details.type)for(let a of c.step_details.tool_calls)a.index==dB(this,al,"f")?this._emit("toolCallDelta",a,b.step_details.tool_calls[a.index]):(dB(this,am,"f")&&this._emit("toolCallDone",dB(this,am,"f")),dA(this,al,a.index,"f"),dA(this,am,b.step_details.tool_calls[a.index],"f"),dB(this,am,"f")&&this._emit("toolCallCreated",dB(this,am,"f")));this._emit("runStepDelta",a.data.delta,b);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":dA(this,ap,void 0,"f"),"tool_calls"==a.data.step_details.type&&dB(this,am,"f")&&(this._emit("toolCallDone",dB(this,am,"f")),dA(this,am,void 0,"f")),this._emit("runStepDone",a.data,b)}},au=function(a){dB(this,ae,"f").push(a),this._emit("event",a)},av=function(a){switch(a.event){case"thread.run.step.created":return dB(this,af,"f")[a.data.id]=a.data,a.data;case"thread.run.step.delta":let b=dB(this,af,"f")[a.data.id];if(!b)throw Error("Received a RunStepDelta before creation of a snapshot");let c=a.data;if(c.delta){let d=ad.accumulateDelta(b,c.delta);dB(this,af,"f")[a.data.id]=d}return dB(this,af,"f")[a.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":dB(this,af,"f")[a.data.id]=a.data}if(dB(this,af,"f")[a.data.id])return dB(this,af,"f")[a.data.id];throw Error("No snapshot available")},aw=function(a,b){let c=[];switch(a.event){case"thread.message.created":return[a.data,c];case"thread.message.delta":if(!b)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let d=a.data;if(d.delta.content)for(let a of d.delta.content)if(a.index in b.content){let c=b.content[a.index];b.content[a.index]=dB(this,ac,"m",ax).call(this,a,c)}else b.content[a.index]=a,c.push(a);return[b,c];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(b)return[b,c];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},ax=function(a,b){return ad.accumulateDelta(b,a)},ay=function(a){switch(dA(this,ao,a.data,"f"),a.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":dA(this,ai,a.data,"f"),dB(this,am,"f")&&(this._emit("toolCallDone",dB(this,am,"f")),dA(this,am,void 0,"f"))}};class fp extends eP{constructor(){super(...arguments),this.steps=new fm(this._client)}create(a,b,c){let{include:d,...e}=b;return this._client.post(eS`/threads/${a}/runs`,{query:{include:d},body:e,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(eS`/threads/${d}/runs/${a}`,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(eS`/threads/${d}/runs/${a}`,{body:e,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(eS`/threads/${a}/runs`,eB,{query:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{thread_id:d}=b;return this._client.post(eS`/threads/${d}/runs/${a}/cancel`,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(d.id,{thread_id:a},c)}createAndStream(a,b,c){return fo.createAssistantStream(a,this._client.beta.threads.runs,b,c)}async poll(a,b,c){let d=fb([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(a,b,{...c,headers:{...c?.headers,...d}}).withResponse();switch(e.status){case"queued":case"in_progress":case"cancelling":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await dZ(g);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return e}}}stream(a,b,c){return fo.createAssistantStream(a,this._client.beta.threads.runs,b,c)}submitToolOutputs(a,b,c){let{thread_id:d,...e}=b;return this._client.post(eS`/threads/${d}/runs/${a}/submit_tool_outputs`,{body:e,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}async submitToolOutputsAndPoll(a,b,c){let d=await this.submitToolOutputs(a,b,c);return await this.poll(d.id,b,c)}submitToolOutputsStream(a,b,c){return fo.createToolAssistantStream(a,this._client.beta.threads.runs,b,c)}}fp.Steps=fm;class fq extends eP{constructor(){super(...arguments),this.runs=new fp(this._client),this.messages=new fl(this._client)}create(a={},b){return this._client.post("/threads",{body:a,...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(eS`/threads/${a}`,{...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(eS`/threads/${a}`,{body:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b){return this._client.delete(eS`/threads/${a}`,{...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}createAndRun(a,b){return this._client.post("/threads/runs",{body:a,...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers]),stream:a.stream??!1})}async createAndRunPoll(a,b){let c=await this.createAndRun(a,b);return await this.runs.poll(c.id,{thread_id:c.thread_id},b)}createAndRunStream(a,b){return fo.createThreadAssistantStream(a,this._client.beta.threads,b)}}fq.Runs=fp,fq.Messages=fl;class fr extends eP{constructor(){super(...arguments),this.realtime=new fk(this._client),this.assistants=new fh(this._client),this.threads=new fq(this._client)}}fr.Realtime=fk,fr.Assistants=fh,fr.Threads=fq;class fs extends eP{create(a,b){return this._client.post("/completions",{body:a,...b,stream:a.stream??!1})}}class ft extends eP{retrieve(a,b,c){let{container_id:d}=b;return this._client.get(eS`/containers/${d}/files/${a}/content`,{...c,headers:fb([{Accept:"application/binary"},c?.headers]),__binaryResponse:!0})}}class fu extends eP{constructor(){super(...arguments),this.content=new ft(this._client)}create(a,b,c){return this._client.post(eS`/containers/${a}/files`,eG({body:b,...c},this._client))}retrieve(a,b,c){let{container_id:d}=b;return this._client.get(eS`/containers/${d}/files/${a}`,c)}list(a,b={},c){return this._client.getAPIList(eS`/containers/${a}/files`,eB,{query:b,...c})}delete(a,b,c){let{container_id:d}=b;return this._client.delete(eS`/containers/${d}/files/${a}`,{...c,headers:fb([{Accept:"*/*"},c?.headers])})}}fu.Content=ft;class fv extends eP{constructor(){super(...arguments),this.files=new fu(this._client)}create(a,b){return this._client.post("/containers",{body:a,...b})}retrieve(a,b){return this._client.get(eS`/containers/${a}`,b)}list(a={},b){return this._client.getAPIList("/containers",eB,{query:a,...b})}delete(a,b){return this._client.delete(eS`/containers/${a}`,{...b,headers:fb([{Accept:"*/*"},b?.headers])})}}fv.Files=fu;class fw extends eP{create(a,b){let c=!!a.encoding_format,d=c?a.encoding_format:"base64";c&&ep(this._client).debug("embeddings/user defined encoding_format:",a.encoding_format);let e=this._client.post("/embeddings",{body:{...a,encoding_format:d},...b});return c?e:(ep(this._client).debug("embeddings/decoding base64 embeddings from base64"),e._thenUnwrap(a=>(a&&a.data&&a.data.forEach(a=>{let b=a.embedding;a.embedding=(a=>{if("undefined"!=typeof Buffer){let b=Buffer.from(a,"base64");return Array.from(new Float32Array(b.buffer,b.byteOffset,b.length/Float32Array.BYTES_PER_ELEMENT))}{let b=atob(a),c=b.length,d=new Uint8Array(c);for(let a=0;a<c;a++)d[a]=b.charCodeAt(a);return Array.from(new Float32Array(d.buffer))}})(b)}),a)))}}class fx extends eP{retrieve(a,b,c){let{eval_id:d,run_id:e}=b;return this._client.get(eS`/evals/${d}/runs/${e}/output_items/${a}`,c)}list(a,b,c){let{eval_id:d,...e}=b;return this._client.getAPIList(eS`/evals/${d}/runs/${a}/output_items`,eB,{query:e,...c})}}class fy extends eP{constructor(){super(...arguments),this.outputItems=new fx(this._client)}create(a,b,c){return this._client.post(eS`/evals/${a}/runs`,{body:b,...c})}retrieve(a,b,c){let{eval_id:d}=b;return this._client.get(eS`/evals/${d}/runs/${a}`,c)}list(a,b={},c){return this._client.getAPIList(eS`/evals/${a}/runs`,eB,{query:b,...c})}delete(a,b,c){let{eval_id:d}=b;return this._client.delete(eS`/evals/${d}/runs/${a}`,c)}cancel(a,b,c){let{eval_id:d}=b;return this._client.post(eS`/evals/${d}/runs/${a}`,c)}}fy.OutputItems=fx;class fz extends eP{constructor(){super(...arguments),this.runs=new fy(this._client)}create(a,b){return this._client.post("/evals",{body:a,...b})}retrieve(a,b){return this._client.get(eS`/evals/${a}`,b)}update(a,b,c){return this._client.post(eS`/evals/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/evals",eB,{query:a,...b})}delete(a,b){return this._client.delete(eS`/evals/${a}`,b)}}fz.Runs=fy;class fA extends eP{create(a,b){return this._client.post("/files",eG({body:a,...b},this._client))}retrieve(a,b){return this._client.get(eS`/files/${a}`,b)}list(a={},b){return this._client.getAPIList("/files",eB,{query:a,...b})}delete(a,b){return this._client.delete(eS`/files/${a}`,b)}content(a,b){return this._client.get(eS`/files/${a}/content`,{...b,headers:fb([{Accept:"application/binary"},b?.headers]),__binaryResponse:!0})}async waitForProcessing(a,{pollInterval:b=5e3,maxWait:c=18e5}={}){let d=new Set(["processed","error","deleted"]),e=Date.now(),f=await this.retrieve(a);for(;!f.status||!d.has(f.status);)if(await dZ(b),f=await this.retrieve(a),Date.now()-e>c)throw new dJ({message:`Giving up on waiting for file ${a} to finish processing after ${c} milliseconds.`});return f}}class fB extends eP{}class fC extends eP{run(a,b){return this._client.post("/fine_tuning/alpha/graders/run",{body:a,...b})}validate(a,b){return this._client.post("/fine_tuning/alpha/graders/validate",{body:a,...b})}}class fD extends eP{constructor(){super(...arguments),this.graders=new fC(this._client)}}fD.Graders=fC;class fE extends eP{create(a,b,c){return this._client.getAPIList(eS`/fine_tuning/checkpoints/${a}/permissions`,eA,{body:b,method:"post",...c})}retrieve(a,b={},c){return this._client.get(eS`/fine_tuning/checkpoints/${a}/permissions`,{query:b,...c})}delete(a,b,c){let{fine_tuned_model_checkpoint:d}=b;return this._client.delete(eS`/fine_tuning/checkpoints/${d}/permissions/${a}`,c)}}class fF extends eP{constructor(){super(...arguments),this.permissions=new fE(this._client)}}fF.Permissions=fE;class fG extends eP{list(a,b={},c){return this._client.getAPIList(eS`/fine_tuning/jobs/${a}/checkpoints`,eB,{query:b,...c})}}class fH extends eP{constructor(){super(...arguments),this.checkpoints=new fG(this._client)}create(a,b){return this._client.post("/fine_tuning/jobs",{body:a,...b})}retrieve(a,b){return this._client.get(eS`/fine_tuning/jobs/${a}`,b)}list(a={},b){return this._client.getAPIList("/fine_tuning/jobs",eB,{query:a,...b})}cancel(a,b){return this._client.post(eS`/fine_tuning/jobs/${a}/cancel`,b)}listEvents(a,b={},c){return this._client.getAPIList(eS`/fine_tuning/jobs/${a}/events`,eB,{query:b,...c})}pause(a,b){return this._client.post(eS`/fine_tuning/jobs/${a}/pause`,b)}resume(a,b){return this._client.post(eS`/fine_tuning/jobs/${a}/resume`,b)}}fH.Checkpoints=fG;class fI extends eP{constructor(){super(...arguments),this.methods=new fB(this._client),this.jobs=new fH(this._client),this.checkpoints=new fF(this._client),this.alpha=new fD(this._client)}}fI.Methods=fB,fI.Jobs=fH,fI.Checkpoints=fF,fI.Alpha=fD;class fJ extends eP{}class fK extends eP{constructor(){super(...arguments),this.graderModels=new fJ(this._client)}}fK.GraderModels=fJ;class fL extends eP{createVariation(a,b){return this._client.post("/images/variations",eG({body:a,...b},this._client))}edit(a,b){return this._client.post("/images/edits",eG({body:a,...b},this._client))}generate(a,b){return this._client.post("/images/generations",{body:a,...b})}}class fM extends eP{retrieve(a,b){return this._client.get(eS`/models/${a}`,b)}list(a){return this._client.getAPIList("/models",eA,a)}delete(a,b){return this._client.delete(eS`/models/${a}`,b)}}class fN extends eP{create(a,b){return this._client.post("/moderations",{body:a,...b})}}function fO(a,b){let c=a.output.map(a=>{if("function_call"===a.type)return{...a,parsed_arguments:function(a,b){var c,d;let e=(c=a.tools??[],d=b.name,c.find(a=>"function"===a.type&&a.name===d));return{...b,...b,parsed_arguments:e?.$brand==="auto-parseable-tool"?e.$parseRaw(b.arguments):e?.strict?JSON.parse(b.arguments):null}}(b,a)};if("message"===a.type){let c=a.content.map(a=>{var c,d;return"output_text"===a.type?{...a,parsed:(c=b,d=a.text,c.text?.format?.type!=="json_schema"?null:"$parseRaw"in c.text?.format?(c.text?.format).$parseRaw(d):JSON.parse(d))}:a});return{...a,content:c}}return a}),d=Object.assign({},a,{output:c});return Object.getOwnPropertyDescriptor(a,"output_text")||fP(d),Object.defineProperty(d,"output_parsed",{enumerable:!0,get(){for(let a of d.output)if("message"===a.type){for(let b of a.content)if("output_text"===b.type&&null!==b.parsed)return b.parsed}return null}}),d}function fP(a){let b=[];for(let c of a.output)if("message"===c.type)for(let a of c.content)"output_text"===a.type&&b.push(a.text);a.output_text=b.join("")}class fQ extends eW{constructor(a){super(),az.add(this),aA.set(this,void 0),aB.set(this,void 0),aC.set(this,void 0),dA(this,aA,a,"f")}static createResponse(a,b,c){let d=new fQ(b);return d._run(()=>d._createOrRetrieveResponse(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createOrRetrieveResponse(a,b,c){let d,e=c?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort())),dB(this,az,"m",aD).call(this);let f=null;for await(let e of("response_id"in b?(d=await a.responses.retrieve(b.response_id,{stream:!0},{...c,signal:this.controller.signal,stream:!0}),f=b.starting_after??null):d=await a.responses.create({...b,stream:!0},{...c,signal:this.controller.signal}),this._connected(),d))dB(this,az,"m",aE).call(this,e,f);if(d.controller.signal?.aborted)throw new dH;return dB(this,az,"m",aF).call(this)}[(aA=new WeakMap,aB=new WeakMap,aC=new WeakMap,az=new WeakSet,aD=function(){this.ended||dA(this,aB,void 0,"f")},aE=function(a,b){if(this.ended)return;let c=(a,c)=>{(null==b||c.sequence_number>b)&&this._emit(a,c)},d=dB(this,az,"m",aG).call(this,a);switch(c("event",a),a.type){case"response.output_text.delta":{let b=d.output[a.output_index];if(!b)throw new dF(`missing output at index ${a.output_index}`);if("message"===b.type){let d=b.content[a.content_index];if(!d)throw new dF(`missing content at index ${a.content_index}`);if("output_text"!==d.type)throw new dF(`expected content to be 'output_text', got ${d.type}`);c("response.output_text.delta",{...a,snapshot:d.text})}break}case"response.function_call_arguments.delta":{let b=d.output[a.output_index];if(!b)throw new dF(`missing output at index ${a.output_index}`);"function_call"===b.type&&c("response.function_call_arguments.delta",{...a,snapshot:b.arguments});break}default:c(a.type,a)}},aF=function(){if(this.ended)throw new dF("stream has ended, this shouldn't happen");let a=dB(this,aB,"f");if(!a)throw new dF("request ended without sending any events");dA(this,aB,void 0,"f");let b=function(a,b){var c;return b&&(c=b,eX(c.text?.format))?fO(a,b):{...a,output_parsed:null,output:a.output.map(a=>"function_call"===a.type?{...a,parsed_arguments:null}:"message"===a.type?{...a,content:a.content.map(a=>({...a,parsed:null}))}:a)}}(a,dB(this,aA,"f"));return dA(this,aC,b,"f"),b},aG=function(a){let b=dB(this,aB,"f");if(!b){if("response.created"!==a.type)throw new dF(`When snapshot hasn't been set yet, expected 'response.created' event, got ${a.type}`);return dA(this,aB,a.response,"f")}switch(a.type){case"response.output_item.added":b.output.push(a.item);break;case"response.content_part.added":{let c=b.output[a.output_index];if(!c)throw new dF(`missing output at index ${a.output_index}`);"message"===c.type&&c.content.push(a.part);break}case"response.output_text.delta":{let c=b.output[a.output_index];if(!c)throw new dF(`missing output at index ${a.output_index}`);if("message"===c.type){let b=c.content[a.content_index];if(!b)throw new dF(`missing content at index ${a.content_index}`);if("output_text"!==b.type)throw new dF(`expected content to be 'output_text', got ${b.type}`);b.text+=a.delta}break}case"response.function_call_arguments.delta":{let c=b.output[a.output_index];if(!c)throw new dF(`missing output at index ${a.output_index}`);"function_call"===c.type&&(c.arguments+=a.delta);break}case"response.completed":dA(this,aB,a.response,"f")}return b},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let a=dB(this,aC,"f");if(!a)throw new dF("stream ended without producing a ChatCompletion");return a}}class fR extends eP{list(a,b={},c){return this._client.getAPIList(eS`/responses/${a}/input_items`,eB,{query:b,...c})}}class fS extends eP{constructor(){super(...arguments),this.inputItems=new fR(this._client)}create(a,b){return this._client.post("/responses",{body:a,...b,stream:a.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&fP(a),a))}retrieve(a,b={},c){return this._client.get(eS`/responses/${a}`,{query:b,...c,stream:b?.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&fP(a),a))}delete(a,b){return this._client.delete(eS`/responses/${a}`,{...b,headers:fb([{Accept:"*/*"},b?.headers])})}parse(a,b){return this._client.responses.create(a,b)._thenUnwrap(b=>fO(b,a))}stream(a,b){return fQ.createResponse(this._client,a,b)}cancel(a,b){return this._client.post(eS`/responses/${a}/cancel`,b)}}fS.InputItems=fR;class fT extends eP{create(a,b,c){return this._client.post(eS`/uploads/${a}/parts`,eG({body:b,...c},this._client))}}class fU extends eP{constructor(){super(...arguments),this.parts=new fT(this._client)}create(a,b){return this._client.post("/uploads",{body:a,...b})}cancel(a,b){return this._client.post(eS`/uploads/${a}/cancel`,b)}complete(a,b,c){return this._client.post(eS`/uploads/${a}/complete`,{body:b,...c})}}fU.Parts=fT;let fV=async a=>{let b=await Promise.allSettled(a),c=b.filter(a=>"rejected"===a.status);if(c.length){for(let a of c)console.error(a.reason);throw Error(`${c.length} promise(s) failed - see the above errors`)}let d=[];for(let a of b)"fulfilled"===a.status&&d.push(a.value);return d};class fW extends eP{create(a,b,c){return this._client.post(eS`/vector_stores/${a}/file_batches`,{body:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(eS`/vector_stores/${d}/file_batches/${a}`,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{vector_store_id:d}=b;return this._client.post(eS`/vector_stores/${d}/file_batches/${a}/cancel`,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b);return await this.poll(a,d.id,c)}listFiles(a,b,c){let{vector_store_id:d,...e}=b;return this._client.getAPIList(eS`/vector_stores/${d}/file_batches/${a}/files`,eB,{query:e,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async poll(a,b,c){let d=fb([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse();switch(e.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await dZ(g);break;case"failed":case"cancelled":case"completed":return e}}}async uploadAndPoll(a,{files:b,fileIds:c=[]},d){if(null==b||0==b.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let e=Math.min(d?.maxConcurrency??5,b.length),f=this._client,g=b.values(),h=[...c];async function i(a){for(let b of a){let a=await f.files.create({file:b,purpose:"assistants"},d);h.push(a.id)}}let j=Array(e).fill(g).map(i);return await fV(j),await this.createAndPoll(a,{file_ids:h})}}class fX extends eP{create(a,b,c){return this._client.post(eS`/vector_stores/${a}/files`,{body:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(eS`/vector_stores/${d}/files/${a}`,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{vector_store_id:d,...e}=b;return this._client.post(eS`/vector_stores/${d}/files/${a}`,{body:e,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(eS`/vector_stores/${a}/files`,eB,{query:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{vector_store_id:d}=b;return this._client.delete(eS`/vector_stores/${d}/files/${a}`,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(a,d.id,c)}async poll(a,b,c){let d=fb([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let e=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse(),f=e.data;switch(f.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=e.response.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await dZ(g);break;case"failed":case"completed":return f}}}async upload(a,b,c){let d=await this._client.files.create({file:b,purpose:"assistants"},c);return this.create(a,{file_id:d.id},c)}async uploadAndPoll(a,b,c){let d=await this.upload(a,b,c);return await this.poll(a,d.id,c)}content(a,b,c){let{vector_store_id:d}=b;return this._client.getAPIList(eS`/vector_stores/${d}/files/${a}/content`,eA,{...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class fY extends eP{constructor(){super(...arguments),this.files=new fX(this._client),this.fileBatches=new fW(this._client)}create(a,b){return this._client.post("/vector_stores",{body:a,...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(eS`/vector_stores/${a}`,{...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(eS`/vector_stores/${a}`,{body:b,...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/vector_stores",eB,{query:a,...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(eS`/vector_stores/${a}`,{...b,headers:fb([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}search(a,b,c){return this._client.getAPIList(eS`/vector_stores/${a}/search`,eA,{body:b,method:"post",...c,headers:fb([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}fY.Files=fX,fY.FileBatches=fW;class fZ extends eP{constructor(){super(...arguments),aH.add(this)}async unwrap(a,b,c=this._client.webhookSecret,d=300){return await this.verifySignature(a,b,c,d),JSON.parse(a)}async verifySignature(a,b,c=this._client.webhookSecret,d=300){if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");dB(this,aH,"m",aI).call(this,c);let e=fb([b]).values,f=dB(this,aH,"m",aJ).call(this,e,"webhook-signature"),g=dB(this,aH,"m",aJ).call(this,e,"webhook-timestamp"),h=dB(this,aH,"m",aJ).call(this,e,"webhook-id"),i=parseInt(g,10);if(isNaN(i))throw new dU("Invalid webhook timestamp format");let j=Math.floor(Date.now()/1e3);if(j-i>d)throw new dU("Webhook timestamp is too old");if(i>j+d)throw new dU("Webhook timestamp is too new");let k=f.split(" ").map(a=>a.startsWith("v1,")?a.substring(3):a),l=c.startsWith("whsec_")?Buffer.from(c.replace("whsec_",""),"base64"):Buffer.from(c,"utf-8"),m=h?`${h}.${g}.${a}`:`${g}.${a}`,n=await crypto.subtle.importKey("raw",l,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let a of k)try{let b=Buffer.from(a,"base64");if(await crypto.subtle.verify("HMAC",n,b,new TextEncoder().encode(m)))return}catch{continue}throw new dU("The given webhook signature does not match the expected signature")}}aH=new WeakSet,aI=function(a){if("string"!=typeof a||0===a.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},aJ=function(a,b){if(!a)throw Error("Headers are required");let c=a.get(b);if(null==c)throw Error(`Missing required header: ${b}`);return c};class f${constructor({baseURL:a=fn("OPENAI_BASE_URL"),apiKey:b=fn("OPENAI_API_KEY"),organization:c=fn("OPENAI_ORG_ID")??null,project:d=fn("OPENAI_PROJECT_ID")??null,webhookSecret:e=fn("OPENAI_WEBHOOK_SECRET")??null,...f}={}){if(aK.add(this),aM.set(this,void 0),this.completions=new fs(this),this.chat=new e9(this),this.embeddings=new fw(this),this.files=new fA(this),this.images=new fL(this),this.audio=new ff(this),this.moderations=new fN(this),this.models=new fM(this),this.fineTuning=new fI(this),this.graders=new fK(this),this.vectorStores=new fY(this),this.webhooks=new fZ(this),this.beta=new fr(this),this.batches=new fg(this),this.uploads=new fU(this),this.responses=new fS(this),this.evals=new fz(this),this.containers=new fv(this),void 0===b)throw new dF("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let g={apiKey:b,organization:c,project:d,webhookSecret:e,...f,baseURL:a||"https://api.openai.com/v1"};if(!g.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new dF("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=g.baseURL,this.timeout=g.timeout??aL.DEFAULT_TIMEOUT,this.logger=g.logger??console;let h="warn";this.logLevel=h,this.logLevel=ek(g.logLevel,"ClientOptions.logLevel",this)??ek(fn("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??h,this.fetchOptions=g.fetchOptions,this.maxRetries=g.maxRetries??2,this.fetch=g.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),dA(this,aM,d5,"f"),this._options=g,this.apiKey=b,this.organization=c,this.project=d,this.webhookSecret=e}withOptions(a){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...a})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:a,nulls:b}){}async authHeaders(a){return fb([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(a){return function(a,b={}){let c,d=a,e=function(a=ee){let b;if(void 0!==a.allowEmptyArrays&&"boolean"!=typeof a.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==a.encodeDotInKeys&&"boolean"!=typeof a.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==a.encoder&&void 0!==a.encoder&&"function"!=typeof a.encoder)throw TypeError("Encoder has to be a function.");let c=a.charset||ee.charset;if(void 0!==a.charset&&"utf-8"!==a.charset&&"iso-8859-1"!==a.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let d=d6;if(void 0!==a.format){if(!d9(d8,a.format))throw TypeError("Unknown format option provided.");d=a.format}let e=d8[d],f=ee.filter;if(("function"==typeof a.filter||dW(a.filter))&&(f=a.filter),b=a.arrayFormat&&a.arrayFormat in ec?a.arrayFormat:"indices"in a?a.indices?"indices":"repeat":ee.arrayFormat,"commaRoundTrip"in a&&"boolean"!=typeof a.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let g=void 0===a.allowDots?!0==!!a.encodeDotInKeys||ee.allowDots:!!a.allowDots;return{addQueryPrefix:"boolean"==typeof a.addQueryPrefix?a.addQueryPrefix:ee.addQueryPrefix,allowDots:g,allowEmptyArrays:"boolean"==typeof a.allowEmptyArrays?!!a.allowEmptyArrays:ee.allowEmptyArrays,arrayFormat:b,charset:c,charsetSentinel:"boolean"==typeof a.charsetSentinel?a.charsetSentinel:ee.charsetSentinel,commaRoundTrip:!!a.commaRoundTrip,delimiter:void 0===a.delimiter?ee.delimiter:a.delimiter,encode:"boolean"==typeof a.encode?a.encode:ee.encode,encodeDotInKeys:"boolean"==typeof a.encodeDotInKeys?a.encodeDotInKeys:ee.encodeDotInKeys,encoder:"function"==typeof a.encoder?a.encoder:ee.encoder,encodeValuesOnly:"boolean"==typeof a.encodeValuesOnly?a.encodeValuesOnly:ee.encodeValuesOnly,filter:f,format:d,formatter:e,serializeDate:"function"==typeof a.serializeDate?a.serializeDate:ee.serializeDate,skipNulls:"boolean"==typeof a.skipNulls?a.skipNulls:ee.skipNulls,sort:"function"==typeof a.sort?a.sort:null,strictNullHandling:"boolean"==typeof a.strictNullHandling?a.strictNullHandling:ee.strictNullHandling}}(b);"function"==typeof e.filter?d=(0,e.filter)("",d):dW(e.filter)&&(c=e.filter);let f=[];if("object"!=typeof d||null===d)return"";let g=ec[e.arrayFormat],h="comma"===g&&e.commaRoundTrip;c||(c=Object.keys(d)),e.sort&&c.sort(e.sort);let i=new WeakMap;for(let a=0;a<c.length;++a){let b=c[a];e.skipNulls&&null===d[b]||ed(f,function a(b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){var t,u;let v,w=b,x=s,y=0,z=!1;for(;void 0!==(x=x.get(ef))&&!z;){let a=x.get(b);if(y+=1,void 0!==a)if(a===y)throw RangeError("Cyclic object value");else z=!0;void 0===x.get(ef)&&(y=0)}if("function"==typeof k?w=k(c,w):w instanceof Date?w=n?.(w):"comma"===d&&dW(w)&&(w=eb(w,function(a){return a instanceof Date?n?.(a):a})),null===w){if(g)return j&&!q?j(c,ee.encoder,r,"key",o):c;w=""}if("string"==typeof(t=w)||"number"==typeof t||"boolean"==typeof t||"symbol"==typeof t||"bigint"==typeof t||(u=w)&&"object"==typeof u&&u.constructor&&u.constructor.isBuffer&&u.constructor.isBuffer(u)){if(j){let a=q?c:j(c,ee.encoder,r,"key",o);return[p?.(a)+"="+p?.(j(w,ee.encoder,r,"value",o))]}return[p?.(c)+"="+p?.(String(w))]}let A=[];if(void 0===w)return A;if("comma"===d&&dW(w))q&&j&&(w=eb(w,j)),v=[{value:w.length>0?w.join(",")||null:void 0}];else if(dW(k))v=k;else{let a=Object.keys(w);v=l?a.sort(l):a}let B=i?String(c).replace(/\./g,"%2E"):String(c),C=e&&dW(w)&&1===w.length?B+"[]":B;if(f&&dW(w)&&0===w.length)return C+"[]";for(let c=0;c<v.length;++c){let t=v[c],u="object"==typeof t&&void 0!==t.value?t.value:w[t];if(h&&null===u)continue;let x=m&&i?t.replace(/\./g,"%2E"):t,z=dW(w)?"function"==typeof d?d(C,x):C:C+(m?"."+x:"["+x+"]");s.set(b,y);let B=new WeakMap;B.set(ef,s),ed(A,a(u,z,d,e,f,g,h,i,"comma"===d&&q&&dW(w)?null:j,k,l,m,n,o,p,q,r,B))}return A}(d[b],b,g,h,e.allowEmptyArrays,e.strictNullHandling,e.skipNulls,e.encodeDotInKeys,e.encode?e.encoder:null,e.filter,e.sort,e.allowDots,e.serializeDate,e.format,e.formatter,e.encodeValuesOnly,e.charset,i))}let j=f.join(e.delimiter),k=!0===e.addQueryPrefix?"?":"";return e.charsetSentinel&&("iso-8859-1"===e.charset?k+="utf8=%26%2310003%3B&":k+="utf8=%E2%9C%93&"),j.length>0?k+j:""}(a,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${d$}`}defaultIdempotencyKey(){return`stainless-node-retry-${dC()}`}makeStatusError(a,b,c,d){return dG.generate(a,b,c,d)}buildURL(a,b,c){let d=!dB(this,aK,"m",aN).call(this)&&c||this.baseURL,e=new URL(dV.test(a)?a:d+(d.endsWith("/")&&a.startsWith("/")?a.slice(1):a)),f=this.defaultQuery();return!function(a){if(!a)return!0;for(let b in a)return!1;return!0}(f)&&(b={...f,...b}),"object"==typeof b&&b&&!Array.isArray(b)&&(e.search=this.stringifyQuery(b)),e.toString()}async prepareOptions(a){}async prepareRequest(a,{url:b,options:c}){}get(a,b){return this.methodRequest("get",a,b)}post(a,b){return this.methodRequest("post",a,b)}patch(a,b){return this.methodRequest("patch",a,b)}put(a,b){return this.methodRequest("put",a,b)}delete(a,b){return this.methodRequest("delete",a,b)}methodRequest(a,b,c){return this.request(Promise.resolve(c).then(c=>({method:a,path:b,...c})))}request(a,b=null){return new ex(this,this.makeRequest(a,b,void 0))}async makeRequest(a,b,c){let d=await a,e=d.maxRetries??this.maxRetries;null==b&&(b=e),await this.prepareOptions(d);let{req:f,url:g,timeout:h}=await this.buildRequest(d,{retryCount:e-b});await this.prepareRequest(f,{url:g,options:d});let i="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),j=void 0===c?"":`, retryOf: ${c}`,k=Date.now();if(ep(this).debug(`[${i}] sending request`,eq({retryOfRequestLogID:c,method:d.method,url:g,options:d,headers:f.headers})),d.signal?.aborted)throw new dH;let l=new AbortController,m=await this.fetchWithTimeout(g,f,h,l).catch(dE),n=Date.now();if(m instanceof Error){let a=`retrying, ${b} attempts remaining`;if(d.signal?.aborted)throw new dH;let e=dD(m)||/timed? ?out/i.test(String(m)+("cause"in m?String(m.cause):""));if(b)return ep(this).info(`[${i}] connection ${e?"timed out":"failed"} - ${a}`),ep(this).debug(`[${i}] connection ${e?"timed out":"failed"} (${a})`,eq({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),this.retryRequest(d,b,c??i);if(ep(this).info(`[${i}] connection ${e?"timed out":"failed"} - error; no more retries left`),ep(this).debug(`[${i}] connection ${e?"timed out":"failed"} (error; no more retries left)`,eq({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),e)throw new dJ;throw new dI({cause:m})}let o=[...m.headers.entries()].filter(([a])=>"x-request-id"===a).map(([a,b])=>", "+a+": "+JSON.stringify(b)).join(""),p=`[${i}${j}${o}] ${f.method} ${g} ${m.ok?"succeeded":"failed"} with status ${m.status} in ${n-k}ms`;if(!m.ok){let a=await this.shouldRetry(m);if(b&&a){let a=`retrying, ${b} attempts remaining`;return await d4(m.body),ep(this).info(`${p} - ${a}`),ep(this).debug(`[${i}] response error (${a})`,eq({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),this.retryRequest(d,b,c??i,m.headers)}let e=a?"error; no more retries left":"error; not retryable";ep(this).info(`${p} - ${e}`);let f=await m.text().catch(a=>dE(a).message),g=(a=>{try{return JSON.parse(a)}catch(a){return}})(f),h=g?void 0:f;throw ep(this).debug(`[${i}] response error (${e})`,eq({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,message:h,durationMs:Date.now()-k})),this.makeStatusError(m.status,g,h,m.headers)}return ep(this).info(p),ep(this).debug(`[${i}] response start`,eq({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),{response:m,options:d,controller:l,requestLogID:i,retryOfRequestLogID:c,startTime:k}}getAPIList(a,b,c){return this.requestAPIList(b,{method:"get",path:a,...c})}requestAPIList(a,b){return new ez(this,this.makeRequest(b,null,void 0),a)}async fetchWithTimeout(a,b,c,d){let{signal:e,method:f,...g}=b||{};e&&e.addEventListener("abort",()=>d.abort());let h=setTimeout(()=>d.abort(),c),i=globalThis.ReadableStream&&g.body instanceof globalThis.ReadableStream||"object"==typeof g.body&&null!==g.body&&Symbol.asyncIterator in g.body,j={signal:d.signal,...i?{duplex:"half"}:{},method:"GET",...g};f&&(j.method=f.toUpperCase());try{return await this.fetch.call(void 0,a,j)}finally{clearTimeout(h)}}async shouldRetry(a){let b=a.headers.get("x-should-retry");return"true"===b||"false"!==b&&(408===a.status||409===a.status||429===a.status||!!(a.status>=500))}async retryRequest(a,b,c,d){let e,f=d?.get("retry-after-ms");if(f){let a=parseFloat(f);Number.isNaN(a)||(e=a)}let g=d?.get("retry-after");if(g&&!e){let a=parseFloat(g);e=Number.isNaN(a)?Date.parse(g)-Date.now():1e3*a}if(!(e&&0<=e&&e<6e4)){let c=a.maxRetries??this.maxRetries;e=this.calculateDefaultRetryTimeoutMillis(b,c)}return await dZ(e),this.makeRequest(a,b-1,c)}calculateDefaultRetryTimeoutMillis(a,b){return Math.min(.5*Math.pow(2,b-a),8)*(1-.25*Math.random())*1e3}async buildRequest(a,{retryCount:b=0}={}){let c={...a},{method:d,path:e,query:f,defaultBaseURL:g}=c,h=this.buildURL(e,f,g);"timeout"in c&&((a,b)=>{if("number"!=typeof b||!Number.isInteger(b))throw new dF(`${a} must be an integer`);if(b<0)throw new dF(`${a} must be a positive integer`)})("timeout",c.timeout),c.timeout=c.timeout??this.timeout;let{bodyHeaders:i,body:j}=this.buildBody({options:c}),k=await this.buildHeaders({options:a,method:d,bodyHeaders:i,retryCount:b});return{req:{method:d,headers:k,...c.signal&&{signal:c.signal},...globalThis.ReadableStream&&j instanceof globalThis.ReadableStream&&{duplex:"half"},...j&&{body:j},...this.fetchOptions??{},...c.fetchOptions??{}},url:h,timeout:c.timeout}}async buildHeaders({options:a,method:b,bodyHeaders:c,retryCount:d}){let e={};this.idempotencyHeader&&"get"!==b&&(a.idempotencyKey||(a.idempotencyKey=this.defaultIdempotencyKey()),e[this.idempotencyHeader]=a.idempotencyKey);let g=fb([e,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(d),...a.timeout?{"X-Stainless-Timeout":String(Math.trunc(a.timeout/1e3))}:{},...f??(f=(()=>{let a="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":d$,"X-Stainless-OS":d0(Deno.build.os),"X-Stainless-Arch":d_(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":d$,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":d$,"X-Stainless-OS":d0(globalThis.process.platform??"unknown"),"X-Stainless-Arch":d_(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let b=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:a,pattern:b}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let c=b.exec(navigator.userAgent);if(c){let b=c[1]||0,d=c[2]||0,e=c[3]||0;return{browser:a,version:`${b}.${d}.${e}`}}}return null}();return b?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":d$,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${b.browser}`,"X-Stainless-Runtime-Version":b.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":d$,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(a),this._options.defaultHeaders,c,a.headers]);return this.validateHeaders(g),g.values}buildBody({options:{body:a,headers:b}}){if(!a)return{bodyHeaders:void 0,body:void 0};let c=fb([b]);return ArrayBuffer.isView(a)||a instanceof ArrayBuffer||a instanceof DataView||"string"==typeof a&&c.values.has("content-type")||a instanceof Blob||a instanceof FormData||a instanceof URLSearchParams||globalThis.ReadableStream&&a instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:a}:"object"==typeof a&&(Symbol.asyncIterator in a||Symbol.iterator in a&&"next"in a&&"function"==typeof a.next)?{bodyHeaders:void 0,body:d2(a)}:dB(this,aM,"f").call(this,{body:a,headers:c})}}aL=f$,aM=new WeakMap,aK=new WeakSet,aN=function(){return"https://api.openai.com/v1"!==this.baseURL},f$.OpenAI=aL,f$.DEFAULT_TIMEOUT=6e5,f$.OpenAIError=dF,f$.APIError=dG,f$.APIConnectionError=dI,f$.APIConnectionTimeoutError=dJ,f$.APIUserAbortError=dH,f$.NotFoundError=dN,f$.ConflictError=dO,f$.RateLimitError=dQ,f$.BadRequestError=dK,f$.AuthenticationError=dL,f$.InternalServerError=dR,f$.PermissionDeniedError=dM,f$.UnprocessableEntityError=dP,f$.InvalidWebhookSignatureError=dU,f$.toFile=eN,f$.Completions=fs,f$.Chat=e9,f$.Embeddings=fw,f$.Files=fA,f$.Images=fL,f$.Audio=ff,f$.Moderations=fN,f$.Models=fM,f$.FineTuning=fI,f$.Graders=fK,f$.VectorStores=fY,f$.Webhooks=fZ,f$.Beta=fr,f$.Batches=fg,f$.Uploads=fU,f$.Responses=fS,f$.Evals=fz,f$.Containers=fv;let f_=[{name:"weather_query",description:"获取指定城市的天气信息",parameters:{type:"object",properties:{city:{type:"string",description:"城市名称"},country:{type:"string",description:"国家代码（可选）"}},required:["city"]}},{name:"translate_text",description:"文本翻译服务",parameters:{type:"object",properties:{text:{type:"string",description:"要翻译的文本"},target_language:{type:"string",description:"目标语言代码（如：en, zh, ja）"},source_language:{type:"string",description:"源语言代码（可选，自动检测）"}},required:["text","target_language"]}},{name:"summarize_text",description:"文本摘要生成",parameters:{type:"object",properties:{text:{type:"string",description:"要生成摘要的文本"},max_length:{type:"number",description:"摘要最大长度（可选）"},style:{type:"string",description:"摘要风格",enum:["concise","detailed","bullet_points"]}},required:["text"]}}];class f0{constructor(){this.client=new f$({apiKey:process.env.OPENAI_API_KEY||"sk-holeazkksyigknkqzozszsgvgzmumdlajxjczwrecwtsdgxw",baseURL:"https://api.siliconflow.cn/v1"})}async createChatCompletion(a,b=!0){try{let c,d=this.convertToOpenAIMessages(a),e=(await this.client.chat.completions.create({model:"Qwen/Qwen2.5-32B-Instruct",messages:d,max_tokens:2e3,temperature:.7,tools:b?f_.map(a=>({type:"function",function:a})):void 0,tool_choice:b?"auto":void 0})).choices[0];if(!e?.message)throw Error("No response from OpenAI");let f={id:`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,role:"assistant",content:e.message.content||"",timestamp:Date.now()};return e.message.tool_calls&&(f.tool_calls=c=e.message.tool_calls.map(a=>({id:a.id,type:"function",function:{name:a.function.name,arguments:a.function.arguments}}))),{message:f,toolCalls:c}}catch(a){throw console.error("OpenAI API error:",a),Error(`Failed to create chat completion: ${a.message}`)}}convertToOpenAIMessages(a){return a.map(a=>{let b={role:a.role,content:a.content};return a.tool_calls&&(b.tool_calls=a.tool_calls),a.tool_call_id&&(b.tool_call_id=a.tool_call_id),b})}}let f1={北京:{city:"北京",temperature:22,description:"晴朗",humidity:45,wind_speed:12,feels_like:24},上海:{city:"上海",temperature:26,description:"多云",humidity:68,wind_speed:8,feels_like:28},广州:{city:"广州",temperature:29,description:"小雨",humidity:78,wind_speed:6,feels_like:32},beijing:{city:"Beijing",temperature:22,description:"Sunny",humidity:45,wind_speed:12,feels_like:24},shanghai:{city:"Shanghai",temperature:26,description:"Cloudy",humidity:68,wind_speed:8,feels_like:28},"new york":{city:"New York",temperature:18,description:"Partly Cloudy",humidity:52,wind_speed:15,feels_like:16},london:{city:"London",temperature:15,description:"Rainy",humidity:82,wind_speed:20,feels_like:12},tokyo:{city:"Tokyo",temperature:24,description:"Clear",humidity:58,wind_speed:10,feels_like:26}},f2={zh:{en:{你好:"Hello",世界:"World","你好，世界！":"Hello, World!",谢谢:"Thank you",再见:"Goodbye",早上好:"Good morning",晚上好:"Good evening",我爱你:"I love you",今天天气很好:"The weather is nice today",我正在学习编程:"I am learning programming"},ja:{你好:"こんにちは",世界:"世界","你好，世界！":"こんにちは、世界！",谢谢:"ありがとう",再见:"さようなら",早上好:"おはよう",晚上好:"こんばんは",我爱你:"愛してる",今天天气很好:"今日はいい天気ですね",我正在学习编程:"プログラミングを勉強しています"}},en:{zh:{Hello:"你好",World:"世界","Hello, World!":"你好，世界！","Thank you":"谢谢",Goodbye:"再见","Good morning":"早上好","Good evening":"晚上好","I love you":"我爱你","The weather is nice today":"今天天气很好","I am learning programming":"我正在学习编程"},ja:{Hello:"こんにちは",World:"世界","Hello, World!":"こんにちは、世界！","Thank you":"ありがとう",Goodbye:"さようなら","Good morning":"おはよう","Good evening":"こんばんは","I love you":"愛してる","The weather is nice today":"今日はいい天気ですね","I am learning programming":"プログラミングを勉強しています"}},ja:{zh:{こんにちは:"你好",世界:"世界","こんにちは、世界！":"你好，世界！",ありがとう:"谢谢",さようなら:"再见",おはよう:"早上好",こんばんは:"晚上好",愛してる:"我爱你"},en:{こんにちは:"Hello",世界:"World","こんにちは、世界！":"Hello, World!",ありがとう:"Thank you",さようなら:"Goodbye",おはよう:"Good morning",こんばんは:"Good evening",愛してる:"I love you"}}};function f3(a,b){return(({zh:"[中文翻译] ",en:"[English Translation] ",ja:"[日本語翻訳] "})[b]||"[Translation] ")+a}function f4(a){return a.split(/\s+/).filter(a=>a.length>0).length}function f5(a,b){let c=a.split(/[.!?。！？]+/).filter(a=>a.trim().length>0),d="",e=0;for(let a of c){let c=f4(a);if(e+c<=b)d+=a.trim()+". ",e+=c;else break}return d.trim()}let f6={name:"weather_query",description:"获取指定城市的天气信息",execute:async function a(a){try{let{city:b,country:c}=a,d=b.toLowerCase().trim(),e=f1[d]||f1[b];if(!e){let a=Object.keys(f1).find(a=>a.includes(d)||d.includes(a));a&&(e=f1[a])}e||(e=function(a){let b=["Sunny","Cloudy","Partly Cloudy","Rainy","Clear","Overcast"],c=Math.floor(30*Math.random())+5;return{city:a,temperature:c,description:b[Math.floor(Math.random()*b.length)],humidity:Math.floor(40*Math.random())+40,wind_speed:Math.floor(20*Math.random())+5,feels_like:c+(Math.random()-.5)*6}}(b));let f=(Math.random()-.5)*4;return e={...e,temperature:Math.round((e.temperature+f)*10)/10,feels_like:Math.round((e.feels_like+f)*10)/10},console.log(`🌤️ Weather query for ${b}: ${e.temperature}\xb0C, ${e.description}`),e}catch(b){throw console.error("Weather query error:",b),Error(`Failed to get weather for ${a.city}: ${b.message}`)}}},f7={name:"translate_text",description:"文本翻译服务",execute:async function a(a){try{var b;let{text:c,target_language:d,source_language:e}=a,f=e||(b=c,/[\u4e00-\u9fff]/.test(b)?"zh":/[\u3040-\u309f\u30a0-\u30ff]/.test(b)?"ja":"en"),g=c,h=f2[f];if(h&&h[d]){let a=h[d];g=a[c]?a[c]:f3(c,d)}else g=f3(c,d);let i={original_text:c,translated_text:g,source_language:f,target_language:d};return console.log(`🌐 Translation: "${c}" (${f}) -> "${g}" (${d})`),i}catch(a){throw console.error("Translation error:",a),Error(`Failed to translate text: ${a.message}`)}}},f8={name:"summarize_text",description:"文本摘要生成",execute:async function a(a){try{var b,c,d,e;let{text:f,max_length:g=100,style:h="concise"}=a,i=f4(f),j="";j="bullet_points"===h?function(a,b){let c=a.split(/[.!?。！？]+/).filter(a=>a.trim().length>0);return c.slice(0,Math.min(5,c.length)).map(a=>"• "+a.trim()).join("\n")}(f,0):"detailed"===h?(b=f,c=g,f5(b,.8*c)+"\n\n本文详细讨论了"+(d=b,e=3,Array.from(new Set(d.split(/\s+/).filter(a=>a.length>3))).sort((a,b)=>b.length-a.length).slice(0,3)).join("、")+"等内容。"):f5(f,g);let k=f4(j),l={original_text:f,summary:j,word_count:{original:i,summary:k}};return console.log(`📝 Summary generated: ${i} words -> ${k} words`),l}catch(a){throw console.error("Summary generation error:",a),Error(`Failed to generate summary: ${a.message}`)}}},f9=[f6,f7,f8];function ga(){return f9.map(a=>({name:a.name,description:a.description,available:!0}))}let gb=new Map,gc=new f0,gd={weather_query:f6.execute,translate_text:f7.execute,summarize_text:f8.execute};async function ge(a){try{let b,{function:c}=a,d=gd[c.name];if(!d)throw Error(`Unknown tool: ${c.name}`);try{b=JSON.parse(c.arguments)}catch(a){throw Error("Invalid JSON in function arguments")}console.log(`🔧 Executing tool: ${c.name} with args:`,b);let e=await d(b),f={tool_call_id:a.id,content:JSON.stringify(e,null,2)};return console.log(`✅ Tool execution completed: ${c.name}`),f}catch(b){return console.error(`❌ Tool execution failed: ${a.function.name}`,b),{tool_call_id:a.id,content:JSON.stringify({error:!0,message:b.message,code:"TOOL_EXECUTION_FAILED"},null,2)}}}async function gf(a){let b=[];for(let c of a){let a=await ge(c);b.push(a)}return b}function gg(){return`${Date.now()}_${Math.random().toString(36).substr(2,9)}`}let gh=cq({sendMessage:cr.input(dy({message:dw(),conversation_id:dw().optional()})).mutation(async({input:a})=>{let{message:b,conversation_id:c}=a,d=c||`conv_${gg()}`,e=gb.get(d)||[],f={id:`msg_${gg()}`,role:"user",content:b,timestamp:Date.now()};if(0===e.length){let a={id:`sys_${Date.now()}`,role:"system",content:`你是一个智能助手，可以使用多种工具来帮助用户。你有以下工具可用：
1. 天气查询 - 获取城市天气信息
2. 翻译工具 - 多语言文本翻译
3. 文本摘要 - 生成文本摘要

请根据用户的需求选择合适的工具，并提供有用的回答。`,timestamp:Date.now()};e.push(a)}e.push(f);let{message:g,toolCalls:h}=await gc.createChatCompletion(e,!0);e.push(g);let i=g;if(h&&h.length>0){for(let a of(console.log(`🔧 Executing ${h.length} tool call(s)`),await gf(h))){let b={id:`tool_${gg()}`,role:"tool",content:a.content,tool_call_id:a.tool_call_id,timestamp:Date.now()};e.push(b)}let{message:a}=await gc.createChatCompletion(e,!1);e.push(a),i=a}return gb.set(d,e),console.log(`💬 Chat response sent for conversation: ${d}`),{message:i,conversation_id:d,tool_calls:h}}),getConversation:cr.input(dy({conversation_id:dw()})).query(async({input:a})=>{let{conversation_id:b}=a,c=gb.get(b);if(!c)throw Error("Conversation not found");return{conversation_id:b,messages:c,message_count:c.length}}),getConversations:cr.query(async()=>{let a=Array.from(gb.entries()).map(([a,b])=>({conversation_id:a,message_count:b.length,last_message:b[b.length-1],created_at:b[0]?.timestamp,updated_at:b[b.length-1]?.timestamp}));return{conversations:a,total_count:a.length}}),deleteConversation:cr.input(dy({conversation_id:dw()})).mutation(async({input:a})=>{let{conversation_id:b}=a,c=gb.has(b);return gb.delete(b),{success:!0,message:c?"Conversation deleted":"Conversation not found",conversation_id:b}})}),gi=cq({getAll:cr.query(async()=>({tools:ga(),total_count:ga().length})),getWeather:cr.input(dy({city:dw(),country:dw().optional()})).query(async({input:a})=>await f6.execute(a)),translate:cr.input(dy({text:dw(),target_language:dw(),source_language:dw().optional()})).mutation(async({input:a})=>await f7.execute({text:a.text,target_language:a.target_language,source_language:a.source_language})),summarize:cr.input(dy({text:dw(),max_length:dx().optional(),style:dz(["concise","detailed","bullet_points"]).optional()})).mutation(async({input:a})=>await f8.execute(a))}),gj=cq({chat:gh,tools:gi}),gk=a=>b2({endpoint:"/api/trpc",req:a,router:gj,createContext:()=>({})}),gl=new aP.AppRouteRouteModule({definition:{kind:aQ.RouteKind.APP_ROUTE,page:"/api/trpc/[trpc]/route",pathname:"/api/trpc/[trpc]",filename:"route",bundlePath:"app/api/trpc/[trpc]/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/api/trpc/[trpc]/route.ts",nextConfigOutput:"",userland:aO}),{workAsyncStorage:gm,workUnitAsyncStorage:gn,serverHooks:go}=gl;function gp(){return(0,aR.patchFetch)({workAsyncStorage:gm,workUnitAsyncStorage:gn})}async function gq(a,b,c){var d;let e="/api/trpc/[trpc]/route";"/index"===e&&(e="/");let f=await gl.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:g,params:h,nextConfig:i,isDraftMode:j,prerenderManifest:k,routerServerContext:l,isOnDemandRevalidate:m,revalidateOnlyGenerated:n,resolvedPathname:o}=f,p=(0,aU.normalizeAppPath)(e),q=!!(k.dynamicRoutes[p]||k.routes[o]);if(q&&!j){let a=!!k.routes[o],b=k.dynamicRoutes[p];if(b&&!1===b.fallback&&!a)throw new a1.NoFallbackError}let r=null;!q||gl.isDev||j||(r="/index"===(r=o)?"/":r);let s=!0===gl.isDev||!q,t=q&&!s,u=a.method||"GET",v=(0,aT.getTracer)(),w=v.getActiveScopeSpan(),x={params:h,prerenderManifest:k,renderOpts:{experimental:{dynamicIO:!!i.experimental.dynamicIO,authInterrupts:!!i.experimental.authInterrupts},supportsDynamicResponse:s,incrementalCache:(0,aS.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=i.experimental)?void 0:d.cacheLife,isRevalidate:t,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>gl.onRequestError(a,b,d,l)},sharedContext:{buildId:g}},y=new aV.NodeNextRequest(a),z=new aV.NodeNextResponse(b),A=aW.NextRequestAdapter.fromNodeNextRequest(y,(0,aW.signalFromNodeResponse)(b));try{let d=async c=>gl.handle(A,x).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=v.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==aX.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${u} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${u} ${a.url}`)}),f=async f=>{var g,h;let o=async({previousCacheEntry:g})=>{try{if(!(0,aS.getRequestMeta)(a,"minimalMode")&&m&&n&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=x.renderOpts.fetchMetrics;let h=x.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let i=x.renderOpts.collectedTags;if(!q)return await (0,aZ.I)(y,z,e,x.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,a$.toNodeOutgoingHttpHeaders)(e.headers);i&&(b[a0.NEXT_CACHE_TAGS_HEADER]=i),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==x.renderOpts.collectedRevalidate&&!(x.renderOpts.collectedRevalidate>=a0.INFINITE_CACHE)&&x.renderOpts.collectedRevalidate,d=void 0===x.renderOpts.collectedExpire||x.renderOpts.collectedExpire>=a0.INFINITE_CACHE?void 0:x.renderOpts.collectedExpire;return{value:{kind:a2.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await gl.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,aY.c)({isRevalidate:t,isOnDemandRevalidate:m})},l),b}},p=await gl.handleResponse({req:a,nextConfig:i,cacheKey:r,routeKind:aQ.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:k,isRoutePPREnabled:!1,isOnDemandRevalidate:m,revalidateOnlyGenerated:n,responseGenerator:o,waitUntil:c.waitUntil});if(!q)return null;if((null==p||null==(g=p.value)?void 0:g.kind)!==a2.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==p||null==(h=p.value)?void 0:h.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,aS.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",m?"REVALIDATED":p.isMiss?"MISS":p.isStale?"STALE":"HIT"),j&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let s=(0,a$.fromNodeOutgoingHttpHeaders)(p.value.headers);return(0,aS.getRequestMeta)(a,"minimalMode")&&q||s.delete(a0.NEXT_CACHE_TAGS_HEADER),!p.cacheControl||b.getHeader("Cache-Control")||s.get("Cache-Control")||s.set("Cache-Control",(0,a_.getCacheControlHeader)(p.cacheControl)),await (0,aZ.I)(y,z,new Response(p.value.body,{headers:s,status:p.value.status||200})),null};w?await f(w):await v.withPropagatedContext(a.headers,()=>v.trace(aX.BaseServerSpan.handleRequest,{spanName:`${u} ${a.url}`,kind:aT.SpanKind.SERVER,attributes:{"http.method":u,"http.target":a.url}},f))}catch(b){if(w||await gl.onRequestError(a,b,{routerKind:"App Router",routePath:p,routeType:"route",revalidateReason:(0,aY.c)({isRevalidate:t,isOnDemandRevalidate:m})}),q)throw b;return await (0,aZ.I)(y,z,new Response(null,{status:500})),null}}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,886],()=>b(b.s=9982));module.exports=c})();