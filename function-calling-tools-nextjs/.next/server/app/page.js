(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1135:()=>{},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx","default")},1850:(a,b,c)=>{Promise.resolve().then(c.bind(c,8363)),Promise.resolve().then(c.bind(c,4903)),Promise.resolve().then(c.bind(c,8693)),Promise.resolve().then(c.bind(c,8228)),Promise.resolve().then(c.bind(c,7394)),Promise.resolve().then(c.bind(c,9100)),Promise.resolve().then(c.bind(c,4050)),Promise.resolve().then(c.bind(c,7322)),Promise.resolve().then(c.bind(c,3425)),Promise.resolve().then(c.bind(c,2030)),Promise.resolve().then(c.bind(c,6674)),Promise.resolve().then(c.bind(c,5522)),Promise.resolve().then(c.bind(c,5806))},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},4538:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,9336)),"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},4885:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},4994:(a,b,c)=>{Promise.resolve().then(c.bind(c,665)),Promise.resolve().then(c.bind(c,5465)),Promise.resolve().then(c.bind(c,1503)),Promise.resolve().then(c.bind(c,2246)),Promise.resolve().then(c.bind(c,8920)),Promise.resolve().then(c.bind(c,3250)),Promise.resolve().then(c.bind(c,5917)),Promise.resolve().then(c.bind(c,4052)),Promise.resolve().then(c.bind(c,4480)),Promise.resolve().then(c.bind(c,5080)),Promise.resolve().then(c.bind(c,5480)),Promise.resolve().then(c.bind(c,240)),Promise.resolve().then(c.bind(c,7512))},5157:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},5694:(a,b,c)=>{Promise.resolve().then(c.bind(c,1204))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7446:(a,b,c)=>{Promise.resolve().then(c.bind(c,8883))},8354:a=>{"use strict";a.exports=require("util")},8883:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>bC});var d,e=c(687),f=c(3210),g=c.t(f,2),h=Object.create,i=Object.defineProperty,j=Object.getOwnPropertyDescriptor,k=Object.getOwnPropertyNames,l=Object.getPrototypeOf,m=Object.prototype.hasOwnProperty,n=(a,b)=>function(){return b||(0,a[k(a)[0]])((b={exports:{}}).exports,b),b.exports},o=n({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(a,b){function c(a){return b.exports=c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},b.exports.__esModule=!0,b.exports.default=b.exports,c(a)}b.exports=c,b.exports.__esModule=!0,b.exports.default=b.exports}}),p=n({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(a,b){var c=o().default;b.exports=function(a,b){if("object"!=c(a)||!a)return a;var d=a[Symbol.toPrimitive];if(void 0!==d){var e=d.call(a,b||"default");if("object"!=c(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)},b.exports.__esModule=!0,b.exports.default=b.exports}}),q=n({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(a,b){var c=o().default,d=p();b.exports=function(a){var b=d(a,"string");return"symbol"==c(b)?b:b+""},b.exports.__esModule=!0,b.exports.default=b.exports}}),r=n({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(a,b){var c=q();b.exports=function(a,b,d){return(b=c(b))in a?Object.defineProperty(a,b,{value:d,enumerable:!0,configurable:!0,writable:!0}):a[b]=d,a},b.exports.__esModule=!0,b.exports.default=b.exports}}),s=n({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(a,b){var c=r();function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}b.exports=function(a){for(var b=1;b<arguments.length;b++){var e=null!=arguments[b]?arguments[b]:{};b%2?d(Object(e),!0).forEach(function(b){c(a,b,e[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):d(Object(e)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(e,b))})}return a},b.exports.__esModule=!0,b.exports.default=b.exports}}),t=c(8693),u=c(8363);function v(a){return a?"input"in a?a:{input:a,output:a}:{input:{serialize:a=>a,deserialize:a=>a},output:{serialize:a=>a,deserialize:a=>a}}}var w=c(1212);let x={INTERNAL_SERVER_ERROR:-32603,BAD_GATEWAY:-32603,SERVICE_UNAVAILABLE:-32603,GATEWAY_TIMEOUT:-32603};function y(a){return!!a&&!Array.isArray(a)&&"object"==typeof a}x.BAD_GATEWAY,x.SERVICE_UNAVAILABLE,x.GATEWAY_TIMEOUT,x.INTERNAL_SERVER_ERROR;let z="function"==typeof Symbol&&!!Symbol.asyncIterator;function A(a){return z&&y(a)&&Symbol.asyncIterator in a}var B=Object.create,C=Object.defineProperty,D=Object.getOwnPropertyDescriptor,E=Object.getOwnPropertyNames,F=Object.getPrototypeOf,G=Object.prototype.hasOwnProperty,H=(a,b)=>function(){return b||(0,a[E(a)[0]])((b={exports:{}}).exports,b),b.exports},I=(a,b,c)=>(c=null!=a?B(F(a)):{},((a,b,c,d)=>{if(b&&"object"==typeof b||"function"==typeof b)for(var e,f=E(b),g=0,h=f.length;g<h;g++)e=f[g],G.call(a,e)||e===c||C(a,e,{get:(a=>b[a]).bind(null,e),enumerable:!(d=D(b,e))||d.enumerable});return a})(!b&&a&&a.__esModule?c:C(c,"default",{value:a,enumerable:!0}),a)),J=H({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js"(a,b){b.exports=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(b.includes(d))continue;c[d]=a[d]}return c},b.exports.__esModule=!0,b.exports.default=b.exports}}),K=H({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutProperties.js"(a,b){var c=J();b.exports=function(a,b){if(null==a)return{};var d,e,f=c(a,b);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(a);for(e=0;e<g.length;e++)d=g[e],b.includes(d)||({}).propertyIsEnumerable.call(a,d)&&(f[d]=a[d])}return f},b.exports.__esModule=!0,b.exports.default=b.exports}}),L=H({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(a,b){function c(a){return b.exports=c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},b.exports.__esModule=!0,b.exports.default=b.exports,c(a)}b.exports=c,b.exports.__esModule=!0,b.exports.default=b.exports}}),M=H({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(a,b){var c=L().default;b.exports=function(a,b){if("object"!=c(a)||!a)return a;var d=a[Symbol.toPrimitive];if(void 0!==d){var e=d.call(a,b||"default");if("object"!=c(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)},b.exports.__esModule=!0,b.exports.default=b.exports}}),N=H({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(a,b){var c=L().default,d=M();b.exports=function(a){var b=d(a,"string");return"symbol"==c(b)?b:b+""},b.exports.__esModule=!0,b.exports.default=b.exports}}),O=H({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(a,b){var c=N();b.exports=function(a,b,d){return(b=c(b))in a?Object.defineProperty(a,b,{value:d,enumerable:!0,configurable:!0,writable:!0}):a[b]=d,a},b.exports.__esModule=!0,b.exports.default=b.exports}}),P=H({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(a,b){var c=O();function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}b.exports=function(a){for(var b=1;b<arguments.length;b++){var e=null!=arguments[b]?arguments[b]:{};b%2?d(Object(e),!0).forEach(function(b){c(a,b,e[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):d(Object(e)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(e,b))})}return a},b.exports.__esModule=!0,b.exports.default=b.exports}}),Q=I(K(),1),R=I(P(),1);let S=["cursor","direction"];function T(a,b,c){let d=a.flatMap(a=>a.split("."));if(!b&&(!c||"any"===c))return d.length?[d]:[];if("infinite"===c&&y(b)&&("direction"in b||"cursor"in b)){let{cursor:a,direction:c}=b;return[d,{input:(0,Q.default)(b,S),type:"infinite"}]}return[d,(0,R.default)((0,R.default)({},void 0!==b&&b!==w.hT&&{input:b}),c&&"any"!==c&&{type:c})]}function U(a){return T(a,void 0,"any")}var V=Object.create,W=Object.defineProperty,X=Object.getOwnPropertyDescriptor,Y=Object.getOwnPropertyNames,Z=Object.getPrototypeOf,$=Object.prototype.hasOwnProperty,_=(a,b)=>function(){return b||(0,a[Y(a)[0]])((b={exports:{}}).exports,b),b.exports},aa=(a,b,c)=>(c=null!=a?V(Z(a)):{},((a,b,c,d)=>{if(b&&"object"==typeof b||"function"==typeof b)for(var e,f=Y(b),g=0,h=f.length;g<h;g++)e=f[g],$.call(a,e)||e===c||W(a,e,{get:(a=>b[a]).bind(null,e),enumerable:!(d=X(b,e))||d.enumerable});return a})(!b&&a&&a.__esModule?c:W(c,"default",{value:a,enumerable:!0}),a)),ab=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(a,b){function c(a){return b.exports=c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},b.exports.__esModule=!0,b.exports.default=b.exports,c(a)}b.exports=c,b.exports.__esModule=!0,b.exports.default=b.exports}}),ac=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(a,b){var c=ab().default;b.exports=function(a,b){if("object"!=c(a)||!a)return a;var d=a[Symbol.toPrimitive];if(void 0!==d){var e=d.call(a,b||"default");if("object"!=c(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)},b.exports.__esModule=!0,b.exports.default=b.exports}}),ad=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(a,b){var c=ab().default,d=ac();b.exports=function(a){var b=d(a,"string");return"symbol"==c(b)?b:b+""},b.exports.__esModule=!0,b.exports.default=b.exports}}),ae=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(a,b){var c=ad();b.exports=function(a,b,d){return(b=c(b))in a?Object.defineProperty(a,b,{value:d,enumerable:!0,configurable:!0,writable:!0}):a[b]=d,a},b.exports.__esModule=!0,b.exports.default=b.exports}}),af=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(a,b){var c=ae();function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}b.exports=function(a){for(var b=1;b<arguments.length;b++){var e=null!=arguments[b]?arguments[b]:{};b%2?d(Object(e),!0).forEach(function(b){c(a,b,e[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):d(Object(e)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(e,b))})}return a},b.exports.__esModule=!0,b.exports.default=b.exports}});function ag(a){let b={subscribe(b){let c=null,d=!1,e=!1,f=!1;function g(){if(null===c){f=!0;return}!e&&(e=!0,"function"==typeof c?c():c&&c.unsubscribe())}return c=a({next(a){var c;d||null==(c=b.next)||c.call(b,a)},error(a){var c;d||(d=!0,null==(c=b.error)||c.call(b,a),g())},complete(){var a;d||(d=!0,null==(a=b.complete)||a.call(b),g())}}),f&&g(),{unsubscribe:g}},pipe:(...a)=>a.reduce(ah,b)};return b}function ah(a,b){return b(a)}var ai=aa(ae(),1),aj=aa(af(),1),ak=class a extends Error{constructor(b,c){var d,e;let f=null==c?void 0:c.cause;super(b,{cause:f}),(0,ai.default)(this,"cause",void 0),(0,ai.default)(this,"shape",void 0),(0,ai.default)(this,"data",void 0),(0,ai.default)(this,"meta",void 0),this.meta=null==c?void 0:c.meta,this.cause=f,this.shape=null==c||null==(d=c.result)?void 0:d.error,this.data=null==c||null==(e=c.result)?void 0:e.error.data,this.name="TRPCClientError",Object.setPrototypeOf(this,a.prototype)}static from(b,c={}){return b instanceof ak?(c.meta&&(b.meta=(0,aj.default)((0,aj.default)({},b.meta),c.meta)),b):y(b)&&y(b.error)&&"number"==typeof b.error.code&&"string"==typeof b.error.message?new a(b.error.message,(0,aj.default)((0,aj.default)({},c),{},{result:b})):new a("string"==typeof b?b:y(b)&&"string"==typeof b.message?b.message:"Unknown error",(0,aj.default)((0,aj.default)({},c),{},{cause:b}))}},al=aa(af(),1);let am={query:"GET",mutation:"POST",subscription:"PATCH"};function an(a){return"input"in a?a.transformer.input.serialize(a.input):function(a){let b={};for(let c=0;c<a.length;c++){let d=a[c];b[c]=d}return b}(a.inputs.map(b=>a.transformer.input.serialize(b)))}let ao=a=>{let b=a.url.split("?"),c=b[0].replace(/\/$/,"")+"/"+a.path,d=[];if(b[1]&&d.push(b[1]),"inputs"in a&&d.push("batch=1"),"query"===a.type||"subscription"===a.type){let b=an(a);void 0!==b&&"POST"!==a.methodOverride&&d.push(`input=${encodeURIComponent(JSON.stringify(b))}`)}return d.length&&(c+="?"+d.join("&")),c},ap=a=>{if("query"===a.type&&"POST"!==a.methodOverride)return;let b=an(a);return void 0!==b?JSON.stringify(b):void 0};var aq=class extends Error{constructor(){let a="AbortError";super(a),this.name=a,this.message=a}};async function ar(a){var b,c,d=a.signal;if(null==d?void 0:d.aborted){if(null==(c=d.throwIfAborted)||c.call(d),"undefined"!=typeof DOMException)throw new DOMException("AbortError","AbortError");throw new aq}let e=a.getUrl(a),f=a.getBody(a),{type:g}=a,h=await (async()=>{let b=await a.headers();return Symbol.iterator in b?Object.fromEntries(b):b})(),i=(0,al.default)((0,al.default)((0,al.default)({},a.contentTypeHeader?{"content-type":a.contentTypeHeader}:{}),a.trpcAcceptHeader?{"trpc-accept":a.trpcAcceptHeader}:void 0),h);return(function(a){if(a)return a;if("undefined"!=typeof window&&"function"==typeof window.fetch)return window.fetch;if("undefined"!=typeof globalThis&&"function"==typeof globalThis.fetch)return globalThis.fetch;throw Error("No fetch implementation found")})(a.fetch)(e,{method:null!=(b=a.methodOverride)?b:am[g],signal:a.signal,body:f,headers:i})}async function as(a){let b={},c=await ar(a);b.response=c;let d=await c.json();return b.responseJSON=d,{json:d,meta:b}}aa(af(),1);var at=Object.create,au=Object.defineProperty,av=Object.getOwnPropertyDescriptor,aw=Object.getOwnPropertyNames,ax=Object.getPrototypeOf,ay=Object.prototype.hasOwnProperty,az=(a,b)=>function(){return b||(0,a[aw(a)[0]])((b={exports:{}}).exports,b),b.exports},aA=(a,b,c)=>(c=null!=a?at(ax(a)):{},((a,b,c,d)=>{if(b&&"object"==typeof b||"function"==typeof b)for(var e,f=aw(b),g=0,h=f.length;g<h;g++)e=f[g],ay.call(a,e)||e===c||au(a,e,{get:(a=>b[a]).bind(null,e),enumerable:!(d=av(b,e))||d.enumerable});return a})(!b&&a&&a.__esModule?c:au(c,"default",{value:a,enumerable:!0}),a));let aB=()=>{},aC=a=>{Object.freeze&&Object.freeze(a)},aD=a=>(function a(b,c,d){let e=c.join(".");return null!=d[e]||(d[e]=new Proxy(aB,{get(e,f){if("string"==typeof f&&"then"!==f)return a(b,[...c,f],d)},apply(a,d,e){let f=c[c.length-1],g={args:e,path:c};return"call"===f?g={args:e.length>=2?[e[1]]:[],path:c.slice(0,-1)}:"apply"===f&&(g={args:e.length>=2?e[1]:[],path:c.slice(0,-1)}),aC(g.args),aC(g.path),b(g)}})),d[e]})(a,[],Object.create(null)),aE=a=>new Proxy(aB,{get(b,c){if("then"!==c)return a(c)}});var aF=az({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(a,b){function c(a){return b.exports=c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},b.exports.__esModule=!0,b.exports.default=b.exports,c(a)}b.exports=c,b.exports.__esModule=!0,b.exports.default=b.exports}}),aG=az({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(a,b){var c=aF().default;b.exports=function(a,b){if("object"!=c(a)||!a)return a;var d=a[Symbol.toPrimitive];if(void 0!==d){var e=d.call(a,b||"default");if("object"!=c(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)},b.exports.__esModule=!0,b.exports.default=b.exports}}),aH=az({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(a,b){var c=aF().default,d=aG();b.exports=function(a){var b=d(a,"string");return"symbol"==c(b)?b:b+""},b.exports.__esModule=!0,b.exports.default=b.exports}}),aI=az({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(a,b){var c=aH();b.exports=function(a,b,d){return(b=c(b))in a?Object.defineProperty(a,b,{value:d,enumerable:!0,configurable:!0,writable:!0}):a[b]=d,a},b.exports.__esModule=!0,b.exports.default=b.exports}}),aJ=az({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(a,b){var c=aI();function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}b.exports=function(a){for(var b=1;b<arguments.length;b++){var e=null!=arguments[b]?arguments[b]:{};b%2?d(Object(e),!0).forEach(function(b){c(a,b,e[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):d(Object(e)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(e,b))})}return a},b.exports.__esModule=!0,b.exports.default=b.exports}});aA(aJ(),1),aA(aI(),1);var aK=aA(aJ(),1),aL=class extends Error{constructor(){super("Unable to transform response from server")}};aA(aJ(),1),Symbol("lazy"),Symbol();let aM=()=>{throw Error("Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new")};function aN(a){let b=null,c=null;function d(){let d=function(b){let c=[[]],d=0;for(;;){var e,f;let g=b[d];if(!g)break;let h=c[c.length-1];if(g.aborted){null==(e=g.reject)||e.call(g,Error("Aborted")),d++;continue}if(a.validate(h.concat(g).map(a=>a.key))){h.push(g),d++;continue}if(0===h.length){null==(f=g.reject)||f.call(g,Error("Input is too big for a single dispatch")),d++;continue}c.push([])}return c}(b);for(let e of(clearTimeout(c),c=null,b=null,d)){if(!e.length)continue;let b={items:e};for(let a of e)a.batch=b;a.fetch(b.items.map(a=>a.key)).then(async a=>{for(let d of(await Promise.all(a.map(async(a,c)=>{var d,e;let f=b.items[c];try{let b=await Promise.resolve(a);null==(d=f.resolve)||d.call(f,b)}catch(a){null==(e=f.reject)||e.call(f,a)}f.batch=null,f.reject=null,f.resolve=null})),b.items)){var c;null==(c=d.reject)||c.call(d,Error("Missing result")),d.batch=null}}).catch(a=>{for(let d of b.items){var c;null==(c=d.reject)||c.call(d,a),d.batch=null}})}}return{load:function(a){let e={aborted:!1,key:a,batch:null,resolve:aM,reject:aM},f=new Promise((a,c)=>{e.reject=c,e.resolve=a,null!=b||(b=[]),b.push(e)});return null!=c||(c=setTimeout(d)),f}}}var aO=aa(af(),1);aa(af(),1),Symbol();let aP=(a,...b)=>"function"==typeof a?a(...b):a;async function aQ(a){let b=await aP(a.url);if(!a.connectionParams)return b;let c=b.includes("?")?"&":"?";return b+`${c}connectionParams=1`}async function aR(a){return JSON.stringify({method:"connectionParams",data:await aP(a)})}aa(ae(),1),aa(ae(),1);var aS=aa(ae(),1),aT=class a{constructor(b){var c;if((0,aS.default)(this,"id",++a.connectCount),(0,aS.default)(this,"WebSocketPonyfill",void 0),(0,aS.default)(this,"urlOptions",void 0),(0,aS.default)(this,"keepAliveOpts",void 0),(0,aS.default)(this,"wsObservable",function(a){let b=null,c=[],d=ag(a=>(void 0!==b&&a.next(b),c.push(a),()=>{c.splice(c.indexOf(a),1)}));return d.next=a=>{if(b!==a)for(let d of(b=a,c))d.next(a)},d.get=()=>b,d}(0)),(0,aS.default)(this,"openPromise",null),this.WebSocketPonyfill=null!=(c=b.WebSocketPonyfill)?c:WebSocket,!this.WebSocketPonyfill)throw Error("No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill");this.urlOptions=b.urlOptions,this.keepAliveOpts=b.keepAlive}get ws(){return this.wsObservable.get()}set ws(a){this.wsObservable.next(a)}isOpen(){return!!this.ws&&this.ws.readyState===this.WebSocketPonyfill.OPEN&&!this.openPromise}isClosed(){return!!this.ws&&(this.ws.readyState===this.WebSocketPonyfill.CLOSING||this.ws.readyState===this.WebSocketPonyfill.CLOSED)}async open(){var b=this;if(b.openPromise)return b.openPromise;b.id=++a.connectCount;let c=aQ(b.urlOptions).then(a=>new b.WebSocketPonyfill(a));b.openPromise=c.then(async a=>{b.ws=a,a.addEventListener("message",function({data:a}){"PING"===a&&this.send("PONG")}),b.keepAliveOpts.enabled&&function(a,{intervalMs:b,pongTimeoutMs:c}){let d,e;function f(){d=setTimeout(()=>{a.send("PING"),e=setTimeout(()=>{a.close()},c)},b)}a.addEventListener("open",f),a.addEventListener("message",({data:a})=>{clearTimeout(d),f(),"PONG"===a&&(clearTimeout(e),clearTimeout(d),f())}),a.addEventListener("close",()=>{clearTimeout(d),clearTimeout(e)})}(a,b.keepAliveOpts),a.addEventListener("close",()=>{b.ws===a&&(b.ws=null)}),await function(a){let b,c,{promise:d,resolve:e,reject:f}={promise:new Promise((a,d)=>{b=a,c=d}),resolve:b,reject:c};return a.addEventListener("open",()=>{a.removeEventListener("error",f),e()}),a.addEventListener("error",f),d}(a),b.urlOptions.connectionParams&&a.send(await aR(b.urlOptions.connectionParams))});try{await b.openPromise}finally{b.openPromise=null}}async close(){var a;try{await this.openPromise}finally{null==(a=this.ws)||a.close()}}};(0,aS.default)(aT,"connectCount",0),aa(ae(),1),aa(af(),1);var aU=aa(ae(),1),aV=aa(af(),1),aW=class{constructor(a){(0,aU.default)(this,"links",void 0),(0,aU.default)(this,"runtime",void 0),(0,aU.default)(this,"requestId",void 0),this.requestId=0,this.runtime={},this.links=a.links.map(a=>a(this.runtime))}$request(a){var b,c;return(c={links:this.links,op:(0,aV.default)((0,aV.default)({},a),{},{context:null!=(b=a.context)?b:{},id:++this.requestId})},ag(a=>(function a(b=0,d=c.op){let e=c.links[b];if(!e)throw Error("No more links to execute - did you forget to add an ending link?");return e({op:d,next:c=>a(b+1,c)})})().subscribe(a))).pipe(a=>{let b=0,c=null,d=[];return ag(e=>(b++,d.push(e),c||(c=a.subscribe({next(a){for(let c of d){var b;null==(b=c.next)||b.call(c,a)}},error(a){for(let c of d){var b;null==(b=c.error)||b.call(c,a)}},complete(){for(let b of d){var a;null==(a=b.complete)||a.call(b)}}})),{unsubscribe(){if(0==--b&&c){let a=c;c=null,a.unsubscribe()}let a=d.findIndex(a=>a===e);a>-1&&d.splice(a,1)}}))})}async requestAsPromise(a){try{let b=this.$request(a);return(await function(a){let b=new AbortController;return new Promise((c,d)=>{let e=!1;function f(){e||(e=!0,g.unsubscribe())}b.signal.addEventListener("abort",()=>{d(b.signal.reason)});let g=a.subscribe({next(a){e=!0,c(a),f()},error(a){d(a)},complete(){b.abort(),f()}})})}(b)).result.data}catch(a){throw ak.from(a)}}query(a,b,c){return this.requestAsPromise({type:"query",path:a,input:b,context:null==c?void 0:c.context,signal:null==c?void 0:c.signal})}mutation(a,b,c){return this.requestAsPromise({type:"mutation",path:a,input:b,context:null==c?void 0:c.context,signal:null==c?void 0:c.signal})}subscription(a,b,c){return this.$request({type:"subscription",path:a,input:b,context:c.context,signal:c.signal}).subscribe({next(a){var b,d,e,f;switch(a.result.type){case"state":null==(b=c.onConnectionStateChange)||b.call(c,a.result);break;case"started":null==(d=c.onStarted)||d.call(c,{context:a.context});break;case"stopped":null==(e=c.onStopped)||e.call(c);break;case"data":case void 0:null==(f=c.onData)||f.call(c,a.result.data)}},error(a){var b;null==(b=c.onError)||b.call(c,a)},complete(){var a;null==(a=c.onComplete)||a.call(c)}})}};let aX=Symbol.for("trpc_untypedClient"),aY={query:"query",mutate:"mutation",subscribe:"subscription"};function aZ(a){let b=aD(({path:b,args:c})=>{let d=[...b],e=aY[d.pop()],f=d.join(".");return a[e](f,...c)});return aE(c=>c===aX?a:b[c])}function a$(a){return aZ(new aW(a))}aa(af(),1),aa(af(),1),aa(_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(a,b){function c(a){function b(a){if(Object(a)!==a)return Promise.reject(TypeError(a+" is not an object."));var b=a.done;return Promise.resolve(a.value).then(function(a){return{value:a,done:b}})}return(c=function(a){this.s=a,this.n=a.next}).prototype={s:null,n:null,next:function(){return b(this.n.apply(this.s,arguments))},return:function(a){var c=this.s.return;return void 0===c?Promise.resolve({value:a,done:!0}):b(c.apply(this.s,arguments))},throw:function(a){var c=this.s.return;return void 0===c?Promise.reject(a):b(c.apply(this.s,arguments))}},new c(a)}b.exports=function(a){var b,d,e,f=2;for("undefined"!=typeof Symbol&&(d=Symbol.asyncIterator,e=Symbol.iterator);f--;){if(d&&null!=(b=a[d]))return b.call(a);if(e&&null!=(b=a[e]))return new c(b.call(a));d="@@asyncIterator",e="@@iterator"}throw TypeError("Object is not async iterable")},b.exports.__esModule=!0,b.exports.default=b.exports}})(),1),aa(af(),1);var a_=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js"(a,b){b.exports=function(){var a="function"==typeof SuppressedError?SuppressedError:function(a,b){var c=Error();return c.name="SuppressedError",c.error=a,c.suppressed=b,c},b={},c=[];function d(a,b){if(null!=b){if(Object(b)!==b)throw TypeError("using declarations can only be used with objects, functions, null, or undefined.");if(a)var d=b[Symbol.asyncDispose||Symbol.for("Symbol.asyncDispose")];if(void 0===d&&(d=b[Symbol.dispose||Symbol.for("Symbol.dispose")],a))var e=d;if("function"!=typeof d)throw TypeError("Object is not disposable.");e&&(d=function(){try{e.call(b)}catch(a){return Promise.reject(a)}}),c.push({v:b,d:d,a:a})}else a&&c.push({d:b,a:a});return b}return{e:b,u:d.bind(null,!1),a:d.bind(null,!0),d:function(){var d,e=this.e,f=0;function g(){for(;d=c.pop();)try{if(!d.a&&1===f)return f=0,c.push(d),Promise.resolve().then(g);if(d.d){var a=d.d.call(d.v);if(d.a)return f|=2,Promise.resolve(a).then(g,h)}else f|=1}catch(a){return h(a)}if(1===f)return e!==b?Promise.reject(e):Promise.resolve();if(e!==b)throw e}function h(c){return e=e!==b?new a(c,e):c,g()}return g()}}},b.exports.__esModule=!0,b.exports.default=b.exports}}),a0=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js"(a,b){b.exports=function(a,b){this.v=a,this.k=b},b.exports.__esModule=!0,b.exports.default=b.exports}}),a1=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js"(a,b){var c=a0();b.exports=function(a){return new c(a,0)},b.exports.__esModule=!0,b.exports.default=b.exports}}),a2=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js"(a,b){var c=a0();function d(a){var b,d;function e(b,d){try{var g=a[b](d),h=g.value,i=h instanceof c;Promise.resolve(i?h.v:h).then(function(c){if(i){var d="return"===b?"return":"next";if(!h.k||c.done)return e(d,c);c=a[d](c).value}f(g.done?"return":"normal",c)},function(a){e("throw",a)})}catch(a){f("throw",a)}}function f(a,c){switch(a){case"return":b.resolve({value:c,done:!0});break;case"throw":b.reject(c);break;default:b.resolve({value:c,done:!1})}(b=b.next)?e(b.key,b.arg):d=null}this._invoke=function(a,c){return new Promise(function(f,g){var h={key:a,arg:c,resolve:f,reject:g,next:null};d?d=d.next=h:(b=d=h,e(a,c))})},"function"!=typeof a.return&&(this.return=void 0)}d.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},d.prototype.next=function(a){return this._invoke("next",a)},d.prototype.throw=function(a){return this._invoke("throw",a)},d.prototype.return=function(a){return this._invoke("return",a)},b.exports=function(a){return function(){return new d(a.apply(this,arguments))}},b.exports.__esModule=!0,b.exports.default=b.exports}});aa(a_(),1),aa(a1(),1),aa(a2(),1),aa(af(),1);var a3=c(2030),a4=c(5806),a5=c(4050),a6=c(7394),a7=c(6674),a8=c(3425),a9=c(5522),ba=c(1489),bb=c(3465),bc=c(5536),bd=class extends bc.Q{constructor(a={}){super(),this.config=a,this.#a=new Map}#a;build(a,b,c){let d=b.queryKey,e=b.queryHash??(0,w.F$)(d,b),f=this.get(e);return f||(f=new ba.X({client:a,queryKey:d,queryHash:e,options:a.defaultQueryOptions(b),state:c,defaultOptions:a.getQueryDefaults(d)}),this.add(f)),f}add(a){this.#a.has(a.queryHash)||(this.#a.set(a.queryHash,a),this.notify({type:"added",query:a}))}remove(a){let b=this.#a.get(a.queryHash);b&&(a.destroy(),b===a&&this.#a.delete(a.queryHash),this.notify({type:"removed",query:a}))}clear(){bb.jG.batch(()=>{this.getAll().forEach(a=>{this.remove(a)})})}get(a){return this.#a.get(a)}getAll(){return[...this.#a.values()]}find(a){let b={exact:!0,...a};return this.getAll().find(a=>(0,w.MK)(b,a))}findAll(a={}){let b=this.getAll();return Object.keys(a).length>0?b.filter(b=>(0,w.MK)(a,b)):b}notify(a){bb.jG.batch(()=>{this.listeners.forEach(b=>{b(a)})})}onFocus(){bb.jG.batch(()=>{this.getAll().forEach(a=>{a.onFocus()})})}onOnline(){bb.jG.batch(()=>{this.getAll().forEach(a=>{a.onOnline()})})}},be=c(5406),bf=class extends bc.Q{constructor(a={}){super(),this.config=a,this.#b=new Set,this.#c=new Map,this.#d=0}#b;#c;#d;build(a,b,c){let d=new be.s({mutationCache:this,mutationId:++this.#d,options:a.defaultMutationOptions(b),state:c});return this.add(d),d}add(a){this.#b.add(a);let b=bg(a);if("string"==typeof b){let c=this.#c.get(b);c?c.push(a):this.#c.set(b,[a])}this.notify({type:"added",mutation:a})}remove(a){if(this.#b.delete(a)){let b=bg(a);if("string"==typeof b){let c=this.#c.get(b);if(c)if(c.length>1){let b=c.indexOf(a);-1!==b&&c.splice(b,1)}else c[0]===a&&this.#c.delete(b)}}this.notify({type:"removed",mutation:a})}canRun(a){let b=bg(a);if("string"!=typeof b)return!0;{let c=this.#c.get(b),d=c?.find(a=>"pending"===a.state.status);return!d||d===a}}runNext(a){let b=bg(a);if("string"!=typeof b)return Promise.resolve();{let c=this.#c.get(b)?.find(b=>b!==a&&b.state.isPaused);return c?.continue()??Promise.resolve()}}clear(){bb.jG.batch(()=>{this.#b.forEach(a=>{this.notify({type:"removed",mutation:a})}),this.#b.clear(),this.#c.clear()})}getAll(){return Array.from(this.#b)}find(a){let b={exact:!0,...a};return this.getAll().find(a=>(0,w.nJ)(b,a))}findAll(a={}){return this.getAll().filter(b=>(0,w.nJ)(a,b))}notify(a){bb.jG.batch(()=>{this.listeners.forEach(b=>{b(a)})})}resumePausedMutations(){let a=this.getAll().filter(a=>a.state.isPaused);return bb.jG.batch(()=>Promise.all(a.map(a=>a.continue().catch(w.lQ))))}};function bg(a){return a.options.scope?.id}var bh=c(9850),bi=c(2115),bj=c(8575),bk=class{#e;#f;#g;#h;#i;#j;#k;#l;constructor(a={}){this.#e=a.queryCache||new bd,this.#f=a.mutationCache||new bf,this.#g=a.defaultOptions||{},this.#h=new Map,this.#i=new Map,this.#j=0}mount(){this.#j++,1===this.#j&&(this.#k=bh.m.subscribe(async a=>{a&&(await this.resumePausedMutations(),this.#e.onFocus())}),this.#l=bi.t.subscribe(async a=>{a&&(await this.resumePausedMutations(),this.#e.onOnline())}))}unmount(){this.#j--,0===this.#j&&(this.#k?.(),this.#k=void 0,this.#l?.(),this.#l=void 0)}isFetching(a){return this.#e.findAll({...a,fetchStatus:"fetching"}).length}isMutating(a){return this.#f.findAll({...a,status:"pending"}).length}getQueryData(a){let b=this.defaultQueryOptions({queryKey:a});return this.#e.get(b.queryHash)?.state.data}ensureQueryData(a){let b=this.defaultQueryOptions(a),c=this.#e.build(this,b),d=c.state.data;return void 0===d?this.fetchQuery(a):(a.revalidateIfStale&&c.isStaleByTime((0,w.d2)(b.staleTime,c))&&this.prefetchQuery(b),Promise.resolve(d))}getQueriesData(a){return this.#e.findAll(a).map(({queryKey:a,state:b})=>[a,b.data])}setQueryData(a,b,c){let d=this.defaultQueryOptions({queryKey:a}),e=this.#e.get(d.queryHash),f=e?.state.data,g=(0,w.Zw)(b,f);if(void 0!==g)return this.#e.build(this,d).setData(g,{...c,manual:!0})}setQueriesData(a,b,c){return bb.jG.batch(()=>this.#e.findAll(a).map(({queryKey:a})=>[a,this.setQueryData(a,b,c)]))}getQueryState(a){let b=this.defaultQueryOptions({queryKey:a});return this.#e.get(b.queryHash)?.state}removeQueries(a){let b=this.#e;bb.jG.batch(()=>{b.findAll(a).forEach(a=>{b.remove(a)})})}resetQueries(a,b){let c=this.#e;return bb.jG.batch(()=>(c.findAll(a).forEach(a=>{a.reset()}),this.refetchQueries({type:"active",...a},b)))}cancelQueries(a,b={}){let c={revert:!0,...b};return Promise.all(bb.jG.batch(()=>this.#e.findAll(a).map(a=>a.cancel(c)))).then(w.lQ).catch(w.lQ)}invalidateQueries(a,b={}){return bb.jG.batch(()=>(this.#e.findAll(a).forEach(a=>{a.invalidate()}),a?.refetchType==="none")?Promise.resolve():this.refetchQueries({...a,type:a?.refetchType??a?.type??"active"},b))}refetchQueries(a,b={}){let c={...b,cancelRefetch:b.cancelRefetch??!0};return Promise.all(bb.jG.batch(()=>this.#e.findAll(a).filter(a=>!a.isDisabled()&&!a.isStatic()).map(a=>{let b=a.fetch(void 0,c);return c.throwOnError||(b=b.catch(w.lQ)),"paused"===a.state.fetchStatus?Promise.resolve():b}))).then(w.lQ)}fetchQuery(a){let b=this.defaultQueryOptions(a);void 0===b.retry&&(b.retry=!1);let c=this.#e.build(this,b);return c.isStaleByTime((0,w.d2)(b.staleTime,c))?c.fetch(b):Promise.resolve(c.state.data)}prefetchQuery(a){return this.fetchQuery(a).then(w.lQ).catch(w.lQ)}fetchInfiniteQuery(a){return a.behavior=(0,bj.PL)(a.pages),this.fetchQuery(a)}prefetchInfiniteQuery(a){return this.fetchInfiniteQuery(a).then(w.lQ).catch(w.lQ)}ensureInfiniteQueryData(a){return a.behavior=(0,bj.PL)(a.pages),this.ensureQueryData(a)}resumePausedMutations(){return bi.t.isOnline()?this.#f.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#e}getMutationCache(){return this.#f}getDefaultOptions(){return this.#g}setDefaultOptions(a){this.#g=a}setQueryDefaults(a,b){this.#h.set((0,w.EN)(a),{queryKey:a,defaultOptions:b})}getQueryDefaults(a){let b=[...this.#h.values()],c={};return b.forEach(b=>{(0,w.Cp)(a,b.queryKey)&&Object.assign(c,b.defaultOptions)}),c}setMutationDefaults(a,b){this.#i.set((0,w.EN)(a),{mutationKey:a,defaultOptions:b})}getMutationDefaults(a){let b=[...this.#i.values()],c={};return b.forEach(b=>{(0,w.Cp)(a,b.mutationKey)&&Object.assign(c,b.defaultOptions)}),c}defaultQueryOptions(a){if(a._defaulted)return a;let b={...this.#g.queries,...this.getQueryDefaults(a.queryKey),...a,_defaulted:!0};return b.queryHash||(b.queryHash=(0,w.F$)(b.queryKey,b)),void 0===b.refetchOnReconnect&&(b.refetchOnReconnect="always"!==b.networkMode),void 0===b.throwOnError&&(b.throwOnError=!!b.suspense),!b.networkMode&&b.persister&&(b.networkMode="offlineFirst"),b.queryFn===w.hT&&(b.enabled=!1),b}defaultMutationOptions(a){return a?._defaulted?a:{...this.#g.mutations,...a?.mutationKey&&this.getMutationDefaults(a.mutationKey),...a,_defaulted:!0}}clear(){this.#e.clear(),this.#f.clear()}};let bl=["client","ssrContext","ssrState","abortOnUnmount"],bm=null==(d=f.createContext)?void 0:d.call(g,null);var bn=I(P(),1);function bo(a){let b=a instanceof aW?a:a[aX];return aD(a=>{let c=a.path,d=c.join("."),[e,f]=a.args;return(0,bn.default)({queryKey:T(c,e,"query"),queryFn:()=>b.query(d,e,null==f?void 0:f.trpc)},f)})}var bp=I(P(),1);function bq(a,b,c){var d,e;let f=a[0],g=null==(d=a[1])?void 0:d.input;return c&&(g=(0,bp.default)((0,bp.default)((0,bp.default)({},null!=(e=g)?e:{}),c.pageParam?{cursor:c.pageParam}:{}),{},{direction:c.direction})),[f.join("."),g,null==b?void 0:b.trpc]}var br=I(H({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(a,b){function c(a){function b(a){if(Object(a)!==a)return Promise.reject(TypeError(a+" is not an object."));var b=a.done;return Promise.resolve(a.value).then(function(a){return{value:a,done:b}})}return(c=function(a){this.s=a,this.n=a.next}).prototype={s:null,n:null,next:function(){return b(this.n.apply(this.s,arguments))},return:function(a){var c=this.s.return;return void 0===c?Promise.resolve({value:a,done:!0}):b(c.apply(this.s,arguments))},throw:function(a){var c=this.s.return;return void 0===c?Promise.reject(a):b(c.apply(this.s,arguments))}},new c(a)}b.exports=function(a){var b,d,e,f=2;for("undefined"!=typeof Symbol&&(d=Symbol.asyncIterator,e=Symbol.iterator);f--;){if(d&&null!=(b=a[d]))return b.call(a);if(e&&null!=(b=a[e]))return new c(b.call(a));d="@@asyncIterator",e="@@iterator"}throw TypeError("Object is not async iterable")},b.exports.__esModule=!0,b.exports.default=b.exports}})(),1);function bs(a){return{path:a.path.join(".")}}function bt(a){let b=bs(a);return f.useMemo(()=>b,[b])}async function bu(a,b,c){let d=b.getQueryCache().build(b,{queryKey:c});d.setState({data:[],status:"success"});let e=[];var f=!1,g=!1;try{for(var h,i,j=(0,br.default)(a);f=!(i=await j.next()).done;f=!1){let a=i.value;e.push(a),d.setState({data:[...e]})}}catch(a){g=!0,h=a}finally{try{f&&null!=j.return&&await j.return()}finally{if(g)throw h}}return e}var bv=I(P(),1),bw=I(P());let bx=(a,b)=>new Proxy(a,{get:(a,c)=>(b(c),a[c])});function by(a){var b,c,d;let g=null!=(b=null==a||null==(c=a.overrides)||null==(c=c.useMutation)?void 0:c.onSuccess)?b:a=>a.originalFn(),h=null!=(d=null==a?void 0:a.context)?d:bm;function i(){let a=f.useContext(h);if(!a)throw Error("Unable to find tRPC Context. Did you forget to wrap your App inside `withTRPC` HoC?");return a}function j(a,b){var c;let{queryClient:d,ssrState:e}=i();return e&&"mounted"!==e&&(null==(c=d.getQueryCache().find({queryKey:a}))?void 0:c.state.status)==="error"?(0,bw.default)({retryOnMount:!1},b):b}let k={data:void 0,error:null,status:"idle"},l={data:void 0,error:null,status:"connecting"};return{Provider:a=>{var b;let{abortOnUnmount:c=!1,queryClient:d,ssrContext:g}=a,[i,j]=f.useState(null!=(b=a.ssrState)&&b),k=a.client instanceof aW?a.client:a.client[aX],l=f.useMemo(()=>(function(a){let{client:b,queryClient:c}=a,d=b instanceof aW?b:b[aX];return{infiniteQueryOptions:(a,b,c)=>{var e,f;let g=(null==(e=b[1])?void 0:e.input)===w.hT,h=async a=>{var e;let f=(0,bv.default)((0,bv.default)({},c),{},{trpc:(0,bv.default)((0,bv.default)({},null==c?void 0:c.trpc),(null==c||null==(e=c.trpc)?void 0:e.abortOnUnmount)?{signal:a.signal}:{signal:null})});return await d.query(...bq(b,f,{direction:a.direction,pageParam:a.pageParam}))};return Object.assign((0,bv.default)((0,bv.default)({},c),{},{initialData:null==c?void 0:c.initialData,queryKey:b,queryFn:g?w.hT:h,initialPageParam:null!=(f=null==c?void 0:c.initialCursor)?f:null}),{trpc:bs({path:a})})},queryOptions:(a,b,e)=>{var f;let g=(null==(f=b[1])?void 0:f.input)===w.hT,h=async a=>{var f;let g=(0,bv.default)((0,bv.default)({},e),{},{trpc:(0,bv.default)((0,bv.default)({},null==e?void 0:e.trpc),(null==e||null==(f=e.trpc)?void 0:f.abortOnUnmount)?{signal:a.signal}:{signal:null})}),h=await d.query(...bq(b,g));return A(h)?bu(h,c,b):h};return Object.assign((0,bv.default)((0,bv.default)({},e),{},{initialData:null==e?void 0:e.initialData,queryKey:b,queryFn:g?w.hT:h}),{trpc:bs({path:a})})},fetchQuery:(a,b)=>c.fetchQuery((0,bv.default)((0,bv.default)({},b),{},{queryKey:a,queryFn:()=>d.query(...bq(a,b))})),fetchInfiniteQuery:(a,b)=>{var e;return c.fetchInfiniteQuery((0,bv.default)((0,bv.default)({},b),{},{queryKey:a,queryFn:({pageParam:c,direction:e})=>d.query(...bq(a,b,{pageParam:c,direction:e})),initialPageParam:null!=(e=null==b?void 0:b.initialCursor)?e:null}))},prefetchQuery:(a,b)=>c.prefetchQuery((0,bv.default)((0,bv.default)({},b),{},{queryKey:a,queryFn:()=>d.query(...bq(a,b))})),prefetchInfiniteQuery:(a,b)=>{var e;return c.prefetchInfiniteQuery((0,bv.default)((0,bv.default)({},b),{},{queryKey:a,queryFn:({pageParam:c,direction:e})=>d.query(...bq(a,b,{pageParam:c,direction:e})),initialPageParam:null!=(e=null==b?void 0:b.initialCursor)?e:null}))},ensureQueryData:(a,b)=>c.ensureQueryData((0,bv.default)((0,bv.default)({},b),{},{queryKey:a,queryFn:()=>d.query(...bq(a,b))})),invalidateQueries:(a,b,d)=>c.invalidateQueries((0,bv.default)((0,bv.default)({},b),{},{queryKey:a}),d),resetQueries:(a,b,d)=>c.resetQueries((0,bv.default)((0,bv.default)({},b),{},{queryKey:a}),d),refetchQueries:(a,b,d)=>c.refetchQueries((0,bv.default)((0,bv.default)({},b),{},{queryKey:a}),d),cancelQuery:(a,b)=>c.cancelQueries({queryKey:a},b),setQueryData:(a,b,d)=>c.setQueryData(a,b,d),setQueriesData:(a,b,d,e)=>c.setQueriesData((0,bv.default)((0,bv.default)({},b),{},{queryKey:a}),d,e),getQueryData:a=>c.getQueryData(a),setInfiniteQueryData:(a,b,d)=>c.setQueryData(a,b,d),getInfiniteQueryData:a=>c.getQueryData(a),setMutationDefaults:(b,e)=>{let f=b[0];return c.setMutationDefaults(b,"function"==typeof e?e({canonicalMutationFn:b=>d.mutation(...bq([f,{input:b}],a))}):e)},getMutationDefaults:a=>c.getMutationDefaults(a),isMutating:a=>c.isMutating((0,bv.default)((0,bv.default)({},a),{},{exact:!0}))}})({client:k,queryClient:d}),[k,d]),m=f.useMemo(()=>(0,bw.default)({abortOnUnmount:c,queryClient:d,client:k,ssrContext:null!=g?g:null,ssrState:i},l),[c,k,l,d,g,i]);return f.useEffect(()=>{j(a=>!!a&&"mounted")},[]),(0,e.jsx)(h.Provider,{value:m,children:a.children})},createClient:a$,useContext:i,useUtils:i,useQuery:function(b,c,d){var e,f,g,h,k;let{abortOnUnmount:l,client:m,ssrState:n,queryClient:o,prefetchQuery:p}=i(),q=T(b,c,"query"),r=o.getQueryDefaults(q),s=c===w.hT;"undefined"!=typeof window||"prepass"!==n||(null==d||null==(e=d.trpc)?void 0:e.ssr)===!1||(null!=(f=null==d?void 0:d.enabled)?f:null==r?void 0:r.enabled)===!1||s||o.getQueryCache().find({queryKey:q})||p(q,d);let t=j(q,(0,bw.default)((0,bw.default)({},r),d)),u=null!=(g=null!=(h=null==d||null==(k=d.trpc)?void 0:k.abortOnUnmount)?h:null==a?void 0:a.abortOnUnmount)?g:l,v=(0,a3.useQuery)((0,bw.default)((0,bw.default)({},t),{},{queryKey:q,queryFn:s?c:async a=>{let b=(0,bw.default)((0,bw.default)({},t),{},{trpc:(0,bw.default)((0,bw.default)({},null==t?void 0:t.trpc),u?{signal:a.signal}:{signal:null})}),c=await m.query(...bq(q,b));return A(c)?bu(c,o,q):c}}),o);return v.trpc=bt({path:b}),v},usePrefetchQuery:function(b,c,d){var e,f,g;let h=i(),j=T(b,c,"query"),k=c===w.hT,l=null!=(e=null!=(f=null==d||null==(g=d.trpc)?void 0:g.abortOnUnmount)?f:null==a?void 0:a.abortOnUnmount)?e:h.abortOnUnmount;!function(a,b){let c=(0,t.useQueryClient)(void 0);c.getQueryState(a.queryKey)||c.prefetchQuery(a)}((0,bw.default)((0,bw.default)({},d),{},{queryKey:j,queryFn:k?c:a=>{let b={trpc:(0,bw.default)((0,bw.default)({},null==d?void 0:d.trpc),l?{signal:a.signal}:{})};return h.client.query(...bq(j,b))}}))},useSuspenseQuery:function(b,c,d){var e,f,g;let h=i(),j=T(b,c,"query"),k=null!=(e=null!=(f=null==d||null==(g=d.trpc)?void 0:g.abortOnUnmount)?f:null==a?void 0:a.abortOnUnmount)?e:h.abortOnUnmount,l=(0,a4.useSuspenseQuery)((0,bw.default)((0,bw.default)({},d),{},{queryKey:j,queryFn:a=>{let b=(0,bw.default)((0,bw.default)({},d),{},{trpc:(0,bw.default)((0,bw.default)({},null==d?void 0:d.trpc),k?{signal:a.signal}:{signal:null})});return h.client.query(...bq(j,b))}}),h.queryClient);return l.trpc=bt({path:b}),[l.data,l]},useQueries:(a,b)=>{let{ssrState:c,queryClient:d,prefetchQuery:e,client:f}=i(),g=a(bo(f));if("undefined"==typeof window&&"prepass"===c)for(let a of g){var h;(null==(h=a.trpc)?void 0:h.ssr)===!1||d.getQueryCache().find({queryKey:a.queryKey})||e(a.queryKey,a)}return(0,a8.useQueries)({queries:g.map(a=>(0,bw.default)((0,bw.default)({},a),{},{queryKey:a.queryKey})),combine:null==b?void 0:b.combine},d)},useSuspenseQueries:a=>{let{queryClient:b,client:c}=i(),d=a(bo(c)),e=(0,a9.useSuspenseQueries)({queries:d.map(a=>(0,bw.default)((0,bw.default)({},a),{},{queryFn:a.queryFn,queryKey:a.queryKey}))},b);return[e.map(a=>a.data),e]},useMutation:function(a,b){let{client:c,queryClient:d}=i(),e=U(a),f=d.defaultMutationOptions(d.getMutationDefaults(e)),h=(0,a5.useMutation)((0,bw.default)((0,bw.default)({},b),{},{mutationKey:e,mutationFn:d=>c.mutation(...bq([a,{input:d}],b)),onSuccess(...a){var c,e;return g({originalFn:()=>{var c,d,e;return null!=(c=null==b||null==(d=b.onSuccess)?void 0:d.call(b,...a))?c:null==f||null==(e=f.onSuccess)?void 0:e.call(f,...a)},queryClient:d,meta:null!=(c=null!=(e=null==b?void 0:b.meta)?e:null==f?void 0:f.meta)?c:{}})}}),d);return h.trpc=bt({path:a}),h},useSubscription:function(a,b,c){var d;let e=null!=(d=null==c?void 0:c.enabled)?d:b!==w.hT,g=(0,w.EN)(T(a,b,"any")),{client:h}=i(),j=f.useRef(c);f.useEffect(()=>{j.current=c});let[m]=f.useState(new Set([])),n=f.useCallback(a=>{m.add(a)},[m]),o=f.useRef(null),p=f.useCallback(a=>{let b=r.current,c=r.current=a(b),d=!1;for(let a of m)if(b[a]!==c[a]){d=!0;break}d&&t(bx(c,n))},[n,m]),q=f.useCallback(()=>{var c;if(null==(c=o.current)||c.unsubscribe(),!e)return void p(()=>(0,bw.default)((0,bw.default)({},k),{},{reset:q}));p(()=>(0,bw.default)((0,bw.default)({},l),{},{reset:q})),o.current=h.subscription(a.join("."),null!=b?b:void 0,{onStarted:()=>{var a,b;null==(a=(b=j.current).onStarted)||a.call(b),p(a=>(0,bw.default)((0,bw.default)({},a),{},{status:"pending",error:null}))},onData:a=>{var b,c;null==(b=(c=j.current).onData)||b.call(c,a),p(b=>(0,bw.default)((0,bw.default)({},b),{},{status:"pending",data:a,error:null}))},onError:a=>{var b,c;null==(b=(c=j.current).onError)||b.call(c,a),p(b=>(0,bw.default)((0,bw.default)({},b),{},{status:"error",error:a}))},onConnectionStateChange:a=>{p(b=>{switch(a.state){case"idle":return(0,bw.default)((0,bw.default)({},b),{},{status:a.state,error:null,data:void 0});case"connecting":return(0,bw.default)((0,bw.default)({},b),{},{error:a.error,status:a.state});case"pending":return b}})},onComplete:()=>{var a,b;null==(a=(b=j.current).onComplete)||a.call(b),p(a=>(0,bw.default)((0,bw.default)({},a),{},{status:"idle",error:null,data:void 0}))}})},[h,g,e,p]);f.useEffect(()=>(q(),()=>{var a;null==(a=o.current)||a.unsubscribe()}),[q]);let r=f.useRef(e?(0,bw.default)((0,bw.default)({},l),{},{reset:q}):(0,bw.default)((0,bw.default)({},k),{},{reset:q})),[s,t]=f.useState(bx(r.current,n));return s},useInfiniteQuery:function(a,b,c){var d,e,f,g,h;let{client:k,ssrState:l,prefetchInfiniteQuery:m,queryClient:n,abortOnUnmount:o}=i(),p=T(a,b,"infinite"),q=n.getQueryDefaults(p),r=b===w.hT;"undefined"!=typeof window||"prepass"!==l||(null==c||null==(d=c.trpc)?void 0:d.ssr)===!1||(null!=(e=null==c?void 0:c.enabled)?e:null==q?void 0:q.enabled)===!1||r||n.getQueryCache().find({queryKey:p})||m(p,(0,bw.default)((0,bw.default)({},q),c));let s=j(p,(0,bw.default)((0,bw.default)({},q),c)),t=null!=(f=null==c||null==(g=c.trpc)?void 0:g.abortOnUnmount)?f:o,u=(0,a6.useInfiniteQuery)((0,bw.default)((0,bw.default)({},s),{},{initialPageParam:null!=(h=c.initialCursor)?h:null,persister:c.persister,queryKey:p,queryFn:r?b:a=>{var b;let d=(0,bw.default)((0,bw.default)({},s),{},{trpc:(0,bw.default)((0,bw.default)({},null==s?void 0:s.trpc),t?{signal:a.signal}:{signal:null})});return k.query(...bq(p,d,{pageParam:null!=(b=a.pageParam)?b:c.initialCursor,direction:a.direction}))}}),n);return u.trpc=bt({path:a}),u},usePrefetchInfiniteQuery:function(a,b,c){var d,e,f;let g=i(),h=T(a,b,"infinite"),k=g.queryClient.getQueryDefaults(h),l=b===w.hT,m=j(h,(0,bw.default)((0,bw.default)({},k),c)),n=null!=(d=null==c||null==(e=c.trpc)?void 0:e.abortOnUnmount)?d:g.abortOnUnmount;!function(a,b){let c=(0,t.useQueryClient)(void 0);c.getQueryState(a.queryKey)||c.prefetchInfiniteQuery(a)}((0,bw.default)((0,bw.default)({},c),{},{initialPageParam:null!=(f=c.initialCursor)?f:null,queryKey:h,queryFn:l?b:a=>{var b;let d=(0,bw.default)((0,bw.default)({},m),{},{trpc:(0,bw.default)((0,bw.default)({},null==m?void 0:m.trpc),n?{signal:a.signal}:{})});return g.client.query(...bq(h,d,{pageParam:null!=(b=a.pageParam)?b:c.initialCursor,direction:a.direction}))}}))},useSuspenseInfiniteQuery:function(a,b,c){var d,e,f;let g=i(),h=T(a,b,"infinite"),k=g.queryClient.getQueryDefaults(h),l=j(h,(0,bw.default)((0,bw.default)({},k),c)),m=null!=(d=null==c||null==(e=c.trpc)?void 0:e.abortOnUnmount)?d:g.abortOnUnmount,n=(0,a7.useSuspenseInfiniteQuery)((0,bw.default)((0,bw.default)({},c),{},{initialPageParam:null!=(f=c.initialCursor)?f:null,queryKey:h,queryFn:a=>{var b;let d=(0,bw.default)((0,bw.default)({},l),{},{trpc:(0,bw.default)((0,bw.default)({},null==l?void 0:l.trpc),m?{signal:a.signal}:{})});return g.client.query(...bq(h,d,{pageParam:null!=(b=a.pageParam)?b:c.initialCursor,direction:a.direction}))}}),g.queryClient);return n.trpc=bt({path:a}),[n.data,n]}}}var bz=((a,b,c)=>(c=null!=a?h(l(a)):{},((a,b,c,d)=>{if(b&&"object"==typeof b||"function"==typeof b)for(var e,f=k(b),g=0,h=f.length;g<h;g++)e=f[g],m.call(a,e)||e===c||i(a,e,{get:(a=>b[a]).bind(null,e),enumerable:!(d=j(b,e))||d.enumerable});return a})(!b&&a&&a.__esModule?c:i(c,"default",{value:a,enumerable:!0}),a)))(s());let bA=function(a){let b=by(a),c=function(a){let{config:b}=a,c=v(a.transformer);return d=>{var g,h;let i=by(a),j=g=>{var h,j;let[k]=(0,f.useState)(()=>{var c;if(g.trpc)return g.trpc;let d=b({}),e=null!=(c=d.queryClient)?c:new bk(d.queryClientConfig),f=i.createClient(d);return{abortOnUnmount:d.abortOnUnmount,queryClient:e,trpcClient:f,ssrState:!!a.ssr&&"mounting",ssrContext:null}}),{queryClient:l,trpcClient:m,ssrState:n,ssrContext:o}=k,p=null==(h=g.pageProps)?void 0:h.trpcState,q=f.useMemo(()=>p?c.input.deserialize(p):p,[p]);return(0,e.jsx)(i.Provider,{abortOnUnmount:null!=(j=k.abortOnUnmount)&&j,client:m,queryClient:l,ssrState:n,ssrContext:o,children:(0,e.jsx)(t.QueryClientProvider,{client:l,children:(0,e.jsx)(u.HydrationBoundary,{state:q,children:(0,e.jsx)(d,(0,bz.default)({},g))})})})};a.ssr?a.ssrPrepass({parent:a,AppOrPage:d,WithTRPC:j}):d.getInitialProps&&(j.getInitialProps=async a=>{var b;let c,e=!!a.Component,f={},g=await d.getInitialProps(a),h=e?null!=(b=g.pageProps)?b:{}:g;return c=f=(0,bz.default)((0,bz.default)({},h),f),e?{pageProps:c}:c});let k=null!=(g=null!=(h=d.displayName)?h:d.name)?g:"Component";return j.displayName=`withTRPC(${k})`,j}}(a),d=aD(({path:a,args:c})=>{var d;let e=[...a],f=e.pop();if("useMutation"===f)return b[f](e,...c);if("_def"===f)return{path:e};let[g,...h]=c,i=null!=(d=h[0])?d:{};return b[f](e,g,i)});return aE(a=>"useContext"===a||"useUtils"===a?()=>{let a=b.useUtils();return(0,f.useMemo)(()=>(function(a){let b=aZ(a.client),c=aD(b=>{let c=[...b.path],d=c.pop(),e=[...b.args],f=e.shift(),g=T(c,f,(a=>{switch(a){case"queryOptions":case"fetch":case"ensureData":case"prefetch":case"getData":case"setData":case"setQueriesData":return"query";case"infiniteQueryOptions":case"fetchInfinite":case"prefetchInfinite":case"getInfiniteData":case"setInfiniteData":return"infinite";case"setMutationDefaults":case"getMutationDefaults":case"isMutating":case"cancel":case"invalidate":case"refetch":case"reset":return"any"}})(d));return({infiniteQueryOptions:()=>a.infiniteQueryOptions(c,g,e[0]),queryOptions:()=>a.queryOptions(c,g,...e),fetch:()=>a.fetchQuery(g,...e),fetchInfinite:()=>a.fetchInfiniteQuery(g,e[0]),prefetch:()=>a.prefetchQuery(g,...e),prefetchInfinite:()=>a.prefetchInfiniteQuery(g,e[0]),ensureData:()=>a.ensureQueryData(g,...e),invalidate:()=>a.invalidateQueries(g,...e),reset:()=>a.resetQueries(g,...e),refetch:()=>a.refetchQueries(g,...e),cancel:()=>a.cancelQuery(g,...e),setData:()=>{a.setQueryData(g,e[0],e[1])},setQueriesData:()=>a.setQueriesData(g,e[0],e[1],e[2]),setInfiniteData:()=>{a.setInfiniteQueryData(g,e[0],e[1])},getData:()=>a.getQueryData(g),getInfiniteData:()=>a.getInfiniteQueryData(g),setMutationDefaults:()=>a.setMutationDefaults(U(c),f),getMutationDefaults:()=>a.getMutationDefaults(U(c)),isMutating:()=>a.isMutating({mutationKey:U(c)})})[d]()});return aE(d=>"client"===d?b:bl.includes(d)?a[d]:c[d])})(a),[a])}:"useQueries"===a?b.useQueries:"useSuspenseQueries"===a?b.useSuspenseQueries:"withTRPC"===a?c:d[a])}({config:()=>({links:[function(a){var b,c;let d={url:a.url.toString(),fetch:a.fetch,transformer:v(a.transformer),methodOverride:a.methodOverride},e=null!=(b=a.maxURLLength)?b:1/0,f=null!=(c=a.maxItems)?c:1/0;return()=>{let b=b=>({validate(a){if(e===1/0&&f===1/0)return!0;if(a.length>f)return!1;let c=a.map(a=>a.path).join(","),g=a.map(a=>a.input);return ao((0,aO.default)((0,aO.default)({},d),{},{type:b,path:c,inputs:g,signal:null})).length<=e},async fetch(c){let e,f=c.map(a=>a.path).join(","),g=c.map(a=>a.input),h=function(...a){let b=new AbortController,c=a.length,d=0,e=()=>{++d===c&&b.abort()};for(let b of a)(null==b?void 0:b.aborted)?e():null==b||b.addEventListener("abort",e,{once:!0});return b.signal}(...c.map(a=>a.signal)),i=await (e=(0,aO.default)((0,aO.default)({},d),{},{path:f,inputs:g,type:b,headers:()=>a.headers?"function"==typeof a.headers?a.headers({opList:c}):a.headers:{},signal:h}),as((0,al.default)((0,al.default)({},e),{},{contentTypeHeader:"application/json",getUrl:ao,getBody:ap})));return(Array.isArray(i.json)?i.json:c.map(()=>i.json)).map(a=>({meta:i.meta,json:a}))}}),c={query:aN(b("query")),mutation:aN(b("mutation"))};return({op:a})=>ag(b=>{let e;if("subscription"===a.type)throw Error("Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`");return c[a.type].load(a).then(a=>{e=a;let c=function(a,b){let c;try{c=function(a,b){if("error"in a){let c=b.deserialize(a.error);return{ok:!1,error:(0,aK.default)((0,aK.default)({},a),{},{error:c})}}return{ok:!0,result:(0,aK.default)((0,aK.default)({},a.result),(!a.result.type||"data"===a.result.type)&&{type:"data",data:b.deserialize(a.result.data)})}}(a,b)}catch(a){throw new aL}if(!c.ok&&(!y(c.error.error)||"number"!=typeof c.error.error.code)||c.ok&&!y(c.result))throw new aL;return c}(a.json,d.transformer.output);if(!c.ok)return void b.error(ak.from(c.error,{meta:a.meta}));b.next({context:a.meta,result:c.result}),b.complete()}).catch(a=>{b.error(ak.from(a,{meta:null==e?void 0:e.meta}))}),()=>{}})}}({url:"/api/trpc"})]}),ssr:!1});function bB(){let[a,b]=(0,f.useState)(""),[c,d]=(0,f.useState)(),[g,h]=(0,f.useState)([]),i=(0,f.useRef)(null),j=bA.chat.sendMessage.useMutation({onSuccess:a=>{h(b=>[...b,a.message]),d(a.conversation_id)},onError:a=>{console.error("Failed to send message:",a)}}),k=async()=>{if(!a.trim()||j.isPending)return;let d=a.trim(),e={id:`user_${Date.now()}`,role:"user",content:d,timestamp:Date.now()};h(a=>[...a,e]),b("");try{await j.mutateAsync({message:d,conversation_id:c})}catch(a){console.error("Failed to send message:",a)}};return(0,e.jsxs)("div",{className:"flex flex-col h-screen bg-gray-50",children:[(0,e.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,e.jsx)("h1",{className:"text-xl font-semibold text-gray-800",children:"Function Calling Tools"}),(0,e.jsx)("p",{className:"text-sm text-gray-600",children:"AI助手，支持天气查询、翻译和文本摘要"})]}),(0,e.jsxs)("div",{className:"flex-1 overflow-y-auto px-6 py-4 space-y-4",children:[0===g.length?(0,e.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,e.jsx)("p",{className:"text-lg mb-2",children:"\uD83D\uDC4B 欢迎使用 Function Calling Tools"}),(0,e.jsx)("p",{className:"text-sm",children:"我可以帮您查询天气、翻译文本或生成摘要。请输入您的需求！"})]}):g.map(a=>(0,e.jsx)("div",{className:`flex ${"user"===a.role?"justify-end":"justify-start"}`,children:(0,e.jsxs)("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${"user"===a.role?"bg-blue-500 text-white":"bg-white text-gray-800 border border-gray-200"}`,children:[(0,e.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:a.content}),a.tool_calls&&a.tool_calls.length>0&&(0,e.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:["\uD83D\uDD27 使用了 ",a.tool_calls.length," 个工具"]})]})},a.id)),j.isPending&&(0,e.jsx)("div",{className:"flex justify-start",children:(0,e.jsx)("div",{className:"bg-white text-gray-800 border border-gray-200 px-4 py-2 rounded-lg",children:(0,e.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,e.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"}),(0,e.jsx)("span",{className:"text-sm",children:"AI正在思考..."})]})})})]}),(0,e.jsx)("div",{className:"bg-white border-t border-gray-200 px-6 py-4",children:(0,e.jsxs)("div",{className:"flex items-end space-x-4",children:[(0,e.jsx)("div",{className:"flex-1",children:(0,e.jsx)("textarea",{ref:i,value:a,onChange:a=>b(a.target.value),onKeyPress:a=>{"Enter"!==a.key||a.shiftKey||(a.preventDefault(),k())},placeholder:"输入您的消息...",className:"w-full px-4 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:1,disabled:j.isPending})}),(0,e.jsx)("button",{onClick:k,disabled:!a.trim()||j.isPending,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:j.isPending?(0,e.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):"发送"})]})})]})}function bC(){return(0,e.jsx)(bB,{})}},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9336:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(7413),e=c(2202),f=c.n(e),g=c(4988),h=c.n(g),i=c(7934),j=c(6301);let k=(0,i.R)({config:()=>({links:[(0,j.fu)({url:"/api/trpc"})]}),ssr:!1});c(1135);let l={title:"Function Calling Tools",description:"AI-powered function calling tools with Next.js and tRPC"},m=k.withTRPC(function({children:a}){return(0,d.jsx)("html",{lang:"zh",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})})}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[431,886,298],()=>b(b.s=4538));module.exports=c})();