"use strict";exports.id=886,exports.ids=[886],exports.modules={457:(a,b,c)=>{c.d(b,{EN:()=>f,Iw:()=>w,OA:()=>x,OX:()=>v,bJ:()=>m,gt:()=>j,qO:()=>i,ri:()=>y,t9:()=>o,u$:()=>l,u7:()=>q,vJ:()=>A});var d=c(3274),e=c(8761);let f=({shape:a})=>a;var g=(0,d.f1)((0,d.Vh)(),1),h=class extends Error{};function i(a){if(a instanceof j||a instanceof Error&&"TRPCError"===a.name)return a;let b=new j({code:"INTERNAL_SERVER_ERROR",cause:a});return a instanceof Error&&a.stack&&(b.stack=a.stack),b}var j=class extends Error{constructor(a){var b,c;let d=function(a){if(a instanceof Error)return a;let b=typeof a;if("undefined"!==b&&"function"!==b&&null!==a){if("object"!==b)return Error(String(a));if((0,e.Gv)(a))return Object.assign(new h,a)}}(a.cause);super(null!=(b=null!=(c=a.message)?c:null==d?void 0:d.message)?b:a.code,{cause:d}),(0,g.default)(this,"cause",void 0),(0,g.default)(this,"code",void 0),this.code=a.code,this.name="TRPCError",null!=this.cause||(this.cause=d)}},k=(0,d.f1)((0,d.jr)(),1);function l(a){return"input"in a?a:{input:a,output:a}}let m={input:{serialize:a=>a,deserialize:a=>a},output:{serialize:a=>a,deserialize:a=>a}};function n(a,b){return"error"in b?(0,k.default)((0,k.default)({},b),{},{error:a.transformer.output.serialize(b.error)}):"data"in b.result?(0,k.default)((0,k.default)({},b),{},{result:(0,k.default)((0,k.default)({},b.result),{},{data:a.transformer.output.serialize(b.result.data)})}):b}function o(a,b){return Array.isArray(b)?b.map(b=>n(a,b)):n(a,b)}var p=class extends Error{constructor(){super("Unable to transform response from server")}};function q(a,b){let c;try{c=function(a,b){if("error"in a){let c=b.deserialize(a.error);return{ok:!1,error:(0,k.default)((0,k.default)({},a),{},{error:c})}}return{ok:!0,result:(0,k.default)((0,k.default)({},a.result),(!a.result.type||"data"===a.result.type)&&{type:"data",data:b.deserialize(a.result.data)})}}(a,b)}catch(a){throw new p}if(!c.ok&&(!(0,e.Gv)(c.error.error)||"number"!=typeof c.error.error.code)||c.ok&&!(0,e.Gv)(c.result))throw new p;return c}var r=(0,d.f1)((0,d.jr)(),1);let s=Symbol("lazy"),t={_ctx:null,_errorShape:null,_meta:null,queries:{},mutations:{},subscriptions:{},errorFormatter:f,transformer:m},u=["then","call","apply"];function v(a){return function(b){let c=new Set(Object.keys(b).filter(a=>u.includes(a)));if(c.size>0)throw Error("Reserved words used in `router({})` call: "+Array.from(c).join(", "));let d=(0,e.QQ)({}),f=(0,e.QQ)({}),g=function a(b,c=[]){let g=(0,e.QQ)({});for(let[h,i]of Object.entries(null!=b?b:{})){if("function"==typeof i&&s in i){f[[...c,h].join(".")]=function b(c){return{ref:c.ref,load:function(a){let b=Symbol(),c=b;return()=>(c===b&&(c=a()),c)}(async()=>{let d=await c.ref(),e=[...c.path,c.key],g=e.join(".");for(let[h,i]of(c.aggregate[c.key]=a(d._def.record,e),delete f[g],Object.entries(d._def.lazy)))f[[...e,h].join(".")]=b({ref:i.ref,path:e,key:h,aggregate:c.aggregate[c.key]})})}}({path:c,ref:i,key:h,aggregate:g});continue}if((0,e.Gv)(i)&&(0,e.Gv)(i._def)&&"router"in i._def){g[h]=a(i._def.record,[...c,h]);continue}if("function"!=typeof i){g[h]=a(i,[...c,h]);continue}let b=[...c,h].join(".");if(d[b])throw Error(`Duplicate key: ${b}`);d[b]=i,g[h]=i}return g}(b),h=(0,r.default)((0,r.default)({_config:a,router:!0,procedures:d,lazy:f},t),{},{record:g});return(0,r.default)((0,r.default)({},g),{},{_def:h,createCaller:x()({_def:h})})}}async function w(a,b){let{_def:c}=a,d=c.procedures[b];for(;!d;){let a=Object.keys(c.lazy).find(a=>b.startsWith(a));if(!a)return null;let e=c.lazy[a];await e.load(),d=c.procedures[b]}return d}function x(){return function(a){let{_def:b}=a;return function(c,f){return(0,d.vX)(async({path:d,args:g})=>{let h,k=d.join(".");if(1===d.length&&"_def"===d[0])return b;let l=await w(a,k);try{if(!l)throw new j({code:"NOT_FOUND",message:`No procedure found on path "${d}"`});return h=(0,e.Tn)(c)?await Promise.resolve(c()):c,await l({path:k,getRawInput:async()=>g[0],ctx:h,type:l._def.type,signal:null==f?void 0:f.signal})}catch(a){var m,n;throw null==f||null==(m=f.onError)||m.call(f,{ctx:h,error:i(a),input:g[0],path:k,type:null!=(n=null==l?void 0:l._def.type)?n:"unknown"}),a}})}}}function y(...a){var b;let c=(0,e.uf)({},...a.map(a=>a._def.record));return v({errorFormatter:a.reduce((a,b)=>{if(b._def._config.errorFormatter&&b._def._config.errorFormatter!==f){if(a!==f&&a!==b._def._config.errorFormatter)throw Error("You seem to have several error formatters");return b._def._config.errorFormatter}return a},f),transformer:a.reduce((a,b)=>{if(b._def._config.transformer&&b._def._config.transformer!==m){if(a!==m&&a!==b._def._config.transformer)throw Error("You seem to have several transformers");return b._def._config.transformer}return a},m),isDev:a.every(a=>a._def._config.isDev),allowOutsideOfServer:a.every(a=>a._def._config.allowOutsideOfServer),isServer:a.every(a=>a._def._config.isServer),$types:null==(b=a[0])?void 0:b._def._config.$types})(c)}let z=Symbol();function A(a){return Array.isArray(a)&&a[2]===z}},3274:(a,b,c)=>{c.d(b,{E$:()=>s,KQ:()=>z,P$:()=>k,U6:()=>p,Vh:()=>w,f1:()=>l,jr:()=>x,vX:()=>o});var d=c(8761),e=Object.create,f=Object.defineProperty,g=Object.getOwnPropertyDescriptor,h=Object.getOwnPropertyNames,i=Object.getPrototypeOf,j=Object.prototype.hasOwnProperty,k=(a,b)=>function(){return b||(0,a[h(a)[0]])((b={exports:{}}).exports,b),b.exports},l=(a,b,c)=>(c=null!=a?e(i(a)):{},((a,b,c,d)=>{if(b&&"object"==typeof b||"function"==typeof b)for(var e,i=h(b),k=0,l=i.length;k<l;k++)e=i[k],j.call(a,e)||e===c||f(a,e,{get:(a=>b[a]).bind(null,e),enumerable:!(d=g(b,e))||d.enumerable});return a})(!b&&a&&a.__esModule?c:f(c,"default",{value:a,enumerable:!0}),a));let m=()=>{},n=a=>{Object.freeze&&Object.freeze(a)},o=a=>(function a(b,c,d){let e=c.join(".");return null!=d[e]||(d[e]=new Proxy(m,{get(e,f){if("string"==typeof f&&"then"!==f)return a(b,[...c,f],d)},apply(a,d,e){let f=c[c.length-1],g={args:e,path:c};return"call"===f?g={args:e.length>=2?[e[1]]:[],path:c.slice(0,-1)}:"apply"===f&&(g={args:e.length>=2?e[1]:[],path:c.slice(0,-1)}),n(g.args),n(g.path),b(g)}})),d[e]})(a,[],Object.create(null)),p=a=>new Proxy(m,{get(b,c){if("then"!==c)return a(c)}}),q={PARSE_ERROR:400,BAD_REQUEST:400,UNAUTHORIZED:401,PAYMENT_REQUIRED:402,FORBIDDEN:403,NOT_FOUND:404,METHOD_NOT_SUPPORTED:405,TIMEOUT:408,CONFLICT:409,PRECONDITION_FAILED:412,PAYLOAD_TOO_LARGE:413,UNSUPPORTED_MEDIA_TYPE:415,UNPROCESSABLE_CONTENT:422,TOO_MANY_REQUESTS:429,CLIENT_CLOSED_REQUEST:499,INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501,BAD_GATEWAY:502,SERVICE_UNAVAILABLE:503,GATEWAY_TIMEOUT:504};function r(a){var b;return null!=(b=q[a])?b:500}function s(a){let b=new Set((Array.isArray(a)?a:[a]).map(a=>{if("error"in a&&(0,d.Gv)(a.error.data)){var b;return"number"==typeof(null==(b=a.error.data)?void 0:b.httpStatus)?a.error.data.httpStatus:r(d.uB[a.error.code])}return 200}));return 1!==b.size?207:b.values().next().value}var t=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(a,b){function c(a){return b.exports=c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},b.exports.__esModule=!0,b.exports.default=b.exports,c(a)}b.exports=c,b.exports.__esModule=!0,b.exports.default=b.exports}}),u=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(a,b){var c=t().default;b.exports=function(a,b){if("object"!=c(a)||!a)return a;var d=a[Symbol.toPrimitive];if(void 0!==d){var e=d.call(a,b||"default");if("object"!=c(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)},b.exports.__esModule=!0,b.exports.default=b.exports}}),v=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(a,b){var c=t().default,d=u();b.exports=function(a){var b=d(a,"string");return"symbol"==c(b)?b:b+""},b.exports.__esModule=!0,b.exports.default=b.exports}}),w=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(a,b){var c=v();b.exports=function(a,b,d){return(b=c(b))in a?Object.defineProperty(a,b,{value:d,enumerable:!0,configurable:!0,writable:!0}):a[b]=d,a},b.exports.__esModule=!0,b.exports.default=b.exports}}),x=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(a,b){var c=w();function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}b.exports=function(a){for(var b=1;b<arguments.length;b++){var e=null!=arguments[b]?arguments[b]:{};b%2?d(Object(e),!0).forEach(function(b){c(a,b,e[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):d(Object(e)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(e,b))})}return a},b.exports.__esModule=!0,b.exports.default=b.exports}}),y=l(x(),1);function z(a){let{path:b,error:c,config:e}=a,{code:f}=a.error,g={message:c.message,code:d.Yk[f],data:{code:f,httpStatus:r(c.code)}};return e.isDev&&"string"==typeof a.error.stack&&(g.data.stack=a.error.stack),"string"==typeof b&&(g.data.path=b),e.errorFormatter((0,y.default)((0,y.default)({},a),{},{shape:g}))}},5810:(a,b,c)=>{function d(a){return"object"==typeof a&&null!==a&&"subscribe"in a}function e(a){let b={subscribe(b){let c=null,d=!1,e=!1,f=!1;function g(){if(null===c){f=!0;return}!e&&(e=!0,"function"==typeof c?c():c&&c.unsubscribe())}return c=a({next(a){var c;d||null==(c=b.next)||c.call(b,a)},error(a){var c;d||(d=!0,null==(c=b.error)||c.call(b,a),g())},complete(){var a;d||(d=!0,null==(a=b.complete)||a.call(b),g())}}),f&&g(),{unsubscribe:g}},pipe:(...a)=>a.reduce(f,b)};return b}function f(a,b){return b(a)}function g(a){let b=new AbortController;return new Promise((c,d)=>{let e=!1;function f(){e||(e=!0,g.unsubscribe())}b.signal.addEventListener("abort",()=>{d(b.signal.reason)});let g=a.subscribe({next(a){e=!0,c(a),f()},error(a){d(a)},complete(){b.abort(),f()}})})}function h(a,b){let c=(function(a,b){let c=null,d=()=>{null==c||c.unsubscribe(),c=null,b.removeEventListener("abort",d)};return new ReadableStream({start(e){c=a.subscribe({next(a){e.enqueue({ok:!0,value:a})},error(a){e.enqueue({ok:!1,error:a}),e.close()},complete(){e.close()}}),b.aborted?d():b.addEventListener("abort",d,{once:!0})},cancel(){d()}})})(a,b).getReader(),d={async next(){let a=await c.read();if(a.done)return{value:void 0,done:!0};let{value:b}=a;if(!b.ok)throw b.error;return{value:b.value,done:!1}},return:async()=>(await c.cancel(),{value:void 0,done:!0})};return{[Symbol.asyncIterator]:()=>d}}c.d(b,{AT:()=>d,di:()=>h,sH:()=>e,yR:()=>g})},8761:(a,b,c)=>{c.d(b,{D_:()=>m,Gv:()=>g,QQ:()=>i,Td:()=>k,Tn:()=>h,Yk:()=>d,eF:()=>l,uB:()=>e,uf:()=>f});let d={PARSE_ERROR:-32700,BAD_REQUEST:-32600,INTERNAL_SERVER_ERROR:-32603,NOT_IMPLEMENTED:-32603,BAD_GATEWAY:-32603,SERVICE_UNAVAILABLE:-32603,GATEWAY_TIMEOUT:-32603,UNAUTHORIZED:-32001,PAYMENT_REQUIRED:-32002,FORBIDDEN:-32003,NOT_FOUND:-32004,METHOD_NOT_SUPPORTED:-32005,TIMEOUT:-32008,CONFLICT:-32009,PRECONDITION_FAILED:-32012,PAYLOAD_TOO_LARGE:-32013,UNSUPPORTED_MEDIA_TYPE:-32015,UNPROCESSABLE_CONTENT:-32022,TOO_MANY_REQUESTS:-32029,CLIENT_CLOSED_REQUEST:-32099},e={[-32700]:"PARSE_ERROR",[-32600]:"BAD_REQUEST",[-32603]:"INTERNAL_SERVER_ERROR",[-32001]:"UNAUTHORIZED",[-32002]:"PAYMENT_REQUIRED",[-32003]:"FORBIDDEN",[-32004]:"NOT_FOUND",[-32005]:"METHOD_NOT_SUPPORTED",[-32008]:"TIMEOUT",[-32009]:"CONFLICT",[-32012]:"PRECONDITION_FAILED",[-32013]:"PAYLOAD_TOO_LARGE",[-32015]:"UNSUPPORTED_MEDIA_TYPE",[-32022]:"UNPROCESSABLE_CONTENT",[-32029]:"TOO_MANY_REQUESTS",[-32099]:"CLIENT_CLOSED_REQUEST"};function f(a,...b){let c=Object.assign(Object.create(null),a);for(let a of b)for(let b in a){if(b in c&&c[b]!==a[b])throw Error(`Duplicate key ${b}`);c[b]=a[b]}return c}function g(a){return!!a&&!Array.isArray(a)&&"object"==typeof a}function h(a){return"function"==typeof a}function i(a){return Object.assign(Object.create(null),a)}d.BAD_GATEWAY,d.SERVICE_UNAVAILABLE,d.GATEWAY_TIMEOUT,d.INTERNAL_SERVER_ERROR;let j="function"==typeof Symbol&&!!Symbol.asyncIterator;function k(a){return j&&g(a)&&Symbol.asyncIterator in a}let l=a=>a();function m(a){return a}}};