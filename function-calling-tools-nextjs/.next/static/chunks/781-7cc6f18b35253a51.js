"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[781],{781:(e,t,r)=>{r.d(t,{R:()=>e_});var n,o=Object.create,u=Object.defineProperty,i=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyNames,s=Object.getPrototypeOf,a=Object.prototype.hasOwnProperty,c=(e,t)=>function(){return t||(0,e[l(e)[0]])((t={exports:{}}).exports,t),t.exports},p=c({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(e){return t.exports=r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t.exports.__esModule=!0,t.exports.default=t.exports,r(e)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),f=c({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=p().default;t.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},t.exports.__esModule=!0,t.exports.default=t.exports}}),d=c({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=p().default,n=f();t.exports=function(e){var t=n(e,"string");return"symbol"==r(t)?t:t+""},t.exports.__esModule=!0,t.exports.default=t.exports}}),y=c({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=d();t.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},t.exports.__esModule=!0,t.exports.default=t.exports}}),h=c({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=y();function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}t.exports=function(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?n(Object(o),!0).forEach(function(t){r(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e},t.exports.__esModule=!0,t.exports.default=t.exports}}),m=r(6715),b=r(8902),v=r(4972),g=r(2020),x=r(1675),j=Object.create,O=Object.defineProperty,P=Object.getOwnPropertyDescriptor,w=Object.getOwnPropertyNames,_=Object.getPrototypeOf,S=Object.prototype.hasOwnProperty,Q=(e,t)=>function(){return t||(0,e[w(e)[0]])((t={exports:{}}).exports,t),t.exports},q=(e,t,r)=>(r=null!=e?j(_(e)):{},((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(var o,u=w(t),i=0,l=u.length;i<l;i++)o=u[i],S.call(e,o)||o===r||O(e,o,{get:(e=>t[e]).bind(null,o),enumerable:!(n=P(t,o))||n.enumerable});return e})(!t&&e&&e.__esModule?r:O(r,"default",{value:e,enumerable:!0}),e)),C=Q({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js"(e,t){t.exports=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r},t.exports.__esModule=!0,t.exports.default=t.exports}}),D=Q({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutProperties.js"(e,t){var r=C();t.exports=function(e,t){if(null==e)return{};var n,o,u=r(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],t.includes(n)||({}).propertyIsEnumerable.call(e,n)&&(u[n]=e[n])}return u},t.exports.__esModule=!0,t.exports.default=t.exports}}),E=Q({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(e){return t.exports=r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t.exports.__esModule=!0,t.exports.default=t.exports,r(e)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),M=Q({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=E().default;t.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},t.exports.__esModule=!0,t.exports.default=t.exports}}),I=Q({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=E().default,n=M();t.exports=function(e){var t=n(e,"string");return"symbol"==r(t)?t:t+""},t.exports.__esModule=!0,t.exports.default=t.exports}}),T=Q({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=I();t.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},t.exports.__esModule=!0,t.exports.default=t.exports}}),k=Q({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=T();function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}t.exports=function(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?n(Object(o),!0).forEach(function(t){r(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e},t.exports.__esModule=!0,t.exports.default=t.exports}}),A=q(D(),1),U=q(k(),1);let N=["cursor","direction"];function F(e,t,r){let n=e.flatMap(e=>e.split("."));if(!t&&(!r||"any"===r))return n.length?[n]:[];if("infinite"===r&&(0,x.Gv)(t)&&("direction"in t||"cursor"in t)){let{cursor:e,direction:r}=t;return[n,{input:(0,A.default)(t,N),type:"infinite"}]}return[n,(0,U.default)((0,U.default)({},void 0!==t&&t!==g.hT&&{input:t}),r&&"any"!==r&&{type:r})]}function G(e){return F(e,void 0,"any")}var K=r(9761),R=r(5838),L=r(3666),H=r(5041),W=r(8822),z=r(5490),X=r(1610),B=r(1142),$=r(9853),V=r(7165),J=r(5910),Y=class extends J.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,r){let n=t.queryKey,o=t.queryHash??(0,g.F$)(n,t),u=this.get(o);return u||(u=new $.X({client:e,queryKey:n,queryHash:o,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(u)),u}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){V.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,g.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,g.MK)(e,t)):t}notify(e){V.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){V.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){V.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},Z=r(4560),ee=class extends J.Q{constructor(e={}){super(),this.config=e,this.#t=new Set,this.#r=new Map,this.#n=0}#t;#r;#n;build(e,t,r){let n=new Z.s({mutationCache:this,mutationId:++this.#n,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#t.add(e);let t=et(e);if("string"==typeof t){let r=this.#r.get(t);r?r.push(e):this.#r.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#t.delete(e)){let t=et(e);if("string"==typeof t){let r=this.#r.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#r.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=et(e);if("string"!=typeof t)return!0;{let r=this.#r.get(t),n=r?.find(e=>"pending"===e.state.status);return!n||n===e}}runNext(e){let t=et(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#r.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){V.jG.batch(()=>{this.#t.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#t.clear(),this.#r.clear()})}getAll(){return Array.from(this.#t)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,g.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,g.nJ)(e,t))}notify(e){V.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return V.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(g.lQ))))}};function et(e){return e.options.scope?.id}var er=r(920),en=r(1239),eo=r(4275),eu=class{#o;#u;#i;#l;#s;#a;#c;#p;constructor(e={}){this.#o=e.queryCache||new Y,this.#u=e.mutationCache||new ee,this.#i=e.defaultOptions||{},this.#l=new Map,this.#s=new Map,this.#a=0}mount(){this.#a++,1===this.#a&&(this.#c=er.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#o.onFocus())}),this.#p=en.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#o.onOnline())}))}unmount(){this.#a--,0===this.#a&&(this.#c?.(),this.#c=void 0,this.#p?.(),this.#p=void 0)}isFetching(e){return this.#o.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#u.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#o.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#o.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,g.d2)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#o.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let n=this.defaultQueryOptions({queryKey:e}),o=this.#o.get(n.queryHash),u=o?.state.data,i=(0,g.Zw)(t,u);if(void 0!==i)return this.#o.build(this,n).setData(i,{...r,manual:!0})}setQueriesData(e,t,r){return V.jG.batch(()=>this.#o.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#o.get(t.queryHash)?.state}removeQueries(e){let t=this.#o;V.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#o;return V.jG.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(V.jG.batch(()=>this.#o.findAll(e).map(e=>e.cancel(r)))).then(g.lQ).catch(g.lQ)}invalidateQueries(e,t={}){return V.jG.batch(()=>(this.#o.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(V.jG.batch(()=>this.#o.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(g.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(g.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#o.build(this,t);return r.isStaleByTime((0,g.d2)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(g.lQ).catch(g.lQ)}fetchInfiniteQuery(e){return e.behavior=(0,eo.PL)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(g.lQ).catch(g.lQ)}ensureInfiniteQueryData(e){return e.behavior=(0,eo.PL)(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return en.t.isOnline()?this.#u.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#o}getMutationCache(){return this.#u}getDefaultOptions(){return this.#i}setDefaultOptions(e){this.#i=e}setQueryDefaults(e,t){this.#l.set((0,g.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#l.values()],r={};return t.forEach(t=>{(0,g.Cp)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#s.set((0,g.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#s.values()],r={};return t.forEach(t=>{(0,g.Cp)(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#i.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,g.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===g.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#i.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#o.clear(),this.#u.clear()}},ei=r(4923),el=r(2115),es=r.t(el,2),ea=r(5155);let ec=["client","ssrContext","ssrState","abortOnUnmount"],ep=null==(n=el.createContext)?void 0:n.call(es,null);var ef=q(k(),1);function ed(e){let t=e instanceof K.Ke?e:(0,K.n2)(e);return(0,ei.vX)(e=>{let r=e.path,n=r.join("."),[o,u]=e.args;return(0,ef.default)({queryKey:F(r,o,"query"),queryFn:()=>t.query(n,o,null==u?void 0:u.trpc)},u)})}var ey=q(k(),1);function eh(e,t,r){var n,o;let u=e[0],i=null==(n=e[1])?void 0:n.input;return r&&(i=(0,ey.default)((0,ey.default)((0,ey.default)({},null!=(o=i)?o:{}),r.pageParam?{cursor:r.pageParam}:{}),{},{direction:r.direction})),[u.join("."),i,null==t?void 0:t.trpc]}var em=q(Q({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(e,t){function r(e){function t(e){if(Object(e)!==e)return Promise.reject(TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(r=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var r=this.s.return;return void 0===r?Promise.resolve({value:e,done:!0}):t(r.apply(this.s,arguments))},throw:function(e){var r=this.s.return;return void 0===r?Promise.reject(e):t(r.apply(this.s,arguments))}},new r(e)}t.exports=function(e){var t,n,o,u=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,o=Symbol.iterator);u--;){if(n&&null!=(t=e[n]))return t.call(e);if(o&&null!=(t=e[o]))return new r(t.call(e));n="@@asyncIterator",o="@@iterator"}throw TypeError("Object is not async iterable")},t.exports.__esModule=!0,t.exports.default=t.exports}})(),1);function eb(e){return{path:e.path.join(".")}}function ev(e){let t=eb(e);return el.useMemo(()=>t,[t])}async function eg(e,t,r){let n=t.getQueryCache().build(t,{queryKey:r});n.setState({data:[],status:"success"});let o=[];var u=!1,i=!1;try{for(var l,s,a=(0,em.default)(e);u=!(s=await a.next()).done;u=!1){let e=s.value;o.push(e),n.setState({data:[...o]})}}catch(e){i=!0,l=e}finally{try{u&&null!=a.return&&await a.return()}finally{if(i)throw l}}return o}var ex=q(k(),1),ej=q(k());let eO=(e,t)=>new Proxy(e,{get:(e,r)=>(t(r),e[r])});function eP(e){var t,r,n;let o=null!=(t=null==e||null==(r=e.overrides)||null==(r=r.useMutation)?void 0:r.onSuccess)?t:e=>e.originalFn(),u=null!=(n=null==e?void 0:e.context)?n:ep,i=K.XT;function l(){let e=el.useContext(u);if(!e)throw Error("Unable to find tRPC Context. Did you forget to wrap your App inside `withTRPC` HoC?");return e}function s(e,t){var r;let{queryClient:n,ssrState:o}=l();return o&&"mounted"!==o&&(null==(r=n.getQueryCache().find({queryKey:e}))?void 0:r.state.status)==="error"?(0,ej.default)({retryOnMount:!1},t):t}let a={data:void 0,error:null,status:"idle"},c={data:void 0,error:null,status:"connecting"};return{Provider:e=>{var t;let{abortOnUnmount:r=!1,queryClient:n,ssrContext:o}=e,[i,l]=el.useState(null!=(t=e.ssrState)&&t),s=e.client instanceof K.Ke?e.client:(0,K.n2)(e.client),a=el.useMemo(()=>(function(e){let{client:t,queryClient:r}=e,n=t instanceof K.Ke?t:(0,K.n2)(t);return{infiniteQueryOptions:(e,t,r)=>{var o,u;let i=(null==(o=t[1])?void 0:o.input)===g.hT,l=async e=>{var o;let u=(0,ex.default)((0,ex.default)({},r),{},{trpc:(0,ex.default)((0,ex.default)({},null==r?void 0:r.trpc),(null==r||null==(o=r.trpc)?void 0:o.abortOnUnmount)?{signal:e.signal}:{signal:null})});return await n.query(...eh(t,u,{direction:e.direction,pageParam:e.pageParam}))};return Object.assign((0,ex.default)((0,ex.default)({},r),{},{initialData:null==r?void 0:r.initialData,queryKey:t,queryFn:i?g.hT:l,initialPageParam:null!=(u=null==r?void 0:r.initialCursor)?u:null}),{trpc:eb({path:e})})},queryOptions:(e,t,o)=>{var u;let i=(null==(u=t[1])?void 0:u.input)===g.hT,l=async e=>{var u;let i=(0,ex.default)((0,ex.default)({},o),{},{trpc:(0,ex.default)((0,ex.default)({},null==o?void 0:o.trpc),(null==o||null==(u=o.trpc)?void 0:u.abortOnUnmount)?{signal:e.signal}:{signal:null})}),l=await n.query(...eh(t,i));return(0,x.Td)(l)?eg(l,r,t):l};return Object.assign((0,ex.default)((0,ex.default)({},o),{},{initialData:null==o?void 0:o.initialData,queryKey:t,queryFn:i?g.hT:l}),{trpc:eb({path:e})})},fetchQuery:(e,t)=>r.fetchQuery((0,ex.default)((0,ex.default)({},t),{},{queryKey:e,queryFn:()=>n.query(...eh(e,t))})),fetchInfiniteQuery:(e,t)=>{var o;return r.fetchInfiniteQuery((0,ex.default)((0,ex.default)({},t),{},{queryKey:e,queryFn:({pageParam:r,direction:o})=>n.query(...eh(e,t,{pageParam:r,direction:o})),initialPageParam:null!=(o=null==t?void 0:t.initialCursor)?o:null}))},prefetchQuery:(e,t)=>r.prefetchQuery((0,ex.default)((0,ex.default)({},t),{},{queryKey:e,queryFn:()=>n.query(...eh(e,t))})),prefetchInfiniteQuery:(e,t)=>{var o;return r.prefetchInfiniteQuery((0,ex.default)((0,ex.default)({},t),{},{queryKey:e,queryFn:({pageParam:r,direction:o})=>n.query(...eh(e,t,{pageParam:r,direction:o})),initialPageParam:null!=(o=null==t?void 0:t.initialCursor)?o:null}))},ensureQueryData:(e,t)=>r.ensureQueryData((0,ex.default)((0,ex.default)({},t),{},{queryKey:e,queryFn:()=>n.query(...eh(e,t))})),invalidateQueries:(e,t,n)=>r.invalidateQueries((0,ex.default)((0,ex.default)({},t),{},{queryKey:e}),n),resetQueries:(e,t,n)=>r.resetQueries((0,ex.default)((0,ex.default)({},t),{},{queryKey:e}),n),refetchQueries:(e,t,n)=>r.refetchQueries((0,ex.default)((0,ex.default)({},t),{},{queryKey:e}),n),cancelQuery:(e,t)=>r.cancelQueries({queryKey:e},t),setQueryData:(e,t,n)=>r.setQueryData(e,t,n),setQueriesData:(e,t,n,o)=>r.setQueriesData((0,ex.default)((0,ex.default)({},t),{},{queryKey:e}),n,o),getQueryData:e=>r.getQueryData(e),setInfiniteQueryData:(e,t,n)=>r.setQueryData(e,t,n),getInfiniteQueryData:e=>r.getQueryData(e),setMutationDefaults:(t,o)=>{let u=t[0];return r.setMutationDefaults(t,"function"==typeof o?o({canonicalMutationFn:t=>n.mutation(...eh([u,{input:t}],e))}):o)},getMutationDefaults:e=>r.getMutationDefaults(e),isMutating:e=>r.isMutating((0,ex.default)((0,ex.default)({},e),{},{exact:!0}))}})({client:s,queryClient:n}),[s,n]),c=el.useMemo(()=>(0,ej.default)({abortOnUnmount:r,queryClient:n,client:s,ssrContext:null!=o?o:null,ssrState:i},a),[r,s,a,n,o,i]);return el.useEffect(()=>{l(e=>!!e&&"mounted")},[]),(0,ea.jsx)(u.Provider,{value:c,children:e.children})},createClient:i,useContext:l,useUtils:l,useQuery:function(t,r,n){var o,u,i,a,c;let{abortOnUnmount:p,client:f,ssrState:d,queryClient:y,prefetchQuery:h}=l(),m=F(t,r,"query"),b=y.getQueryDefaults(m),v=r===g.hT;"undefined"!=typeof window||"prepass"!==d||(null==n||null==(o=n.trpc)?void 0:o.ssr)===!1||(null!=(u=null==n?void 0:n.enabled)?u:null==b?void 0:b.enabled)===!1||v||y.getQueryCache().find({queryKey:m})||h(m,n);let j=s(m,(0,ej.default)((0,ej.default)({},b),n)),O=null!=(i=null!=(a=null==n||null==(c=n.trpc)?void 0:c.abortOnUnmount)?a:null==e?void 0:e.abortOnUnmount)?i:p,P=(0,R.useQuery)((0,ej.default)((0,ej.default)({},j),{},{queryKey:m,queryFn:v?r:async e=>{let t=(0,ej.default)((0,ej.default)({},j),{},{trpc:(0,ej.default)((0,ej.default)({},null==j?void 0:j.trpc),O?{signal:e.signal}:{signal:null})}),r=await f.query(...eh(m,t));return(0,x.Td)(r)?eg(r,y,m):r}}),y);return P.trpc=ev({path:t}),P},usePrefetchQuery:function(t,r,n){var o,u,i;let s=l(),a=F(t,r,"query"),c=r===g.hT,p=null!=(o=null!=(u=null==n||null==(i=n.trpc)?void 0:i.abortOnUnmount)?u:null==e?void 0:e.abortOnUnmount)?o:s.abortOnUnmount;!function(e,t){let r=(0,m.useQueryClient)(void 0);r.getQueryState(e.queryKey)||r.prefetchQuery(e)}((0,ej.default)((0,ej.default)({},n),{},{queryKey:a,queryFn:c?r:e=>{let t={trpc:(0,ej.default)((0,ej.default)({},null==n?void 0:n.trpc),p?{signal:e.signal}:{})};return s.client.query(...eh(a,t))}}))},useSuspenseQuery:function(t,r,n){var o,u,i;let s=l(),a=F(t,r,"query"),c=null!=(o=null!=(u=null==n||null==(i=n.trpc)?void 0:i.abortOnUnmount)?u:null==e?void 0:e.abortOnUnmount)?o:s.abortOnUnmount,p=(0,L.useSuspenseQuery)((0,ej.default)((0,ej.default)({},n),{},{queryKey:a,queryFn:e=>{let t=(0,ej.default)((0,ej.default)({},n),{},{trpc:(0,ej.default)((0,ej.default)({},null==n?void 0:n.trpc),c?{signal:e.signal}:{signal:null})});return s.client.query(...eh(a,t))}}),s.queryClient);return p.trpc=ev({path:t}),[p.data,p]},useQueries:(e,t)=>{let{ssrState:r,queryClient:n,prefetchQuery:o,client:u}=l(),i=e(ed(u));if("undefined"==typeof window&&"prepass"===r)for(let e of i){var s;(null==(s=e.trpc)?void 0:s.ssr)===!1||n.getQueryCache().find({queryKey:e.queryKey})||o(e.queryKey,e)}return(0,X.useQueries)({queries:i.map(e=>(0,ej.default)((0,ej.default)({},e),{},{queryKey:e.queryKey})),combine:null==t?void 0:t.combine},n)},useSuspenseQueries:e=>{let{queryClient:t,client:r}=l(),n=e(ed(r)),o=(0,B.useSuspenseQueries)({queries:n.map(e=>(0,ej.default)((0,ej.default)({},e),{},{queryFn:e.queryFn,queryKey:e.queryKey}))},t);return[o.map(e=>e.data),o]},useMutation:function(e,t){let{client:r,queryClient:n}=l(),u=G(e),i=n.defaultMutationOptions(n.getMutationDefaults(u)),s=(0,H.useMutation)((0,ej.default)((0,ej.default)({},t),{},{mutationKey:u,mutationFn:n=>r.mutation(...eh([e,{input:n}],t)),onSuccess(...e){var r,u;return o({originalFn:()=>{var r,n,o;return null!=(r=null==t||null==(n=t.onSuccess)?void 0:n.call(t,...e))?r:null==i||null==(o=i.onSuccess)?void 0:o.call(i,...e)},queryClient:n,meta:null!=(r=null!=(u=null==t?void 0:t.meta)?u:null==i?void 0:i.meta)?r:{}})}}),n);return s.trpc=ev({path:e}),s},useSubscription:function(e,t,r){var n;let o=null!=(n=null==r?void 0:r.enabled)?n:t!==g.hT,u=(0,g.EN)(F(e,t,"any")),{client:i}=l(),s=el.useRef(r);el.useEffect(()=>{s.current=r});let[p]=el.useState(new Set([])),f=el.useCallback(e=>{p.add(e)},[p]),d=el.useRef(null),y=el.useCallback(e=>{let t=m.current,r=m.current=e(t),n=!1;for(let e of p)if(t[e]!==r[e]){n=!0;break}n&&v(eO(r,f))},[f,p]),h=el.useCallback(()=>{var r;if(null==(r=d.current)||r.unsubscribe(),!o)return void y(()=>(0,ej.default)((0,ej.default)({},a),{},{reset:h}));y(()=>(0,ej.default)((0,ej.default)({},c),{},{reset:h})),d.current=i.subscription(e.join("."),null!=t?t:void 0,{onStarted:()=>{var e,t;null==(e=(t=s.current).onStarted)||e.call(t),y(e=>(0,ej.default)((0,ej.default)({},e),{},{status:"pending",error:null}))},onData:e=>{var t,r;null==(t=(r=s.current).onData)||t.call(r,e),y(t=>(0,ej.default)((0,ej.default)({},t),{},{status:"pending",data:e,error:null}))},onError:e=>{var t,r;null==(t=(r=s.current).onError)||t.call(r,e),y(t=>(0,ej.default)((0,ej.default)({},t),{},{status:"error",error:e}))},onConnectionStateChange:e=>{y(t=>{switch(e.state){case"idle":return(0,ej.default)((0,ej.default)({},t),{},{status:e.state,error:null,data:void 0});case"connecting":return(0,ej.default)((0,ej.default)({},t),{},{error:e.error,status:e.state});case"pending":return t}})},onComplete:()=>{var e,t;null==(e=(t=s.current).onComplete)||e.call(t),y(e=>(0,ej.default)((0,ej.default)({},e),{},{status:"idle",error:null,data:void 0}))}})},[i,u,o,y]);el.useEffect(()=>(h(),()=>{var e;null==(e=d.current)||e.unsubscribe()}),[h]);let m=el.useRef(o?(0,ej.default)((0,ej.default)({},c),{},{reset:h}):(0,ej.default)((0,ej.default)({},a),{},{reset:h})),[b,v]=el.useState(eO(m.current,f));return b},useInfiniteQuery:function(e,t,r){var n,o,u,i,a;let{client:c,ssrState:p,prefetchInfiniteQuery:f,queryClient:d,abortOnUnmount:y}=l(),h=F(e,t,"infinite"),m=d.getQueryDefaults(h),b=t===g.hT;"undefined"!=typeof window||"prepass"!==p||(null==r||null==(n=r.trpc)?void 0:n.ssr)===!1||(null!=(o=null==r?void 0:r.enabled)?o:null==m?void 0:m.enabled)===!1||b||d.getQueryCache().find({queryKey:h})||f(h,(0,ej.default)((0,ej.default)({},m),r));let v=s(h,(0,ej.default)((0,ej.default)({},m),r)),x=null!=(u=null==r||null==(i=r.trpc)?void 0:i.abortOnUnmount)?u:y,j=(0,W.useInfiniteQuery)((0,ej.default)((0,ej.default)({},v),{},{initialPageParam:null!=(a=r.initialCursor)?a:null,persister:r.persister,queryKey:h,queryFn:b?t:e=>{var t;let n=(0,ej.default)((0,ej.default)({},v),{},{trpc:(0,ej.default)((0,ej.default)({},null==v?void 0:v.trpc),x?{signal:e.signal}:{signal:null})});return c.query(...eh(h,n,{pageParam:null!=(t=e.pageParam)?t:r.initialCursor,direction:e.direction}))}}),d);return j.trpc=ev({path:e}),j},usePrefetchInfiniteQuery:function(e,t,r){var n,o,u;let i=l(),a=F(e,t,"infinite"),c=i.queryClient.getQueryDefaults(a),p=t===g.hT,f=s(a,(0,ej.default)((0,ej.default)({},c),r)),d=null!=(n=null==r||null==(o=r.trpc)?void 0:o.abortOnUnmount)?n:i.abortOnUnmount;!function(e,t){let r=(0,m.useQueryClient)(void 0);r.getQueryState(e.queryKey)||r.prefetchInfiniteQuery(e)}((0,ej.default)((0,ej.default)({},r),{},{initialPageParam:null!=(u=r.initialCursor)?u:null,queryKey:a,queryFn:p?t:e=>{var t;let n=(0,ej.default)((0,ej.default)({},f),{},{trpc:(0,ej.default)((0,ej.default)({},null==f?void 0:f.trpc),d?{signal:e.signal}:{})});return i.client.query(...eh(a,n,{pageParam:null!=(t=e.pageParam)?t:r.initialCursor,direction:e.direction}))}}))},useSuspenseInfiniteQuery:function(e,t,r){var n,o,u;let i=l(),a=F(e,t,"infinite"),c=i.queryClient.getQueryDefaults(a),p=s(a,(0,ej.default)((0,ej.default)({},c),r)),f=null!=(n=null==r||null==(o=r.trpc)?void 0:o.abortOnUnmount)?n:i.abortOnUnmount,d=(0,z.useSuspenseInfiniteQuery)((0,ej.default)((0,ej.default)({},r),{},{initialPageParam:null!=(u=r.initialCursor)?u:null,queryKey:a,queryFn:e=>{var t;let n=(0,ej.default)((0,ej.default)({},p),{},{trpc:(0,ej.default)((0,ej.default)({},null==p?void 0:p.trpc),f?{signal:e.signal}:{})});return i.client.query(...eh(a,n,{pageParam:null!=(t=e.pageParam)?t:r.initialCursor,direction:e.direction}))}}),i.queryClient);return d.trpc=ev({path:e}),[d.data,d]}}}var ew=((e,t,r)=>(r=null!=e?o(s(e)):{},((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(var o,s=l(t),c=0,p=s.length;c<p;c++)o=s[c],a.call(e,o)||o===r||u(e,o,{get:(e=>t[e]).bind(null,o),enumerable:!(n=i(t,o))||n.enumerable});return e})(!t&&e&&e.__esModule?r:u(r,"default",{value:e,enumerable:!0}),e)))(h());function e_(e){let t=eP(e),r=function(e){let{config:t}=e,r=(0,v.K)(e.transformer);return n=>{var o,u;let i=eP(e),l=o=>{var u,l;let[s]=(0,el.useState)(()=>{var r;if(o.trpc)return o.trpc;let n=t({}),u=null!=(r=n.queryClient)?r:new eu(n.queryClientConfig),l=i.createClient(n);return{abortOnUnmount:n.abortOnUnmount,queryClient:u,trpcClient:l,ssrState:!!e.ssr&&"mounting",ssrContext:null}}),{queryClient:a,trpcClient:c,ssrState:p,ssrContext:f}=s,d=null==(u=o.pageProps)?void 0:u.trpcState,y=el.useMemo(()=>d?r.input.deserialize(d):d,[d]);return(0,ea.jsx)(i.Provider,{abortOnUnmount:null!=(l=s.abortOnUnmount)&&l,client:c,queryClient:a,ssrState:p,ssrContext:f,children:(0,ea.jsx)(m.QueryClientProvider,{client:a,children:(0,ea.jsx)(b.HydrationBoundary,{state:y,children:(0,ea.jsx)(n,(0,ew.default)({},o))})})})};e.ssr?e.ssrPrepass({parent:e,AppOrPage:n,WithTRPC:l}):n.getInitialProps&&(l.getInitialProps=async e=>{var t;let r,o=!!e.Component,u={},i=await n.getInitialProps(e),l=o?null!=(t=i.pageProps)?t:{}:i;return r=u=(0,ew.default)((0,ew.default)({},l),u),o?{pageProps:r}:r});let s=null!=(o=null!=(u=n.displayName)?u:n.name)?o:"Component";return l.displayName=`withTRPC(${s})`,l}}(e),n=(0,ei.vX)(({path:e,args:r})=>{var n;let o=[...e],u=o.pop();if("useMutation"===u)return t[u](o,...r);if("_def"===u)return{path:o};let[i,...l]=r,s=null!=(n=l[0])?n:{};return t[u](o,i,s)});return(0,ei.U6)(e=>"useContext"===e||"useUtils"===e?()=>{let e=t.useUtils();return(0,el.useMemo)(()=>(function(e){let t=(0,K.Xq)(e.client),r=(0,ei.vX)(t=>{let r=[...t.path],n=r.pop(),o=[...t.args],u=o.shift(),i=F(r,u,(e=>{switch(e){case"queryOptions":case"fetch":case"ensureData":case"prefetch":case"getData":case"setData":case"setQueriesData":return"query";case"infiniteQueryOptions":case"fetchInfinite":case"prefetchInfinite":case"getInfiniteData":case"setInfiniteData":return"infinite";case"setMutationDefaults":case"getMutationDefaults":case"isMutating":case"cancel":case"invalidate":case"refetch":case"reset":return"any"}})(n));return({infiniteQueryOptions:()=>e.infiniteQueryOptions(r,i,o[0]),queryOptions:()=>e.queryOptions(r,i,...o),fetch:()=>e.fetchQuery(i,...o),fetchInfinite:()=>e.fetchInfiniteQuery(i,o[0]),prefetch:()=>e.prefetchQuery(i,...o),prefetchInfinite:()=>e.prefetchInfiniteQuery(i,o[0]),ensureData:()=>e.ensureQueryData(i,...o),invalidate:()=>e.invalidateQueries(i,...o),reset:()=>e.resetQueries(i,...o),refetch:()=>e.refetchQueries(i,...o),cancel:()=>e.cancelQuery(i,...o),setData:()=>{e.setQueryData(i,o[0],o[1])},setQueriesData:()=>e.setQueriesData(i,o[0],o[1],o[2]),setInfiniteData:()=>{e.setInfiniteQueryData(i,o[0],o[1])},getData:()=>e.getQueryData(i),getInfiniteData:()=>e.getInfiniteQueryData(i),setMutationDefaults:()=>e.setMutationDefaults(G(r),u),getMutationDefaults:()=>e.getMutationDefaults(G(r)),isMutating:()=>e.isMutating({mutationKey:G(r)})})[n]()});return(0,ei.U6)(n=>"client"===n?t:ec.includes(n)?e[n]:r[n])})(e),[e])}:"useQueries"===e?t.useQueries:"useSuspenseQueries"===e?t.useSuspenseQueries:"withTRPC"===e?r:n[e])}},1675:(e,t,r)=>{r.d(t,{Gv:()=>o,Td:()=>i});let n={INTERNAL_SERVER_ERROR:-32603,BAD_GATEWAY:-32603,SERVICE_UNAVAILABLE:-32603,GATEWAY_TIMEOUT:-32603};function o(e){return!!e&&!Array.isArray(e)&&"object"==typeof e}n.BAD_GATEWAY,n.SERVICE_UNAVAILABLE,n.GATEWAY_TIMEOUT,n.INTERNAL_SERVER_ERROR;let u="function"==typeof Symbol&&!!Symbol.asyncIterator;function i(e){return u&&o(e)&&Symbol.asyncIterator in e}},4923:(e,t,r)=>{r.d(t,{U6:()=>y,Vh:()=>v,f1:()=>c,jr:()=>g,vX:()=>d});var n=Object.create,o=Object.defineProperty,u=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,l=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,a=(e,t)=>function(){return t||(0,e[i(e)[0]])((t={exports:{}}).exports,t),t.exports},c=(e,t,r)=>(r=null!=e?n(l(e)):{},((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(var l,a=i(t),c=0,p=a.length;c<p;c++)l=a[c],s.call(e,l)||l===r||o(e,l,{get:(e=>t[e]).bind(null,l),enumerable:!(n=u(t,l))||n.enumerable});return e})(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e));let p=()=>{},f=e=>{Object.freeze&&Object.freeze(e)},d=e=>(function e(t,r,n){let o=r.join(".");return null!=n[o]||(n[o]=new Proxy(p,{get(o,u){if("string"==typeof u&&"then"!==u)return e(t,[...r,u],n)},apply(e,n,o){let u=r[r.length-1],i={args:o,path:r};return"call"===u?i={args:o.length>=2?[o[1]]:[],path:r.slice(0,-1)}:"apply"===u&&(i={args:o.length>=2?o[1]:[],path:r.slice(0,-1)}),f(i.args),f(i.path),t(i)}})),n[o]})(e,[],Object.create(null)),y=e=>new Proxy(p,{get(t,r){if("then"!==r)return e(r)}});var h=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(e){return t.exports=r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t.exports.__esModule=!0,t.exports.default=t.exports,r(e)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),m=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=h().default;t.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},t.exports.__esModule=!0,t.exports.default=t.exports}}),b=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=h().default,n=m();t.exports=function(e){var t=n(e,"string");return"symbol"==r(t)?t:t+""},t.exports.__esModule=!0,t.exports.default=t.exports}}),v=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=b();t.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},t.exports.__esModule=!0,t.exports.default=t.exports}}),g=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=v();function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}t.exports=function(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?n(Object(o),!0).forEach(function(t){r(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e},t.exports.__esModule=!0,t.exports.default=t.exports}});c(g(),1)},4972:(e,t,r)=>{r.d(t,{K:()=>n});function n(e){return e?"input"in e?e:{input:e,output:e}:{input:{serialize:e=>e,deserialize:e=>e},output:{serialize:e=>e,deserialize:e=>e}}}},9761:(e,t,r)=>{r.d(t,{Ke:()=>W,XT:()=>$,Xq:()=>B,n2:()=>V,fu:()=>U});var n=Object.create,o=Object.defineProperty,u=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,l=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,a=(e,t)=>function(){return t||(0,e[i(e)[0]])((t={exports:{}}).exports,t),t.exports},c=(e,t,r)=>(r=null!=e?n(l(e)):{},((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(var l,a=i(t),c=0,p=a.length;c<p;c++)l=a[c],s.call(e,l)||l===r||o(e,l,{get:(e=>t[e]).bind(null,l),enumerable:!(n=u(t,l))||n.enumerable});return e})(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e)),p=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(e){return t.exports=r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t.exports.__esModule=!0,t.exports.default=t.exports,r(e)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),f=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=p().default;t.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},t.exports.__esModule=!0,t.exports.default=t.exports}}),d=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=p().default,n=f();t.exports=function(e){var t=n(e,"string");return"symbol"==r(t)?t:t+""},t.exports.__esModule=!0,t.exports.default=t.exports}}),y=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=d();t.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},t.exports.__esModule=!0,t.exports.default=t.exports}}),h=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=y();function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}t.exports=function(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?n(Object(o),!0).forEach(function(t){r(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e},t.exports.__esModule=!0,t.exports.default=t.exports}});function m(e){let t={subscribe(t){let r=null,n=!1,o=!1,u=!1;function i(){if(null===r){u=!0;return}!o&&(o=!0,"function"==typeof r?r():r&&r.unsubscribe())}return r=e({next(e){var r;n||null==(r=t.next)||r.call(t,e)},error(e){var r;n||(n=!0,null==(r=t.error)||r.call(t,e),i())},complete(){var e;n||(n=!0,null==(e=t.complete)||e.call(t),i())}}),u&&i(),{unsubscribe:i}},pipe:(...e)=>e.reduce(b,t)};return t}function b(e,t){return t(e)}var v=r(1675),g=c(y(),1),x=c(h(),1),j=class e extends Error{constructor(t,r){var n,o;let u=null==r?void 0:r.cause;super(t,{cause:u}),(0,g.default)(this,"cause",void 0),(0,g.default)(this,"shape",void 0),(0,g.default)(this,"data",void 0),(0,g.default)(this,"meta",void 0),this.meta=null==r?void 0:r.meta,this.cause=u,this.shape=null==r||null==(n=r.result)?void 0:n.error,this.data=null==r||null==(o=r.result)?void 0:o.error.data,this.name="TRPCClientError",Object.setPrototypeOf(this,e.prototype)}static from(t,r={}){return t instanceof j?(r.meta&&(t.meta=(0,x.default)((0,x.default)({},t.meta),r.meta)),t):(0,v.Gv)(t)&&(0,v.Gv)(t.error)&&"number"==typeof t.error.code&&"string"==typeof t.error.message?new e(t.error.message,(0,x.default)((0,x.default)({},r),{},{result:t})):new e("string"==typeof t?t:(0,v.Gv)(t)&&"string"==typeof t.message?t.message:"Unknown error",(0,x.default)((0,x.default)({},r),{},{cause:t}))}},O=r(4972),P=c(h(),1);let w={query:"GET",mutation:"POST",subscription:"PATCH"};function _(e){return"input"in e?e.transformer.input.serialize(e.input):function(e){let t={};for(let r=0;r<e.length;r++){let n=e[r];t[r]=n}return t}(e.inputs.map(t=>e.transformer.input.serialize(t)))}let S=e=>{let t=e.url.split("?"),r=t[0].replace(/\/$/,"")+"/"+e.path,n=[];if(t[1]&&n.push(t[1]),"inputs"in e&&n.push("batch=1"),"query"===e.type||"subscription"===e.type){let t=_(e);void 0!==t&&"POST"!==e.methodOverride&&n.push(`input=${encodeURIComponent(JSON.stringify(t))}`)}return n.length&&(r+="?"+n.join("&")),r},Q=e=>{if("query"===e.type&&"POST"!==e.methodOverride)return;let t=_(e);return void 0!==t?JSON.stringify(t):void 0};var q=class extends Error{constructor(){let e="AbortError";super(e),this.name=e,this.message=e}};async function C(e){var t,r,n=e.signal;if(null==n?void 0:n.aborted){if(null==(r=n.throwIfAborted)||r.call(n),"undefined"!=typeof DOMException)throw new DOMException("AbortError","AbortError");throw new q}let o=e.getUrl(e),u=e.getBody(e),{type:i}=e,l=await (async()=>{let t=await e.headers();return Symbol.iterator in t?Object.fromEntries(t):t})(),s=(0,P.default)((0,P.default)((0,P.default)({},e.contentTypeHeader?{"content-type":e.contentTypeHeader}:{}),e.trpcAcceptHeader?{"trpc-accept":e.trpcAcceptHeader}:void 0),l);return(function(e){if(e)return e;if("undefined"!=typeof window&&"function"==typeof window.fetch)return window.fetch;if("undefined"!=typeof globalThis&&"function"==typeof globalThis.fetch)return globalThis.fetch;throw Error("No fetch implementation found")})(e.fetch)(o,{method:null!=(t=e.methodOverride)?t:w[i],signal:e.signal,body:u,headers:s})}async function D(e){let t={},r=await C(e);t.response=r;let n=await r.json();return t.responseJSON=n,{json:n,meta:t}}c(h(),1);var E=r(4923);(0,E.f1)((0,E.Vh)(),1);var M=(0,E.f1)((0,E.jr)(),1),I=class extends Error{constructor(){super("Unable to transform response from server")}};(0,E.f1)((0,E.jr)(),1),Symbol("lazy"),Symbol();let T=()=>{throw Error("Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new")};function k(e){let t=null,r=null;function n(){let n=function(t){let r=[[]],n=0;for(;;){var o,u;let i=t[n];if(!i)break;let l=r[r.length-1];if(i.aborted){null==(o=i.reject)||o.call(i,Error("Aborted")),n++;continue}if(e.validate(l.concat(i).map(e=>e.key))){l.push(i),n++;continue}if(0===l.length){null==(u=i.reject)||u.call(i,Error("Input is too big for a single dispatch")),n++;continue}r.push([])}return r}(t);for(let o of(clearTimeout(r),r=null,t=null,n)){if(!o.length)continue;let t={items:o};for(let e of o)e.batch=t;e.fetch(t.items.map(e=>e.key)).then(async e=>{for(let n of(await Promise.all(e.map(async(e,r)=>{var n,o;let u=t.items[r];try{let t=await Promise.resolve(e);null==(n=u.resolve)||n.call(u,t)}catch(e){null==(o=u.reject)||o.call(u,e)}u.batch=null,u.reject=null,u.resolve=null})),t.items)){var r;null==(r=n.reject)||r.call(n,Error("Missing result")),n.batch=null}}).catch(e=>{for(let n of t.items){var r;null==(r=n.reject)||r.call(n,e),n.batch=null}})}}return{load:function(e){let o={aborted:!1,key:e,batch:null,resolve:T,reject:T},u=new Promise((e,r)=>{o.reject=r,o.resolve=e,null!=t||(t=[]),t.push(o)});return null!=r||(r=setTimeout(n)),u}}}var A=c(h(),1);function U(e){var t,r;let n={url:e.url.toString(),fetch:e.fetch,transformer:(0,O.K)(e.transformer),methodOverride:e.methodOverride},o=null!=(t=e.maxURLLength)?t:1/0,u=null!=(r=e.maxItems)?r:1/0;return()=>{let t=t=>({validate(e){if(o===1/0&&u===1/0)return!0;if(e.length>u)return!1;let r=e.map(e=>e.path).join(","),i=e.map(e=>e.input);return S((0,A.default)((0,A.default)({},n),{},{type:t,path:r,inputs:i,signal:null})).length<=o},async fetch(r){let o,u=r.map(e=>e.path).join(","),i=r.map(e=>e.input),l=function(...e){let t=new AbortController,r=e.length,n=0,o=()=>{++n===r&&t.abort()};for(let t of e)(null==t?void 0:t.aborted)?o():null==t||t.addEventListener("abort",o,{once:!0});return t.signal}(...r.map(e=>e.signal)),s=await (o=(0,A.default)((0,A.default)({},n),{},{path:u,inputs:i,type:t,headers:()=>e.headers?"function"==typeof e.headers?e.headers({opList:r}):e.headers:{},signal:l}),D((0,P.default)((0,P.default)({},o),{},{contentTypeHeader:"application/json",getUrl:S,getBody:Q})));return(Array.isArray(s.json)?s.json:r.map(()=>s.json)).map(e=>({meta:s.meta,json:e}))}}),r={query:k(t("query")),mutation:k(t("mutation"))};return({op:e})=>m(t=>{let o;if("subscription"===e.type)throw Error("Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`");return r[e.type].load(e).then(e=>{o=e;let r=function(e,t){let r;try{r=function(e,t){if("error"in e){let r=t.deserialize(e.error);return{ok:!1,error:(0,M.default)((0,M.default)({},e),{},{error:r})}}return{ok:!0,result:(0,M.default)((0,M.default)({},e.result),(!e.result.type||"data"===e.result.type)&&{type:"data",data:t.deserialize(e.result.data)})}}(e,t)}catch(e){throw new I}if(!r.ok&&(!(0,v.Gv)(r.error.error)||"number"!=typeof r.error.error.code)||r.ok&&!(0,v.Gv)(r.result))throw new I;return r}(e.json,n.transformer.output);if(!r.ok)return void t.error(j.from(r.error,{meta:e.meta}));t.next({context:e.meta,result:r.result}),t.complete()}).catch(e=>{t.error(j.from(e,{meta:null==o?void 0:o.meta}))}),()=>{}})}}c(h(),1),Symbol();let N=(e,...t)=>"function"==typeof e?e(...t):e;async function F(e){let t=await N(e.url);if(!e.connectionParams)return t;let r=t.includes("?")?"&":"?";return t+`${r}connectionParams=1`}async function G(e){return JSON.stringify({method:"connectionParams",data:await N(e)})}c(y(),1),c(y(),1);var K=c(y(),1),R=class e{constructor(t){var r;if((0,K.default)(this,"id",++e.connectCount),(0,K.default)(this,"WebSocketPonyfill",void 0),(0,K.default)(this,"urlOptions",void 0),(0,K.default)(this,"keepAliveOpts",void 0),(0,K.default)(this,"wsObservable",function(e){let t=null,r=[],n=m(e=>(void 0!==t&&e.next(t),r.push(e),()=>{r.splice(r.indexOf(e),1)}));return n.next=e=>{if(t!==e)for(let n of(t=e,r))n.next(e)},n.get=()=>t,n}(0)),(0,K.default)(this,"openPromise",null),this.WebSocketPonyfill=null!=(r=t.WebSocketPonyfill)?r:WebSocket,!this.WebSocketPonyfill)throw Error("No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill");this.urlOptions=t.urlOptions,this.keepAliveOpts=t.keepAlive}get ws(){return this.wsObservable.get()}set ws(e){this.wsObservable.next(e)}isOpen(){return!!this.ws&&this.ws.readyState===this.WebSocketPonyfill.OPEN&&!this.openPromise}isClosed(){return!!this.ws&&(this.ws.readyState===this.WebSocketPonyfill.CLOSING||this.ws.readyState===this.WebSocketPonyfill.CLOSED)}async open(){var t=this;if(t.openPromise)return t.openPromise;t.id=++e.connectCount;let r=F(t.urlOptions).then(e=>new t.WebSocketPonyfill(e));t.openPromise=r.then(async e=>{t.ws=e,e.addEventListener("message",function({data:e}){"PING"===e&&this.send("PONG")}),t.keepAliveOpts.enabled&&function(e,{intervalMs:t,pongTimeoutMs:r}){let n,o;function u(){n=setTimeout(()=>{e.send("PING"),o=setTimeout(()=>{e.close()},r)},t)}e.addEventListener("open",u),e.addEventListener("message",({data:e})=>{clearTimeout(n),u(),"PONG"===e&&(clearTimeout(o),clearTimeout(n),u())}),e.addEventListener("close",()=>{clearTimeout(n),clearTimeout(o)})}(e,t.keepAliveOpts),e.addEventListener("close",()=>{t.ws===e&&(t.ws=null)}),await function(e){let t,r,{promise:n,resolve:o,reject:u}={promise:new Promise((e,n)=>{t=e,r=n}),resolve:t,reject:r};return e.addEventListener("open",()=>{e.removeEventListener("error",u),o()}),e.addEventListener("error",u),n}(e),t.urlOptions.connectionParams&&e.send(await G(t.urlOptions.connectionParams))});try{await t.openPromise}finally{t.openPromise=null}}async close(){var e;try{await this.openPromise}finally{null==(e=this.ws)||e.close()}}};(0,K.default)(R,"connectCount",0),c(y(),1),c(h(),1);var L=c(y(),1),H=c(h(),1),W=class{constructor(e){(0,L.default)(this,"links",void 0),(0,L.default)(this,"runtime",void 0),(0,L.default)(this,"requestId",void 0),this.requestId=0,this.runtime={},this.links=e.links.map(e=>e(this.runtime))}$request(e){var t,r;return(r={links:this.links,op:(0,H.default)((0,H.default)({},e),{},{context:null!=(t=e.context)?t:{},id:++this.requestId})},m(e=>(function e(t=0,n=r.op){let o=r.links[t];if(!o)throw Error("No more links to execute - did you forget to add an ending link?");return o({op:n,next:r=>e(t+1,r)})})().subscribe(e))).pipe(e=>{let t=0,r=null,n=[];return m(o=>(t++,n.push(o),r||(r=e.subscribe({next(e){for(let r of n){var t;null==(t=r.next)||t.call(r,e)}},error(e){for(let r of n){var t;null==(t=r.error)||t.call(r,e)}},complete(){for(let t of n){var e;null==(e=t.complete)||e.call(t)}}})),{unsubscribe(){if(0==--t&&r){let e=r;r=null,e.unsubscribe()}let e=n.findIndex(e=>e===o);e>-1&&n.splice(e,1)}}))})}async requestAsPromise(e){try{let t=this.$request(e);return(await function(e){let t=new AbortController;return new Promise((r,n)=>{let o=!1;function u(){o||(o=!0,i.unsubscribe())}t.signal.addEventListener("abort",()=>{n(t.signal.reason)});let i=e.subscribe({next(e){o=!0,r(e),u()},error(e){n(e)},complete(){t.abort(),u()}})})}(t)).result.data}catch(e){throw j.from(e)}}query(e,t,r){return this.requestAsPromise({type:"query",path:e,input:t,context:null==r?void 0:r.context,signal:null==r?void 0:r.signal})}mutation(e,t,r){return this.requestAsPromise({type:"mutation",path:e,input:t,context:null==r?void 0:r.context,signal:null==r?void 0:r.signal})}subscription(e,t,r){return this.$request({type:"subscription",path:e,input:t,context:r.context,signal:r.signal}).subscribe({next(e){var t,n,o,u;switch(e.result.type){case"state":null==(t=r.onConnectionStateChange)||t.call(r,e.result);break;case"started":null==(n=r.onStarted)||n.call(r,{context:e.context});break;case"stopped":null==(o=r.onStopped)||o.call(r);break;case"data":case void 0:null==(u=r.onData)||u.call(r,e.result.data)}},error(e){var t;null==(t=r.onError)||t.call(r,e)},complete(){var e;null==(e=r.onComplete)||e.call(r)}})}};let z=Symbol.for("trpc_untypedClient"),X={query:"query",mutate:"mutation",subscribe:"subscription"};function B(e){let t=(0,E.vX)(({path:t,args:r})=>{let n=[...t],o=X[n.pop()],u=n.join(".");return e[o](u,...r)});return(0,E.U6)(r=>r===z?e:t[r])}function $(e){return B(new W(e))}function V(e){return e[z]}c(h(),1),c(h(),1),c(a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(e,t){function r(e){function t(e){if(Object(e)!==e)return Promise.reject(TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(r=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var r=this.s.return;return void 0===r?Promise.resolve({value:e,done:!0}):t(r.apply(this.s,arguments))},throw:function(e){var r=this.s.return;return void 0===r?Promise.reject(e):t(r.apply(this.s,arguments))}},new r(e)}t.exports=function(e){var t,n,o,u=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,o=Symbol.iterator);u--;){if(n&&null!=(t=e[n]))return t.call(e);if(o&&null!=(t=e[o]))return new r(t.call(e));n="@@asyncIterator",o="@@iterator"}throw TypeError("Object is not async iterable")},t.exports.__esModule=!0,t.exports.default=t.exports}})(),1),c(h(),1);var J=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js"(e,t){t.exports=function(){var e="function"==typeof SuppressedError?SuppressedError:function(e,t){var r=Error();return r.name="SuppressedError",r.error=e,r.suppressed=t,r},t={},r=[];function n(e,t){if(null!=t){if(Object(t)!==t)throw TypeError("using declarations can only be used with objects, functions, null, or undefined.");if(e)var n=t[Symbol.asyncDispose||Symbol.for("Symbol.asyncDispose")];if(void 0===n&&(n=t[Symbol.dispose||Symbol.for("Symbol.dispose")],e))var o=n;if("function"!=typeof n)throw TypeError("Object is not disposable.");o&&(n=function(){try{o.call(t)}catch(e){return Promise.reject(e)}}),r.push({v:t,d:n,a:e})}else e&&r.push({d:t,a:e});return t}return{e:t,u:n.bind(null,!1),a:n.bind(null,!0),d:function(){var n,o=this.e,u=0;function i(){for(;n=r.pop();)try{if(!n.a&&1===u)return u=0,r.push(n),Promise.resolve().then(i);if(n.d){var e=n.d.call(n.v);if(n.a)return u|=2,Promise.resolve(e).then(i,l)}else u|=1}catch(e){return l(e)}if(1===u)return o!==t?Promise.reject(o):Promise.resolve();if(o!==t)throw o}function l(r){return o=o!==t?new e(r,o):r,i()}return i()}}},t.exports.__esModule=!0,t.exports.default=t.exports}}),Y=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js"(e,t){t.exports=function(e,t){this.v=e,this.k=t},t.exports.__esModule=!0,t.exports.default=t.exports}}),Z=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js"(e,t){var r=Y();t.exports=function(e){return new r(e,0)},t.exports.__esModule=!0,t.exports.default=t.exports}}),ee=a({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js"(e,t){var r=Y();function n(e){var t,n;function o(t,n){try{var i=e[t](n),l=i.value,s=l instanceof r;Promise.resolve(s?l.v:l).then(function(r){if(s){var n="return"===t?"return":"next";if(!l.k||r.done)return o(n,r);r=e[n](r).value}u(i.done?"return":"normal",r)},function(e){o("throw",e)})}catch(e){u("throw",e)}}function u(e,r){switch(e){case"return":t.resolve({value:r,done:!0});break;case"throw":t.reject(r);break;default:t.resolve({value:r,done:!1})}(t=t.next)?o(t.key,t.arg):n=null}this._invoke=function(e,r){return new Promise(function(u,i){var l={key:e,arg:r,resolve:u,reject:i,next:null};n?n=n.next=l:(t=n=l,o(e,r))})},"function"!=typeof e.return&&(this.return=void 0)}n.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},n.prototype.next=function(e){return this._invoke("next",e)},n.prototype.throw=function(e){return this._invoke("throw",e)},n.prototype.return=function(e){return this._invoke("return",e)},t.exports=function(e){return function(){return new n(e.apply(this,arguments))}},t.exports.__esModule=!0,t.exports.default=t.exports}});c(J(),1),c(Z(),1),c(ee(),1),c(h(),1)}}]);