(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1808:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(5155),a=t(2115),l=t(781),n=t(9761);let i=(0,l.R)({config:()=>({links:[(0,n.fu)({url:"/api/trpc"})]}),ssr:!1});function c(){let[e,s]=(0,a.useState)(""),[t,l]=(0,a.useState)(),[n,c]=(0,a.useState)([]),o=(0,a.useRef)(null),d=i.chat.sendMessage.useMutation({onSuccess:e=>{c(s=>[...s,e.message]),l(e.conversation_id)},onError:e=>{console.error("Failed to send message:",e)}});(0,a.useEffect)(()=>{o.current&&(o.current.style.height="auto",o.current.style.height="".concat(o.current.scrollHeight,"px"))},[e]);let u=async()=>{if(!e.trim()||d.isPending)return;let r=e.trim(),a={id:"user_".concat(Date.now()),role:"user",content:r,timestamp:Date.now()};c(e=>[...e,a]),s("");try{await d.mutateAsync({message:r,conversation_id:t})}catch(e){console.error("Failed to send message:",e)}};return(0,r.jsxs)("div",{className:"flex flex-col h-screen bg-gray-50",children:[(0,r.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-800",children:"Function Calling Tools"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"AI助手，支持天气查询、翻译和文本摘要"})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto px-6 py-4 space-y-4",children:[0===n.length?(0,r.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,r.jsx)("p",{className:"text-lg mb-2",children:"\uD83D\uDC4B 欢迎使用 Function Calling Tools"}),(0,r.jsx)("p",{className:"text-sm",children:"我可以帮您查询天气、翻译文本或生成摘要。请输入您的需求！"})]}):n.map(e=>(0,r.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:"max-w-xs lg:max-w-md px-4 py-2 rounded-lg ".concat("user"===e.role?"bg-blue-500 text-white":"bg-white text-gray-800 border border-gray-200"),children:[(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content}),e.tool_calls&&e.tool_calls.length>0&&(0,r.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:["\uD83D\uDD27 使用了 ",e.tool_calls.length," 个工具"]})]})},e.id)),d.isPending&&(0,r.jsx)("div",{className:"flex justify-start",children:(0,r.jsx)("div",{className:"bg-white text-gray-800 border border-gray-200 px-4 py-2 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"}),(0,r.jsx)("span",{className:"text-sm",children:"AI正在思考..."})]})})})]}),(0,r.jsx)("div",{className:"bg-white border-t border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-end space-x-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("textarea",{ref:o,value:e,onChange:e=>s(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),u())},placeholder:"输入您的消息...",className:"w-full px-4 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:1,disabled:d.isPending})}),(0,r.jsx)("button",{onClick:u,disabled:!e.trim()||d.isPending,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d.isPending?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):"发送"})]})})]})}function o(){return(0,r.jsx)(c,{})}},2530:(e,s,t)=>{Promise.resolve().then(t.bind(t,1808))}},e=>{e.O(0,[869,781,441,964,358],()=>e(e.s=2530)),_N_E=e.O()}]);