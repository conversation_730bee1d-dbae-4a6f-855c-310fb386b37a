"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[869],{5:(t,e,s)=>{s.d(e,{t:()=>l});var r=s(2115),i=s(7165),n=s(2020),a=s(6715),o=s(382),u=s(2450),h=s(1581),c=s(4791);function l(t,e,s){var l,d,p,f,y;let v=(0,h.useIsRestoring)(),b=(0,o.useQueryErrorResetBoundary)(),m=(0,a.useQueryClient)(s),g=m.defaultQueryOptions(t);null==(d=m.getDefaultOptions().queries)||null==(l=d._experimental_beforeQuery)||l.call(d,g),g._optimisticResults=v?"isRestoring":"optimistic",(0,c.jv)(g),(0,u.LJ)(g,b),(0,u.wZ)(b);let R=!m.getQueryCache().get(g.queryHash),[O]=r.useState(()=>new e(m,g)),S=O.getOptimisticResult(g),C=!v&&!1!==t.subscribed;if(r.useSyncExternalStore(r.useCallback(t=>{let e=C?O.subscribe(i.jG.batchCalls(t)):n.lQ;return O.updateResult(),e},[O,C]),()=>O.getCurrentResult(),()=>O.getCurrentResult()),r.useEffect(()=>{O.setOptions(g)},[g,O]),(0,c.EU)(g,S))throw(0,c.iL)(g,O,b);if((0,u.$1)({result:S,errorResetBoundary:b,throwOnError:g.throwOnError,query:m.getQueryCache().get(g.queryHash),suspense:g.suspense}))throw S.error;if(null==(f=m.getDefaultOptions().queries)||null==(p=f._experimental_afterQuery)||p.call(f,g,S),g.experimental_prefetchInRender&&!n.S$&&(0,c.nE)(S,v)){let t=R?(0,c.iL)(g,O,b):null==(y=m.getQueryCache().get(g.queryHash))?void 0:y.promise;null==t||t.catch(n.lQ).finally(()=>{O.updateResult()})}return g.notifyOnChangeProps?S:O.trackResult(S)}},382:(t,e,s)=>{s.d(e,{QueryErrorResetBoundary:()=>u,useQueryErrorResetBoundary:()=>o});var r=s(2115),i=s(5155);function n(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var a=r.createContext(n()),o=()=>r.useContext(a),u=t=>{let{children:e}=t,[s]=r.useState(()=>n());return(0,i.jsx)(a.Provider,{value:s,children:"function"==typeof e?e(s):e})}},920:(t,e,s)=>{s.d(e,{m:()=>n});var r=s(5910),i=s(2020),n=new class extends r.Q{#t;#e;#s;constructor(){super(),this.#s=t=>{if(!i.S$&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:globalThis.document?.visibilityState!=="hidden"}}},1142:(t,e,s)=>{s.d(e,{useSuspenseQueries:()=>n});var r=s(1610),i=s(4791);function n(t,e){return(0,r.useQueries)({...t,queries:t.queries.map(t=>({...t,suspense:!0,throwOnError:i.R3,enabled:!0,placeholderData:void 0}))},e)}},1239:(t,e,s)=>{s.d(e,{t:()=>n});var r=s(5910),i=s(2020),n=new class extends r.Q{#r=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!i.S$&&window.addEventListener){let e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#r!==t&&(this.#r=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#r}}},1581:(t,e,s)=>{s.d(e,{IsRestoringProvider:()=>a,useIsRestoring:()=>n});var r=s(2115),i=r.createContext(!1),n=()=>r.useContext(i),a=i.Provider},1610:(t,e,s)=>{s.d(e,{useQueries:()=>y});var r=s(2115),i=s(7165),n=s(6347),a=s(5910),o=s(2020);function u(t,e){let s=new Set(e);return t.filter(t=>!s.has(t))}var h=class extends a.Q{#i;#n;#a;#o;#u;#h;#c;#l;#d=[];constructor(t,e,s){super(),this.#i=t,this.#o=s,this.#a=[],this.#u=[],this.#n=[],this.setQueries(e)}onSubscribe(){1===this.listeners.size&&this.#u.forEach(t=>{t.subscribe(e=>{this.#p(t,e)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#u.forEach(t=>{t.destroy()})}setQueries(t,e){this.#a=t,this.#o=e,i.jG.batch(()=>{let t=this.#u,e=this.#f(this.#a);this.#d=e,e.forEach(t=>t.observer.setOptions(t.defaultedQueryOptions));let s=e.map(t=>t.observer),r=s.map(t=>t.getCurrentResult()),i=s.some((e,s)=>e!==t[s]);(t.length!==s.length||i)&&(this.#u=s,this.#n=r,this.hasListeners()&&(u(t,s).forEach(t=>{t.destroy()}),u(s,t).forEach(t=>{t.subscribe(e=>{this.#p(t,e)})}),this.#y()))})}getCurrentResult(){return this.#n}getQueries(){return this.#u.map(t=>t.getCurrentQuery())}getObservers(){return this.#u}getOptimisticResult(t,e){let s=this.#f(t),r=s.map(t=>t.observer.getOptimisticResult(t.defaultedQueryOptions));return[r,t=>this.#v(t??r,e),()=>this.#b(r,s)]}#b(t,e){return e.map((s,r)=>{let i=t[r];return s.defaultedQueryOptions.notifyOnChangeProps?i:s.observer.trackResult(i,t=>{e.forEach(e=>{e.observer.trackProp(t)})})})}#v(t,e){return e?(this.#h&&this.#n===this.#l&&e===this.#c||(this.#c=e,this.#l=this.#n,this.#h=(0,o.BH)(this.#h,e(t))),this.#h):t}#f(t){let e=new Map(this.#u.map(t=>[t.options.queryHash,t])),s=[];return t.forEach(t=>{let r=this.#i.defaultQueryOptions(t),i=e.get(r.queryHash);i?s.push({defaultedQueryOptions:r,observer:i}):s.push({defaultedQueryOptions:r,observer:new n.$(this.#i,r)})}),s}#p(t,e){let s=this.#u.indexOf(t);-1!==s&&(this.#n=function(t,e,s){let r=t.slice(0);return r[e]=s,r}(this.#n,s,e),this.#y())}#y(){if(this.hasListeners()){let t=this.#h,e=this.#b(this.#n,this.#d);t!==this.#v(e,this.#o?.combine)&&i.jG.batch(()=>{this.listeners.forEach(t=>{t(this.#n)})})}}},c=s(6715),l=s(1581),d=s(382),p=s(2450),f=s(4791);function y(t,e){let{queries:s,...a}=t,u=(0,c.useQueryClient)(e),y=(0,l.useIsRestoring)(),v=(0,d.useQueryErrorResetBoundary)(),b=r.useMemo(()=>s.map(t=>{let e=u.defaultQueryOptions(t);return e._optimisticResults=y?"isRestoring":"optimistic",e}),[s,u,y]);b.forEach(t=>{(0,f.jv)(t),(0,p.LJ)(t,v)}),(0,p.wZ)(v);let[m]=r.useState(()=>new h(u,b,a)),[g,R,O]=m.getOptimisticResult(b,a.combine),S=!y&&!1!==a.subscribed;r.useSyncExternalStore(r.useCallback(t=>S?m.subscribe(i.jG.batchCalls(t)):o.lQ,[m,S]),()=>m.getCurrentResult(),()=>m.getCurrentResult()),r.useEffect(()=>{m.setQueries(b,a)},[b,a,m]);let C=g.some((t,e)=>(0,f.EU)(b[e],t))?g.flatMap((t,e)=>{let s=b[e];if(s){let e=new n.$(u,s);if((0,f.EU)(s,t))return(0,f.iL)(s,e,v);(0,f.nE)(t,y)&&(0,f.iL)(s,e,v)}return[]}):[];if(C.length>0)throw Promise.all(C);let w=g.find((t,e)=>{let s=b[e];return s&&(0,p.$1)({result:t,errorResetBoundary:v,throwOnError:s.throwOnError,query:u.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(null==w?void 0:w.error)throw w.error;return R(O())}},2020:(t,e,s)=>{s.d(e,{BH:()=>y,Cp:()=>f,EN:()=>p,Eh:()=>h,F$:()=>d,GU:()=>E,MK:()=>c,S$:()=>r,ZM:()=>Q,ZZ:()=>C,Zw:()=>n,d2:()=>u,f8:()=>v,gn:()=>a,hT:()=>w,j3:()=>o,lQ:()=>i,nJ:()=>l,pl:()=>O,y9:()=>S,yy:()=>R});var r="undefined"==typeof window||"Deno"in globalThis;function i(){}function n(t,e){return"function"==typeof t?t(e):t}function a(t){return"number"==typeof t&&t>=0&&t!==1/0}function o(t,e){return Math.max(t+(e||0)-Date.now(),0)}function u(t,e){return"function"==typeof t?t(e):t}function h(t,e){return"function"==typeof t?t(e):t}function c(t,e){let{type:s="all",exact:r,fetchStatus:i,predicate:n,queryKey:a,stale:o}=t;if(a){if(r){if(e.queryHash!==d(a,e.options))return!1}else if(!f(e.queryKey,a))return!1}if("all"!==s){let t=e.isActive();if("active"===s&&!t||"inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&(!i||i===e.state.fetchStatus)&&(!n||!!n(e))}function l(t,e){let{exact:s,status:r,predicate:i,mutationKey:n}=t;if(n){if(!e.options.mutationKey)return!1;if(s){if(p(e.options.mutationKey)!==p(n))return!1}else if(!f(e.options.mutationKey,n))return!1}return(!r||e.state.status===r)&&(!i||!!i(e))}function d(t,e){return(e?.queryKeyHashFn||p)(t)}function p(t){return JSON.stringify(t,(t,e)=>m(e)?Object.keys(e).sort().reduce((t,s)=>(t[s]=e[s],t),{}):e)}function f(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(s=>f(t[s],e[s]))}function y(t,e){if(t===e)return t;let s=b(t)&&b(e);if(s||m(t)&&m(e)){let r=s?t:Object.keys(t),i=r.length,n=s?e:Object.keys(e),a=n.length,o=s?[]:{},u=new Set(r),h=0;for(let r=0;r<a;r++){let i=s?r:n[r];(!s&&u.has(i)||s)&&void 0===t[i]&&void 0===e[i]?(o[i]=void 0,h++):(o[i]=y(t[i],e[i]),o[i]===t[i]&&void 0!==t[i]&&h++)}return i===a&&h===i?t:o}return e}function v(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let s in t)if(t[s]!==e[s])return!1;return!0}function b(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function m(t){if(!g(t))return!1;let e=t.constructor;if(void 0===e)return!0;let s=e.prototype;return!!g(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function g(t){return"[object Object]"===Object.prototype.toString.call(t)}function R(t){return new Promise(e=>{setTimeout(e,t)})}function O(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?y(t,e):e}function S(t,e,s=0){let r=[...t,e];return s&&r.length>s?r.slice(1):r}function C(t,e,s=0){let r=[e,...t];return s&&r.length>s?r.slice(0,-1):r}var w=Symbol();function Q(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==w?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}function E(t,e){return"function"==typeof t?t(...e):!!t}},2450:(t,e,s)=>{s.d(e,{$1:()=>o,LJ:()=>n,wZ:()=>a});var r=s(2115),i=s(2020),n=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&!e.isReset()&&(t.retryOnMount=!1)},a=t=>{r.useEffect(()=>{t.clearReset()},[t])},o=t=>{let{result:e,errorResetBoundary:s,throwOnError:r,query:n,suspense:a}=t;return e.isError&&!s.isReset()&&!e.isFetching&&n&&(a&&void 0===e.data||(0,i.GU)(r,[e.error,n]))}},3504:(t,e,s)=>{s.d(e,{T:()=>i,b:()=>n});var r=s(2020);function i(){let t,e,s=new Promise((s,r)=>{t=s,e=r});function r(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=e=>{r({status:"fulfilled",value:e}),t(e)},s.reject=t=>{r({status:"rejected",reason:t}),e(t)},s}function n(t){let e;if(t.then(t=>(e=t,t),r.lQ)?.catch(r.lQ),void 0!==e)return{data:e}}},3666:(t,e,s)=>{s.d(e,{useSuspenseQuery:()=>a});var r=s(6347),i=s(5),n=s(4791);function a(t,e){return(0,i.t)({...t,enabled:!0,suspense:!0,throwOnError:n.R3,placeholderData:void 0},r.$,e)}},4123:(t,e,s)=>{s.d(e,{z:()=>n});var r=s(6347),i=s(4275),n=class extends r.${constructor(t,e){super(t,e)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(t){super.setOptions({...t,behavior:(0,i.PL)()})}getOptimisticResult(t){return t.behavior=(0,i.PL)(),super.getOptimisticResult(t)}fetchNextPage(t){return this.fetch({...t,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(t){return this.fetch({...t,meta:{fetchMore:{direction:"backward"}}})}createResult(t,e){let{state:s}=t,r=super.createResult(t,e),{isFetching:n,isRefetching:a,isError:o,isRefetchError:u}=r,h=s.fetchMeta?.fetchMore?.direction,c=o&&"forward"===h,l=n&&"forward"===h,d=o&&"backward"===h,p=n&&"backward"===h;return{...r,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,i.rB)(e,s.data),hasPreviousPage:(0,i.RQ)(e,s.data),isFetchNextPageError:c,isFetchingNextPage:l,isFetchPreviousPageError:d,isFetchingPreviousPage:p,isRefetchError:u&&!c&&!d,isRefetching:a&&!l&&!p}}}},4275:(t,e,s)=>{s.d(e,{PL:()=>i,RQ:()=>u,rB:()=>o});var r=s(2020);function i(t){return{onFetch:(e,s)=>{let i=e.options,o=e.fetchOptions?.meta?.fetchMore?.direction,u=e.state.data?.pages||[],h=e.state.data?.pageParams||[],c={pages:[],pageParams:[]},l=0,d=async()=>{let s=!1,d=(0,r.ZM)(e.options,e.fetchOptions),p=async(t,i,n)=>{if(s)return Promise.reject();if(null==i&&t.pages.length)return Promise.resolve(t);let a=(()=>{let t={client:e.client,queryKey:e.queryKey,pageParam:i,direction:n?"backward":"forward",meta:e.options.meta};return Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)}),t})(),o=await d(a),{maxPages:u}=e.options,h=n?r.ZZ:r.y9;return{pages:h(t.pages,o,u),pageParams:h(t.pageParams,i,u)}};if(o&&u.length){let t="backward"===o,e={pages:u,pageParams:h},s=(t?a:n)(i,e);c=await p(e,s,t)}else{let e=t??u.length;do{let t=0===l?h[0]??i.initialPageParam:n(i,c);if(l>0&&null==t)break;c=await p(c,t),l++}while(l<e)}return c};e.options.persister?e.fetchFn=()=>e.options.persister?.(d,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=d}}}function n(t,{pages:e,pageParams:s}){let r=e.length-1;return e.length>0?t.getNextPageParam(e[r],e,s[r],s):void 0}function a(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}function o(t,e){return!!e&&null!=n(t,e)}function u(t,e){return!!e&&!!t.getPreviousPageParam&&null!=a(t,e)}},4560:(t,e,s)=>{s.d(e,{$:()=>o,s:()=>a});var r=s(7165),i=s(7948),n=s(6784),a=class extends i.k{#u;#m;#g;constructor(t){super(),this.mutationId=t.mutationId,this.#m=t.mutationCache,this.#u=[],this.state=t.state||o(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#u.includes(t)||(this.#u.push(t),this.clearGcTimeout(),this.#m.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#u=this.#u.filter(e=>e!==t),this.scheduleGc(),this.#m.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#u.length||("pending"===this.state.status?this.scheduleGc():this.#m.remove(this))}continue(){return this.#g?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#R({type:"continue"})};this.#g=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#R({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#R({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#m.canRun(this)});let s="pending"===this.state.status,r=!this.#g.canStart();try{if(s)e();else{this.#R({type:"pending",variables:t,isPaused:r}),await this.#m.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#R({type:"pending",context:e,variables:t,isPaused:r})}let i=await this.#g.start();return await this.#m.config.onSuccess?.(i,t,this.state.context,this),await this.options.onSuccess?.(i,t,this.state.context),await this.#m.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,t,this.state.context),this.#R({type:"success",data:i}),i}catch(e){try{throw await this.#m.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#m.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#R({type:"error",error:e})}}finally{this.#m.runNext(this)}}#R(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),r.jG.batch(()=>{this.#u.forEach(e=>{e.onMutationUpdate(t)}),this.#m.notify({mutation:this,type:"updated",action:t})})}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},4791:(t,e,s)=>{s.d(e,{EU:()=>a,R3:()=>r,iL:()=>o,jv:()=>i,nE:()=>n});var r=(t,e)=>void 0===e.state.data,i=t=>{if(t.suspense){let e=t=>"static"===t?t:Math.max(t??1e3,1e3),s=t.staleTime;t.staleTime="function"==typeof s?(...t)=>e(s(...t)):e(s),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}},n=(t,e)=>t.isLoading&&t.isFetching&&!e,a=(t,e)=>t?.suspense&&e.isPending,o=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()})},5041:(t,e,s)=>{s.d(e,{useMutation:()=>c});var r=s(2115),i=s(4560),n=s(7165),a=s(5910),o=s(2020),u=class extends a.Q{#i;#O=void 0;#S;#C;constructor(t,e){super(),this.#i=t,this.setOptions(e),this.bindMethods(),this.#w()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#i.defaultMutationOptions(t),(0,o.f8)(this.options,e)||this.#i.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#S,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,o.EN)(e.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#S?.state.status==="pending"&&this.#S.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#S?.removeObserver(this)}onMutationUpdate(t){this.#w(),this.#y(t)}getCurrentResult(){return this.#O}reset(){this.#S?.removeObserver(this),this.#S=void 0,this.#w(),this.#y()}mutate(t,e){return this.#C=e,this.#S?.removeObserver(this),this.#S=this.#i.getMutationCache().build(this.#i,this.options),this.#S.addObserver(this),this.#S.execute(t)}#w(){let t=this.#S?.state??(0,i.$)();this.#O={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#y(t){n.jG.batch(()=>{if(this.#C&&this.hasListeners()){let e=this.#O.variables,s=this.#O.context;t?.type==="success"?(this.#C.onSuccess?.(t.data,e,s),this.#C.onSettled?.(t.data,null,e,s)):t?.type==="error"&&(this.#C.onError?.(t.error,e,s),this.#C.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach(t=>{t(this.#O)})})}},h=s(6715);function c(t,e){let s=(0,h.useQueryClient)(e),[i]=r.useState(()=>new u(s,t));r.useEffect(()=>{i.setOptions(t)},[i,t]);let a=r.useSyncExternalStore(r.useCallback(t=>i.subscribe(n.jG.batchCalls(t)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),c=r.useCallback((t,e)=>{i.mutate(t,e).catch(o.lQ)},[i]);if(a.error&&(0,o.GU)(i.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:c,mutateAsync:a.mutate}}},5490:(t,e,s)=>{s.d(e,{useSuspenseInfiniteQuery:()=>a});var r=s(4123),i=s(5),n=s(4791);function a(t,e){return(0,i.t)({...t,enabled:!0,suspense:!0,throwOnError:n.R3},r.z,e)}},5838:(t,e,s)=>{s.d(e,{useQuery:()=>n});var r=s(6347),i=s(5);function n(t,e){return(0,i.t)(t,r.$,e)}},5910:(t,e,s)=>{s.d(e,{Q:()=>r});var r=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},6347:(t,e,s)=>{s.d(e,{$:()=>h});var r=s(920),i=s(7165),n=s(9853),a=s(5910),o=s(3504),u=s(2020),h=class extends a.Q{constructor(t,e){super(),this.options=e,this.#i=t,this.#Q=null,this.#E=(0,o.T)(),this.options.experimental_prefetchInRender||this.#E.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#i;#P=void 0;#I=void 0;#O=void 0;#M;#T;#E;#Q;#F;#x;#U;#q;#k;#j;#D=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#P.addObserver(this),c(this.#P,this.options)?this.#L():this.updateResult(),this.#A())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.#P,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.#P,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#G(),this.#$(),this.#P.removeObserver(this)}setOptions(t){let e=this.options,s=this.#P;if(this.options=this.#i.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,u.Eh)(this.options.enabled,this.#P))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#K(),this.#P.setOptions(this.options),e._defaulted&&!(0,u.f8)(this.options,e)&&this.#i.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#P,observer:this});let r=this.hasListeners();r&&d(this.#P,s,this.options,e)&&this.#L(),this.updateResult(),r&&(this.#P!==s||(0,u.Eh)(this.options.enabled,this.#P)!==(0,u.Eh)(e.enabled,this.#P)||(0,u.d2)(this.options.staleTime,this.#P)!==(0,u.d2)(e.staleTime,this.#P))&&this.#_();let i=this.#H();r&&(this.#P!==s||(0,u.Eh)(this.options.enabled,this.#P)!==(0,u.Eh)(e.enabled,this.#P)||i!==this.#j)&&this.#N(i)}getOptimisticResult(t){var e,s;let r=this.#i.getQueryCache().build(this.#i,t),i=this.createResult(r,t);return e=this,s=i,(0,u.f8)(e.getCurrentResult(),s)||(this.#O=i,this.#T=this.options,this.#M=this.#P.state),i}getCurrentResult(){return this.#O}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),e?.(s),Reflect.get(t,s))})}trackProp(t){this.#D.add(t)}getCurrentQuery(){return this.#P}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#i.defaultQueryOptions(t),s=this.#i.getQueryCache().build(this.#i,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#L({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#O))}#L(t){this.#K();let e=this.#P.fetch(this.options,t);return t?.throwOnError||(e=e.catch(u.lQ)),e}#_(){this.#G();let t=(0,u.d2)(this.options.staleTime,this.#P);if(u.S$||this.#O.isStale||!(0,u.gn)(t))return;let e=(0,u.j3)(this.#O.dataUpdatedAt,t);this.#q=setTimeout(()=>{this.#O.isStale||this.updateResult()},e+1)}#H(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#P):this.options.refetchInterval)??!1}#N(t){this.#$(),this.#j=t,!u.S$&&!1!==(0,u.Eh)(this.options.enabled,this.#P)&&(0,u.gn)(this.#j)&&0!==this.#j&&(this.#k=setInterval(()=>{(this.options.refetchIntervalInBackground||r.m.isFocused())&&this.#L()},this.#j))}#A(){this.#_(),this.#N(this.#H())}#G(){this.#q&&(clearTimeout(this.#q),this.#q=void 0)}#$(){this.#k&&(clearInterval(this.#k),this.#k=void 0)}createResult(t,e){let s,r=this.#P,i=this.options,a=this.#O,h=this.#M,l=this.#T,f=t!==r?t.state:this.#I,{state:y}=t,v={...y},b=!1;if(e._optimisticResults){let s=this.hasListeners(),a=!s&&c(t,e),o=s&&d(t,r,e,i);(a||o)&&(v={...v,...(0,n.k)(y.data,t.options)}),"isRestoring"===e._optimisticResults&&(v.fetchStatus="idle")}let{error:m,errorUpdatedAt:g,status:R}=v;s=v.data;let O=!1;if(void 0!==e.placeholderData&&void 0===s&&"pending"===R){let t;a?.isPlaceholderData&&e.placeholderData===l?.placeholderData?(t=a.data,O=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#U?.state.data,this.#U):e.placeholderData,void 0!==t&&(R="success",s=(0,u.pl)(a?.data,t,e),b=!0)}if(e.select&&void 0!==s&&!O)if(a&&s===h?.data&&e.select===this.#F)s=this.#x;else try{this.#F=e.select,s=e.select(s),s=(0,u.pl)(a?.data,s,e),this.#x=s,this.#Q=null}catch(t){this.#Q=t}this.#Q&&(m=this.#Q,s=this.#x,g=Date.now(),R="error");let S="fetching"===v.fetchStatus,C="pending"===R,w="error"===R,Q=C&&S,E=void 0!==s,P={status:R,fetchStatus:v.fetchStatus,isPending:C,isSuccess:"success"===R,isError:w,isInitialLoading:Q,isLoading:Q,data:s,dataUpdatedAt:v.dataUpdatedAt,error:m,errorUpdatedAt:g,failureCount:v.fetchFailureCount,failureReason:v.fetchFailureReason,errorUpdateCount:v.errorUpdateCount,isFetched:v.dataUpdateCount>0||v.errorUpdateCount>0,isFetchedAfterMount:v.dataUpdateCount>f.dataUpdateCount||v.errorUpdateCount>f.errorUpdateCount,isFetching:S,isRefetching:S&&!C,isLoadingError:w&&!E,isPaused:"paused"===v.fetchStatus,isPlaceholderData:b,isRefetchError:w&&E,isStale:p(t,e),refetch:this.refetch,promise:this.#E,isEnabled:!1!==(0,u.Eh)(e.enabled,t)};if(this.options.experimental_prefetchInRender){let e=t=>{"error"===P.status?t.reject(P.error):void 0!==P.data&&t.resolve(P.data)},s=()=>{e(this.#E=P.promise=(0,o.T)())},i=this.#E;switch(i.status){case"pending":t.queryHash===r.queryHash&&e(i);break;case"fulfilled":("error"===P.status||P.data!==i.value)&&s();break;case"rejected":("error"!==P.status||P.error!==i.reason)&&s()}}return P}updateResult(){let t=this.#O,e=this.createResult(this.#P,this.options);this.#M=this.#P.state,this.#T=this.options,void 0!==this.#M.data&&(this.#U=this.#P),(0,u.f8)(e,t)||(this.#O=e,this.#y({listeners:(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!this.#D.size)return!0;let r=new Set(s??this.#D);return this.options.throwOnError&&r.add("error"),Object.keys(this.#O).some(e=>this.#O[e]!==t[e]&&r.has(e))})()}))}#K(){let t=this.#i.getQueryCache().build(this.#i,this.options);if(t===this.#P)return;let e=this.#P;this.#P=t,this.#I=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#A()}#y(t){i.jG.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#O)}),this.#i.getQueryCache().notify({query:this.#P,type:"observerResultsUpdated"})})}};function c(t,e){return!1!==(0,u.Eh)(e.enabled,t)&&void 0===t.state.data&&("error"!==t.state.status||!1!==e.retryOnMount)||void 0!==t.state.data&&l(t,e,e.refetchOnMount)}function l(t,e,s){if(!1!==(0,u.Eh)(e.enabled,t)&&"static"!==(0,u.d2)(e.staleTime,t)){let r="function"==typeof s?s(t):s;return"always"===r||!1!==r&&p(t,e)}return!1}function d(t,e,s,r){return(t!==e||!1===(0,u.Eh)(r.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&p(t,s)}function p(t,e){return!1!==(0,u.Eh)(e.enabled,t)&&t.isStaleByTime((0,u.d2)(e.staleTime,t))}},6715:(t,e,s)=>{s.r(e),s.d(e,{QueryClientContext:()=>n,QueryClientProvider:()=>o,useQueryClient:()=>a});var r=s(2115),i=s(5155),n=r.createContext(void 0),a=t=>{let e=r.useContext(n);if(t)return t;if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},o=t=>{let{client:e,children:s}=t;return r.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,i.jsx)(n.Provider,{value:e,children:s})}},6784:(t,e,s)=>{s.d(e,{II:()=>l,v_:()=>u,wm:()=>c});var r=s(920),i=s(1239),n=s(3504),a=s(2020);function o(t){return Math.min(1e3*2**t,3e4)}function u(t){return(t??"online")!=="online"||i.t.isOnline()}var h=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function c(t){return t instanceof h}function l(t){let e,s=!1,c=0,l=!1,d=(0,n.T)(),p=()=>r.m.isFocused()&&("always"===t.networkMode||i.t.isOnline())&&t.canRun(),f=()=>u(t.networkMode)&&t.canRun(),y=s=>{l||(l=!0,t.onSuccess?.(s),e?.(),d.resolve(s))},v=s=>{l||(l=!0,t.onError?.(s),e?.(),d.reject(s))},b=()=>new Promise(s=>{e=t=>{(l||p())&&s(t)},t.onPause?.()}).then(()=>{e=void 0,l||t.onContinue?.()}),m=()=>{let e;if(l)return;let r=0===c?t.initialPromise:void 0;try{e=r??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(y).catch(e=>{if(l)return;let r=t.retry??3*!a.S$,i=t.retryDelay??o,n="function"==typeof i?i(c,e):i,u=!0===r||"number"==typeof r&&c<r||"function"==typeof r&&r(c,e);if(s||!u)return void v(e);c++,t.onFail?.(c,e),(0,a.yy)(n).then(()=>p()?void 0:b()).then(()=>{s?v(e):m()})})};return{promise:d,cancel:e=>{l||(v(new h(e)),t.abort?.())},continue:()=>(e?.(),d),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:f,start:()=>(f()?m():b().then(m),d)}}},7165:(t,e,s)=>{s.d(e,{jG:()=>i});var r=t=>setTimeout(t,0),i=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},n=r,a=r=>{e?t.push(r):n(()=>{s(r)})};return{batch:r=>{let a;e++;try{a=r()}finally{--e||(()=>{let e=t;t=[],e.length&&n(()=>{i(()=>{e.forEach(t=>{s(t)})})})})()}return a},batchCalls:t=>(...e)=>{a(()=>{t(...e)})},schedule:a,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{n=t}}}()},7948:(t,e,s)=>{s.d(e,{k:()=>i});var r=s(2020),i=class{#B;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,r.gn)(this.gcTime)&&(this.#B=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(r.S$?1/0:3e5))}clearGcTimeout(){this.#B&&(clearTimeout(this.#B),this.#B=void 0)}}},8822:(t,e,s)=>{s.d(e,{useInfiniteQuery:()=>n});var r=s(4123),i=s(5);function n(t,e){return(0,i.t)(t,r.z,e)}},8902:(t,e,s)=>{s.d(e,{HydrationBoundary:()=>u});var r=s(2115),i=s(3504);function n(t){return t}function a(t,e,s){if("object"!=typeof e||null===e)return;let r=t.getMutationCache(),a=t.getQueryCache(),o=s?.defaultOptions?.deserializeData??t.getDefaultOptions().hydrate?.deserializeData??n,u=e.mutations||[],h=e.queries||[];u.forEach(({state:e,...i})=>{r.build(t,{...t.getDefaultOptions().hydrate?.mutations,...s?.defaultOptions?.mutations,...i},e)}),h.forEach(({queryKey:e,state:r,queryHash:n,meta:u,promise:h,dehydratedAt:c})=>{let l=h?(0,i.b)(h):void 0,d=void 0===r.data?l?.data:r.data,p=void 0===d?d:o(d),f=a.get(n),y=f?.state.status==="pending",v=f?.state.fetchStatus==="fetching";if(f){let t=l&&void 0!==c&&c>f.state.dataUpdatedAt;if(r.dataUpdatedAt>f.state.dataUpdatedAt||t){let{fetchStatus:t,...e}=r;f.setState({...e,data:p})}}else f=a.build(t,{...t.getDefaultOptions().hydrate?.queries,...s?.defaultOptions?.queries,queryKey:e,queryHash:n,meta:u},{...r,data:p,fetchStatus:"idle",status:void 0!==p?"success":r.status});h&&!y&&!v&&(void 0===c||c>f.state.dataUpdatedAt)&&f.fetch(void 0,{initialPromise:Promise.resolve(h).then(o)})})}var o=s(6715),u=t=>{let{children:e,options:s={},state:i,queryClient:n}=t,u=(0,o.useQueryClient)(n),h=r.useRef(s);h.current=s;let c=r.useMemo(()=>{if(i){if("object"!=typeof i)return;let t=u.getQueryCache(),e=i.queries||[],s=[],r=[];for(let i of e){let e=t.get(i.queryHash);e?(i.state.dataUpdatedAt>e.state.dataUpdatedAt||i.promise&&"pending"!==e.state.status&&"fetching"!==e.state.fetchStatus&&void 0!==i.dehydratedAt&&i.dehydratedAt>e.state.dataUpdatedAt)&&r.push(i):s.push(i)}if(s.length>0&&a(u,{queries:s},h.current),r.length>0)return r}},[u,i]);return r.useEffect(()=>{c&&a(u,{queries:c},h.current)},[u,c]),e}},9853:(t,e,s)=>{s.d(e,{X:()=>o,k:()=>u});var r=s(2020),i=s(7165),n=s(6784),a=s(7948),o=class extends a.k{#Z;#z;#W;#i;#g;#J;#X;constructor(t){super(),this.#X=!1,this.#J=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#i=t.client,this.#W=this.#i.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#Z=function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,r=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#Z,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#g?.promise}setOptions(t){this.options={...this.#J,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#W.remove(this)}setData(t,e){let s=(0,r.pl)(this.state.data,t,this.options);return this.#R({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#R({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#g?.promise;return this.#g?.cancel(t),e?e.then(r.lQ).catch(r.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#Z)}isActive(){return this.observers.some(t=>!1!==(0,r.Eh)(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===r.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===(0,r.d2)(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!(0,r.j3)(this.state.dataUpdatedAt,t))}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#g?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#g?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#W.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#g&&(this.#X?this.#g.cancel({revert:!0}):this.#g.cancelRetry()),this.scheduleGc()),this.#W.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#R({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#g)return this.#g.continueRetry(),this.#g.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let s=new AbortController,i=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#X=!0,s.signal)})},a=()=>{let t=(0,r.ZM)(this.options,e),s=(()=>{let t={client:this.#i,queryKey:this.queryKey,meta:this.meta};return i(t),t})();return(this.#X=!1,this.options.persister)?this.options.persister(t,s,this):t(s)},o=(()=>{let t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#i,state:this.state,fetchFn:a};return i(t),t})();this.options.behavior?.onFetch(o,this),this.#z=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==o.fetchOptions?.meta)&&this.#R({type:"fetch",meta:o.fetchOptions?.meta});let u=t=>{(0,n.wm)(t)&&t.silent||this.#R({type:"error",error:t}),(0,n.wm)(t)||(this.#W.config.onError?.(t,this),this.#W.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#g=(0,n.II)({initialPromise:e?.initialPromise,fn:o.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0===t)return void u(Error(`${this.queryHash} data is undefined`));try{this.setData(t)}catch(t){u(t);return}this.#W.config.onSuccess?.(t,this),this.#W.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError:u,onFail:(t,e)=>{this.#R({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#R({type:"pause"})},onContinue:()=>{this.#R({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0}),this.#g.start()}#R(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...u(e.data,this.options),fetchMeta:t.meta??null};case"success":return this.#z=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let s=t.error;if((0,n.wm)(s)&&s.revert&&this.#z)return{...this.#z,fetchStatus:"idle"};return{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),i.jG.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#W.notify({query:this,type:"updated",action:t})})}};function u(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,n.v_)(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}}}]);