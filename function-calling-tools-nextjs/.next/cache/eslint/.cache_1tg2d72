[{"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/api/trpc/[trpc]/route.ts": "1", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/layout.tsx": "2", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx": "3", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/components/ChatInterface.tsx": "4", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/root.ts": "5", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/chat.ts": "6", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/tools.ts": "7", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/trpc.ts": "8", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/services/openai.ts": "9", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/index.ts": "10", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/summary.ts": "11", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/translation.ts": "12", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/weather.ts": "13", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/types/index.ts": "14", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/utils/trpc.ts": "15"}, {"size": 324, "mtime": 1752572269335, "results": "16", "hashOfConfig": "17"}, {"size": 789, "mtime": 1752572437182, "results": "18", "hashOfConfig": "17"}, {"size": 139, "mtime": 1752573665835, "results": "19", "hashOfConfig": "17"}, {"size": 5362, "mtime": 1752573310962, "results": "20", "hashOfConfig": "17"}, {"size": 269, "mtime": 1752572076361, "results": "21", "hashOfConfig": "17"}, {"size": 6833, "mtime": 1752572021423, "results": "22", "hashOfConfig": "17"}, {"size": 1363, "mtime": 1752570527182, "results": "23", "hashOfConfig": "17"}, {"size": 409, "mtime": 1752570212345, "results": "24", "hashOfConfig": "17"}, {"size": 4021, "mtime": 1752571972679, "results": "25", "hashOfConfig": "17"}, {"size": 835, "mtime": 1752570506276, "results": "26", "hashOfConfig": "17"}, {"size": 3415, "mtime": 1752570474015, "results": "27", "hashOfConfig": "17"}, {"size": 4848, "mtime": 1752573579881, "results": "28", "hashOfConfig": "17"}, {"size": 3505, "mtime": 1752570390542, "results": "29", "hashOfConfig": "17"}, {"size": 2145, "mtime": 1752571942005, "results": "30", "hashOfConfig": "17"}, {"size": 337, "mtime": 1752572347174, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cjadss", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/api/trpc/[trpc]/route.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/components/ChatInterface.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/root.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/chat.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/tools.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/trpc.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/services/openai.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/index.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/summary.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/translation.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/weather.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/types/index.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/utils/trpc.ts", [], []]