[{"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/api/trpc/[trpc]/route.ts": "1", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/layout.tsx": "2", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx": "3", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/components/ChatInterface.tsx": "4", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/root.ts": "5", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/chat.ts": "6", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/tools.ts": "7", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/trpc.ts": "8", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/services/openai.ts": "9", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/index.ts": "10", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/summary.ts": "11", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/translation.ts": "12", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/weather.ts": "13", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/types/index.ts": "14", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/utils/trpc.ts": "15", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/components/TRPCProvider.tsx": "16"}, {"size": 324, "mtime": 1752572269335, "results": "17", "hashOfConfig": "18"}, {"size": 831, "mtime": 1752575981149, "results": "19", "hashOfConfig": "18"}, {"size": 140, "mtime": 1752575715482, "results": "20", "hashOfConfig": "18"}, {"size": 5363, "mtime": 1752575992144, "results": "21", "hashOfConfig": "18"}, {"size": 269, "mtime": 1752572076361, "results": "22", "hashOfConfig": "18"}, {"size": 6833, "mtime": 1752572021423, "results": "23", "hashOfConfig": "18"}, {"size": 1363, "mtime": 1752570527182, "results": "24", "hashOfConfig": "18"}, {"size": 409, "mtime": 1752570212345, "results": "25", "hashOfConfig": "18"}, {"size": 4021, "mtime": 1752571972679, "results": "26", "hashOfConfig": "18"}, {"size": 835, "mtime": 1752570506276, "results": "27", "hashOfConfig": "18"}, {"size": 3415, "mtime": 1752570474015, "results": "28", "hashOfConfig": "18"}, {"size": 4848, "mtime": 1752573579881, "results": "29", "hashOfConfig": "18"}, {"size": 3505, "mtime": 1752570390542, "results": "30", "hashOfConfig": "18"}, {"size": 2145, "mtime": 1752571942005, "results": "31", "hashOfConfig": "18"}, {"size": 337, "mtime": 1752572347174, "results": "32", "hashOfConfig": "18"}, {"size": 827, "mtime": 1752575937644, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cjadss", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/api/trpc/[trpc]/route.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/components/ChatInterface.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/root.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/chat.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/tools.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/trpc.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/services/openai.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/index.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/summary.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/translation.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/weather.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/types/index.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/utils/trpc.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/components/TRPCProvider.tsx", [], []]