[{"/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/api/trpc/[trpc]/route.ts": "1", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/layout.tsx": "2", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx": "3", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/components/ChatInterface.tsx": "4", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/root.ts": "5", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/chat.ts": "6", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/tools.ts": "7", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/trpc.ts": "8", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/services/openai.ts": "9", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/index.ts": "10", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/summary.ts": "11", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/translation.ts": "12", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/weather.ts": "13", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/types/index.ts": "14", "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/utils/trpc.ts": "15"}, {"size": 324, "mtime": 1752572269335, "results": "16", "hashOfConfig": "17"}, {"size": 789, "mtime": 1752572437182, "results": "18", "hashOfConfig": "17"}, {"size": 125, "mtime": 1752572583051, "results": "19", "hashOfConfig": "17"}, {"size": 5362, "mtime": 1752572523195, "results": "20", "hashOfConfig": "17"}, {"size": 269, "mtime": 1752572076361, "results": "21", "hashOfConfig": "17"}, {"size": 6833, "mtime": 1752572021423, "results": "22", "hashOfConfig": "17"}, {"size": 1363, "mtime": 1752570527182, "results": "23", "hashOfConfig": "17"}, {"size": 409, "mtime": 1752570212345, "results": "24", "hashOfConfig": "17"}, {"size": 4021, "mtime": 1752571972679, "results": "25", "hashOfConfig": "17"}, {"size": 835, "mtime": 1752570506276, "results": "26", "hashOfConfig": "17"}, {"size": 3415, "mtime": 1752570474015, "results": "27", "hashOfConfig": "17"}, {"size": 4832, "mtime": 1752570425001, "results": "28", "hashOfConfig": "17"}, {"size": 3505, "mtime": 1752570390542, "results": "29", "hashOfConfig": "17"}, {"size": 2145, "mtime": 1752571942005, "results": "30", "hashOfConfig": "17"}, {"size": 337, "mtime": 1752572347174, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1135cwe", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/api/trpc/[trpc]/route.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/app/page.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/components/ChatInterface.tsx", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/root.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/chat.ts", ["77", "78", "79"], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/routers/tools.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/api/trpc.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/services/openai.ts", ["80", "81", "82"], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/index.ts", [], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/summary.ts", ["83", "84"], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/translation.ts", ["85"], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/server/tools/weather.ts", ["86", "87"], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/types/index.ts", ["88"], [], "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/src/utils/trpc.ts", [], [], {"ruleId": "89", "severity": 2, "message": "90", "line": 29, "column": 15, "nodeType": "91", "messageId": "92", "endLine": 29, "endColumn": 18, "suggestions": "93"}, {"ruleId": "94", "severity": 1, "message": "95", "line": 32, "column": 14, "nodeType": null, "messageId": "96", "endLine": 32, "endColumn": 19}, {"ruleId": "89", "severity": 2, "message": "90", "line": 50, "column": 19, "nodeType": "91", "messageId": "92", "endLine": 50, "endColumn": 22, "suggestions": "97"}, {"ruleId": "89", "severity": 2, "message": "90", "line": 131, "column": 21, "nodeType": "91", "messageId": "92", "endLine": 131, "endColumn": 24, "suggestions": "98"}, {"ruleId": "89", "severity": 2, "message": "90", "line": 137, "column": 61, "nodeType": "91", "messageId": "92", "endLine": 137, "endColumn": 64, "suggestions": "99"}, {"ruleId": "89", "severity": 2, "message": "90", "line": 139, "column": 28, "nodeType": "91", "messageId": "92", "endLine": 139, "endColumn": 31, "suggestions": "100"}, {"ruleId": "89", "severity": 2, "message": "90", "line": 49, "column": 19, "nodeType": "91", "messageId": "92", "endLine": 49, "endColumn": 22, "suggestions": "101"}, {"ruleId": "94", "severity": 1, "message": "102", "line": 97, "column": 51, "nodeType": null, "messageId": "96", "endLine": 97, "endColumn": 60}, {"ruleId": "89", "severity": 2, "message": "90", "line": 128, "column": 19, "nodeType": "91", "messageId": "92", "endLine": 128, "endColumn": 22, "suggestions": "103"}, {"ruleId": "94", "severity": 1, "message": "104", "line": 85, "column": 19, "nodeType": null, "messageId": "96", "endLine": 85, "endColumn": 26}, {"ruleId": "89", "severity": 2, "message": "90", "line": 120, "column": 19, "nodeType": "91", "messageId": "92", "endLine": 120, "endColumn": 22, "suggestions": "105"}, {"ruleId": "89", "severity": 2, "message": "90", "line": 106, "column": 13, "nodeType": "91", "messageId": "92", "endLine": 106, "endColumn": 16, "suggestions": "106"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["107", "108"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["109", "110"], ["111", "112"], ["113", "114"], ["115", "116"], ["117", "118"], "'maxLength' is defined but never used.", ["119", "120"], "'country' is assigned a value but never used.", ["121", "122"], ["123", "124"], {"messageId": "125", "fix": "126", "desc": "127"}, {"messageId": "128", "fix": "129", "desc": "130"}, {"messageId": "125", "fix": "131", "desc": "127"}, {"messageId": "128", "fix": "132", "desc": "130"}, {"messageId": "125", "fix": "133", "desc": "127"}, {"messageId": "128", "fix": "134", "desc": "130"}, {"messageId": "125", "fix": "135", "desc": "127"}, {"messageId": "128", "fix": "136", "desc": "130"}, {"messageId": "125", "fix": "137", "desc": "127"}, {"messageId": "128", "fix": "138", "desc": "130"}, {"messageId": "125", "fix": "139", "desc": "127"}, {"messageId": "128", "fix": "140", "desc": "130"}, {"messageId": "125", "fix": "141", "desc": "127"}, {"messageId": "128", "fix": "142", "desc": "130"}, {"messageId": "125", "fix": "143", "desc": "127"}, {"messageId": "128", "fix": "144", "desc": "130"}, {"messageId": "125", "fix": "145", "desc": "127"}, {"messageId": "128", "fix": "146", "desc": "130"}, "suggestUnknown", {"range": "147", "text": "148"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "149", "text": "150"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "151", "text": "148"}, {"range": "152", "text": "150"}, {"range": "153", "text": "148"}, {"range": "154", "text": "150"}, {"range": "155", "text": "148"}, {"range": "156", "text": "150"}, {"range": "157", "text": "148"}, {"range": "158", "text": "150"}, {"range": "159", "text": "148"}, {"range": "160", "text": "150"}, {"range": "161", "text": "148"}, {"range": "162", "text": "150"}, {"range": "163", "text": "148"}, {"range": "164", "text": "150"}, {"range": "165", "text": "148"}, {"range": "166", "text": "150"}, [888, 891], "unknown", [888, 891], "never", [1408, 1411], [1408, 1411], [3242, 3245], [3242, 3245], [3446, 3449], [3446, 3449], [3514, 3517], [3514, 3517], [1204, 1207], [1204, 1207], [3267, 3270], [3267, 3270], [2616, 2619], [2616, 2619], [1923, 1926], [1923, 1926]]