[{"name": "generate-buildid", "duration": 103, "timestamp": 2771783293586, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752575280098, "traceId": "0b6300b52cee7394"}, {"name": "load-custom-routes", "duration": 161, "timestamp": 2771783293738, "id": 5, "parentId": 1, "tags": {}, "startTime": 1752575280098, "traceId": "0b6300b52cee7394"}, {"name": "create-dist-dir", "duration": 1612, "timestamp": 2771783328429, "id": 6, "parentId": 1, "tags": {}, "startTime": 1752575280132, "traceId": "0b6300b52cee7394"}, {"name": "create-pages-mapping", "duration": 114, "timestamp": 2771783338644, "id": 7, "parentId": 1, "tags": {}, "startTime": 1752575280143, "traceId": "0b6300b52cee7394"}, {"name": "collect-app-paths", "duration": 1226, "timestamp": 2771783338785, "id": 8, "parentId": 1, "tags": {}, "startTime": 1752575280143, "traceId": "0b6300b52cee7394"}, {"name": "create-app-mapping", "duration": 1265, "timestamp": 2771783340022, "id": 9, "parentId": 1, "tags": {}, "startTime": 1752575280144, "traceId": "0b6300b52cee7394"}, {"name": "public-dir-conflict-check", "duration": 152, "timestamp": 2771783341477, "id": 10, "parentId": 1, "tags": {}, "startTime": 1752575280146, "traceId": "0b6300b52cee7394"}, {"name": "generate-routes-manifest", "duration": 1209, "timestamp": 2771783341720, "id": 11, "parentId": 1, "tags": {}, "startTime": 1752575280146, "traceId": "0b6300b52cee7394"}, {"name": "create-entrypoints", "duration": 11052, "timestamp": 2771783902990, "id": 15, "parentId": 13, "tags": {}, "startTime": 1752575280707, "traceId": "0b6300b52cee7394"}, {"name": "generate-webpack-config", "duration": 72353, "timestamp": 2771783914160, "id": 16, "parentId": 14, "tags": {}, "startTime": 1752575280718, "traceId": "0b6300b52cee7394"}, {"name": "next-trace-entrypoint-plugin", "duration": 1465, "timestamp": 2771784058355, "id": 18, "parentId": 17, "tags": {}, "startTime": 1752575280862, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 195909, "timestamp": 2771784063318, "id": 23, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1752575280867, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 233192, "timestamp": 2771784063342, "id": 26, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1752575280867, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 272542, "timestamp": 2771784063329, "id": 24, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1752575280867, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 306391, "timestamp": 2771784062888, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752575280867, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 404769, "timestamp": 2771784063182, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-not-found.js&appDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp&appPaths=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-not-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752575280867, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 404643, "timestamp": 2771784063336, "id": 25, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752575280867, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 430981, "timestamp": 2771784063204, "id": 22, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp&appPaths=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752575280867, "traceId": "0b6300b52cee7394"}, {"name": "make", "duration": 549124, "timestamp": 2771784062733, "id": 19, "parentId": 17, "tags": {}, "startTime": 1752575280867, "traceId": "0b6300b52cee7394"}, {"name": "get-entries", "duration": 330, "timestamp": 2771784612683, "id": 42, "parentId": 41, "tags": {}, "startTime": 1752575281417, "traceId": "0b6300b52cee7394"}, {"name": "node-file-trace-plugin", "duration": 80727, "timestamp": 2771784615712, "id": 43, "parentId": 41, "tags": {"traceEntryCount": "10"}, "startTime": 1752575281420, "traceId": "0b6300b52cee7394"}, {"name": "collect-traced-files", "duration": 632, "timestamp": 2771784696448, "id": 44, "parentId": 41, "tags": {}, "startTime": 1752575281500, "traceId": "0b6300b52cee7394"}, {"name": "finish-modules", "duration": 84553, "timestamp": 2771784612530, "id": 41, "parentId": 18, "tags": {}, "startTime": 1752575281417, "traceId": "0b6300b52cee7394"}, {"name": "chunk-graph", "duration": 8303, "timestamp": 2771784724170, "id": 46, "parentId": 45, "tags": {}, "startTime": 1752575281528, "traceId": "0b6300b52cee7394"}, {"name": "optimize-modules", "duration": 13, "timestamp": 2771784732547, "id": 48, "parentId": 45, "tags": {}, "startTime": 1752575281537, "traceId": "0b6300b52cee7394"}, {"name": "optimize-chunks", "duration": 6977, "timestamp": 2771784732695, "id": 49, "parentId": 45, "tags": {}, "startTime": 1752575281537, "traceId": "0b6300b52cee7394"}, {"name": "optimize-tree", "duration": 71, "timestamp": 2771784739733, "id": 50, "parentId": 45, "tags": {}, "startTime": 1752575281544, "traceId": "0b6300b52cee7394"}, {"name": "optimize-chunk-modules", "duration": 16784, "timestamp": 2771784739850, "id": 51, "parentId": 45, "tags": {}, "startTime": 1752575281544, "traceId": "0b6300b52cee7394"}, {"name": "optimize", "duration": 24178, "timestamp": 2771784732516, "id": 47, "parentId": 45, "tags": {}, "startTime": 1752575281537, "traceId": "0b6300b52cee7394"}, {"name": "module-hash", "duration": 15060, "timestamp": 2771784770532, "id": 52, "parentId": 45, "tags": {}, "startTime": 1752575281575, "traceId": "0b6300b52cee7394"}, {"name": "code-generation", "duration": 4082, "timestamp": 2771784785651, "id": 53, "parentId": 45, "tags": {}, "startTime": 1752575281590, "traceId": "0b6300b52cee7394"}, {"name": "hash", "duration": 4951, "timestamp": 2771784792607, "id": 54, "parentId": 45, "tags": {}, "startTime": 1752575281597, "traceId": "0b6300b52cee7394"}, {"name": "code-generation-jobs", "duration": 167, "timestamp": 2771784797557, "id": 55, "parentId": 45, "tags": {}, "startTime": 1752575281602, "traceId": "0b6300b52cee7394"}, {"name": "module-assets", "duration": 265, "timestamp": 2771784797704, "id": 56, "parentId": 45, "tags": {}, "startTime": 1752575281602, "traceId": "0b6300b52cee7394"}, {"name": "create-chunk-assets", "duration": 3133, "timestamp": 2771784797977, "id": 57, "parentId": 45, "tags": {}, "startTime": 1752575281602, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3598, "timestamp": 2771784811763, "id": 60, "parentId": 58, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3565, "timestamp": 2771784811811, "id": 61, "parentId": 58, "tags": {"name": "../app/api/trpc/[trpc]/route.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3560, "timestamp": 2771784811817, "id": 62, "parentId": 58, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3555, "timestamp": 2771784811822, "id": 63, "parentId": 58, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3552, "timestamp": 2771784811827, "id": 64, "parentId": 58, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3549, "timestamp": 2771784811830, "id": 65, "parentId": 58, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3546, "timestamp": 2771784811834, "id": 66, "parentId": 58, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3544, "timestamp": 2771784811837, "id": 67, "parentId": 58, "tags": {"name": "431.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 3541, "timestamp": 2771784811840, "id": 68, "parentId": 58, "tags": {"name": "886.js", "cache": "HIT"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 54, "timestamp": 2771784815327, "id": 70, "parentId": 58, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1752575281619, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 48110, "timestamp": 2771784808388, "id": 59, "parentId": 58, "tags": {"name": "../app/favicon.ico/route.js", "cache": "MISS"}, "startTime": 1752575281612, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 142562, "timestamp": 2771784811845, "id": 69, "parentId": 58, "tags": {"name": "298.js", "cache": "MISS"}, "startTime": 1752575281616, "traceId": "0b6300b52cee7394"}, {"name": "minify-webpack-plugin-optimize", "duration": 151665, "timestamp": 2771784802757, "id": 58, "parentId": 17, "tags": {"compilationName": "server", "mangle": "[object Object]"}, "startTime": 1752575281607, "traceId": "0b6300b52cee7394"}, {"name": "css-minimizer-plugin", "duration": 157, "timestamp": 2771784954547, "id": 71, "parentId": 17, "tags": {}, "startTime": 1752575281759, "traceId": "0b6300b52cee7394"}, {"name": "create-trace-assets", "duration": 1475, "timestamp": 2771784954824, "id": 72, "parentId": 18, "tags": {}, "startTime": 1752575281759, "traceId": "0b6300b52cee7394"}, {"name": "seal", "duration": 250339, "timestamp": 2771784711017, "id": 45, "parentId": 17, "tags": {}, "startTime": 1752575281515, "traceId": "0b6300b52cee7394"}, {"name": "webpack-compilation", "duration": 913185, "timestamp": 2771784057153, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752575280861, "traceId": "0b6300b52cee7394"}, {"name": "emit", "duration": 5958, "timestamp": 2771784970690, "id": 73, "parentId": 14, "tags": {}, "startTime": 1752575281775, "traceId": "0b6300b52cee7394"}, {"name": "webpack-close", "duration": 179020, "timestamp": 2771784977748, "id": 74, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752575281782, "traceId": "0b6300b52cee7394"}, {"name": "webpack-generate-error-stats", "duration": 1350, "timestamp": 2771785156813, "id": 75, "parentId": 74, "tags": {}, "startTime": 1752575281961, "traceId": "0b6300b52cee7394"}, {"name": "run-webpack-compiler", "duration": 1255385, "timestamp": 2771783902987, "id": 14, "parentId": 13, "tags": {}, "startTime": 1752575280707, "traceId": "0b6300b52cee7394"}, {"name": "format-webpack-messages", "duration": 45, "timestamp": 2771785158376, "id": 76, "parentId": 13, "tags": {}, "startTime": 1752575281962, "traceId": "0b6300b52cee7394"}, {"name": "worker-main-server", "duration": 1255752, "timestamp": 2771783902748, "id": 13, "parentId": 1, "tags": {}, "startTime": 1752575280707, "traceId": "0b6300b52cee7394"}, {"name": "create-entrypoints", "duration": 8319, "timestamp": 2771785587463, "id": 80, "parentId": 78, "tags": {}, "startTime": 1752575282391, "traceId": "0b6300b52cee7394"}, {"name": "generate-webpack-config", "duration": 55766, "timestamp": 2771785595911, "id": 81, "parentId": 79, "tags": {}, "startTime": 1752575282400, "traceId": "0b6300b52cee7394"}, {"name": "make", "duration": 434, "timestamp": 2771785707014, "id": 83, "parentId": 82, "tags": {}, "startTime": 1752575282511, "traceId": "0b6300b52cee7394"}, {"name": "chunk-graph", "duration": 382, "timestamp": 2771785709052, "id": 85, "parentId": 84, "tags": {}, "startTime": 1752575282513, "traceId": "0b6300b52cee7394"}, {"name": "optimize-modules", "duration": 16, "timestamp": 2771785709592, "id": 87, "parentId": 84, "tags": {}, "startTime": 1752575282514, "traceId": "0b6300b52cee7394"}, {"name": "optimize-chunks", "duration": 511, "timestamp": 2771785709657, "id": 88, "parentId": 84, "tags": {}, "startTime": 1752575282514, "traceId": "0b6300b52cee7394"}, {"name": "optimize-tree", "duration": 63, "timestamp": 2771785710206, "id": 89, "parentId": 84, "tags": {}, "startTime": 1752575282514, "traceId": "0b6300b52cee7394"}, {"name": "optimize-chunk-modules", "duration": 274, "timestamp": 2771785710369, "id": 90, "parentId": 84, "tags": {}, "startTime": 1752575282514, "traceId": "0b6300b52cee7394"}, {"name": "optimize", "duration": 1333, "timestamp": 2771785709468, "id": 86, "parentId": 84, "tags": {}, "startTime": 1752575282513, "traceId": "0b6300b52cee7394"}, {"name": "module-hash", "duration": 122, "timestamp": 2771785711290, "id": 91, "parentId": 84, "tags": {}, "startTime": 1752575282515, "traceId": "0b6300b52cee7394"}, {"name": "code-generation", "duration": 117, "timestamp": 2771785711433, "id": 92, "parentId": 84, "tags": {}, "startTime": 1752575282515, "traceId": "0b6300b52cee7394"}, {"name": "hash", "duration": 208, "timestamp": 2771785711688, "id": 93, "parentId": 84, "tags": {}, "startTime": 1752575282516, "traceId": "0b6300b52cee7394"}, {"name": "code-generation-jobs", "duration": 67, "timestamp": 2771785711895, "id": 94, "parentId": 84, "tags": {}, "startTime": 1752575282516, "traceId": "0b6300b52cee7394"}, {"name": "module-assets", "duration": 38, "timestamp": 2771785711950, "id": 95, "parentId": 84, "tags": {}, "startTime": 1752575282516, "traceId": "0b6300b52cee7394"}, {"name": "create-chunk-assets", "duration": 105, "timestamp": 2771785711993, "id": 96, "parentId": 84, "tags": {}, "startTime": 1752575282516, "traceId": "0b6300b52cee7394"}, {"name": "minify-js", "duration": 197, "timestamp": 2771785717536, "id": 98, "parentId": 97, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1752575282522, "traceId": "0b6300b52cee7394"}, {"name": "minify-webpack-plugin-optimize", "duration": 3618, "timestamp": 2771785714121, "id": 97, "parentId": 82, "tags": {"compilationName": "edge-server", "mangle": "[object Object]"}, "startTime": 1752575282518, "traceId": "0b6300b52cee7394"}, {"name": "css-minimizer-plugin", "duration": 77, "timestamp": 2771785717788, "id": 99, "parentId": 82, "tags": {}, "startTime": 1752575282522, "traceId": "0b6300b52cee7394"}, {"name": "seal", "duration": 10987, "timestamp": 2771785708730, "id": 84, "parentId": 82, "tags": {}, "startTime": 1752575282513, "traceId": "0b6300b52cee7394"}, {"name": "webpack-compilation", "duration": 17065, "timestamp": 2771785702833, "id": 82, "parentId": 79, "tags": {"name": "edge-server"}, "startTime": 1752575282507, "traceId": "0b6300b52cee7394"}, {"name": "emit", "duration": 1570, "timestamp": 2771785720184, "id": 100, "parentId": 79, "tags": {}, "startTime": 1752575282524, "traceId": "0b6300b52cee7394"}, {"name": "webpack-close", "duration": 353, "timestamp": 2771785722182, "id": 101, "parentId": 79, "tags": {"name": "edge-server"}, "startTime": 1752575282526, "traceId": "0b6300b52cee7394"}, {"name": "webpack-generate-error-stats", "duration": 1252, "timestamp": 2771785722560, "id": 102, "parentId": 101, "tags": {}, "startTime": 1752575282527, "traceId": "0b6300b52cee7394"}, {"name": "run-webpack-compiler", "duration": 136385, "timestamp": 2771785587460, "id": 79, "parentId": 78, "tags": {}, "startTime": 1752575282391, "traceId": "0b6300b52cee7394"}, {"name": "format-webpack-messages", "duration": 41, "timestamp": 2771785723848, "id": 103, "parentId": 78, "tags": {}, "startTime": 1752575282528, "traceId": "0b6300b52cee7394"}, {"name": "worker-main-edge-server", "duration": 136755, "timestamp": 2771785587198, "id": 78, "parentId": 1, "tags": {}, "startTime": 1752575282391, "traceId": "0b6300b52cee7394"}, {"name": "create-entrypoints", "duration": 8318, "timestamp": 2771786131846, "id": 106, "parentId": 104, "tags": {}, "startTime": 1752575282936, "traceId": "0b6300b52cee7394"}, {"name": "generate-webpack-config", "duration": 63056, "timestamp": 2771786140271, "id": 107, "parentId": 105, "tags": {}, "startTime": 1752575282944, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 89596, "timestamp": 2771786267694, "id": 118, "parentId": 109, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 167686, "timestamp": 2771786267654, "id": 113, "parentId": 109, "tags": {"request": "next-client-pages-loader?absolutePagePath=%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-not-found.js&page=%2F_not-found%2Fpage!"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 198601, "timestamp": 2771786267640, "id": 112, "parentId": 109, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 205749, "timestamp": 2771786267665, "id": 114, "parentId": 109, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 205833, "timestamp": 2771786267618, "id": 111, "parentId": 109, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 205775, "timestamp": 2771786267681, "id": 116, "parentId": 109, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 205786, "timestamp": 2771786267674, "id": 115, "parentId": 109, "tags": {"request": "/Users/<USER>/Documents/work_space/other_code/function-calling-tools/function-calling-tools-nextjs/node_modules/next/dist/client/router.js"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 210368, "timestamp": 2771786267688, "id": 117, "parentId": 109, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FHydrationBoundary.js%22%2C%22ids%22%3A%5B%22HydrationBoundary%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FIsRestoringProvider.js%22%2C%22ids%22%3A%5B%22IsRestoringProvider%22%2C%22useIsRestoring%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FQueryClientProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22useQueryClient%22%2C%22QueryClientContext%22%2C%22QueryClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FQueryErrorResetBoundary.js%22%2C%22ids%22%3A%5B%22QueryErrorResetBoundary%22%2C%22useQueryErrorResetBoundary%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseInfiniteQuery.js%22%2C%22ids%22%3A%5B%22useInfiniteQuery%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseIsFetching.js%22%2C%22ids%22%3A%5B%22useIsFetching%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseMutation.js%22%2C%22ids%22%3A%5B%22useMutation%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseMutationState.js%22%2C%22ids%22%3A%5B%22useIsMutating%22%2C%22useMutationState%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseQueries.js%22%2C%22ids%22%3A%5B%22useQueries%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseQuery.js%22%2C%22ids%22%3A%5B%22useQuery%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseSuspenseInfiniteQuery.js%22%2C%22ids%22%3A%5B%22useSuspenseInfiniteQuery%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseSuspenseQueries.js%22%2C%22ids%22%3A%5B%22useSuspenseQueries%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2F%40tanstack%2Freact-query%2Fbuild%2Fmodern%2FuseSuspenseQuery.js%22%2C%22ids%22%3A%5B%22useSuspenseQuery%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 210783, "timestamp": 2771786267323, "id": 110, "parentId": 109, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1752575283071, "traceId": "0b6300b52cee7394"}, {"name": "add-entry", "duration": 231519, "timestamp": 2771786267805, "id": 119, "parentId": 109, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fchaoxin%2FDocuments%2Fwork_space%2Fother_code%2Ffunction-calling-tools%2Ffunction-calling-tools-nextjs%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752575283072, "traceId": "0b6300b52cee7394"}, {"name": "make", "duration": 232493, "timestamp": 2771786267072, "id": 109, "parentId": 108, "tags": {}, "startTime": 1752575283071, "traceId": "0b6300b52cee7394"}, {"name": "chunk-graph", "duration": 4780, "timestamp": 2771786514922, "id": 121, "parentId": 120, "tags": {}, "startTime": 1752575283319, "traceId": "0b6300b52cee7394"}, {"name": "optimize-modules", "duration": 13, "timestamp": 2771786519774, "id": 123, "parentId": 120, "tags": {}, "startTime": 1752575283324, "traceId": "0b6300b52cee7394"}, {"name": "optimize-chunks", "duration": 5867, "timestamp": 2771786520700, "id": 125, "parentId": 120, "tags": {}, "startTime": 1752575283325, "traceId": "0b6300b52cee7394"}, {"name": "optimize-tree", "duration": 81, "timestamp": 2771786526657, "id": 126, "parentId": 120, "tags": {}, "startTime": 1752575283331, "traceId": "0b6300b52cee7394"}]