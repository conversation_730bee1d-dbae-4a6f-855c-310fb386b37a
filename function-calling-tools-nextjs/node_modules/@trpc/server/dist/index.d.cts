import "./index.d-BiUz7kM_.cjs";
import { AnyClientTypes, AnyMiddlewareFunction, AnyMutationProcedure, AnyProcedure, AnyQueryProcedure, AnyRootTypes, AnyRouter, AnySubscriptionProcedure, BuiltRouter, CombinedDataTransformer, CreateContextCallback, CreateRouterOptions, DecorateCreateRouterOptions, DeepPartial, DefaultErrorData, DefaultErrorShape, Dict, ErrorFormatter, MergeRouters, MiddlewareBuilder, MiddlewareFunction, MutationProcedure, ProcedureBuilder, ProcedureType, QueryProcedure, RootConfig, RouterBuilder, RouterCaller, RouterCallerFactory, RouterDef, RouterRecord, RuntimeConfigOptions, StandardSchemaV1Error, SubscriptionProcedure, TRPCBuilder, TRPCError, TRPCErrorShape, TRPCRootObject, TRPC_ERROR_CODE_KEY, TRPC_ERROR_CODE_NUMBER, TrackedEnvelope, UnsetMarker, callProcedure, createFlatProxy, createRecursiveProxy, experimental_standaloneMiddleware, getErrorShape, getTRPCErrorFromUnknown, inferClientTypes, inferProcedureBuilderResolverOptions, inferProcedureInput, inferProcedureOutput, inferRouterContext, inferRouterError, inferRouterInputs, inferRouterOutputs, inferTransformedProcedureOutput, inferTransformedSubscriptionOutput, initTRPC, isTrackedEnvelope, lazy, sse, tracked, transformTRPCResponse } from "./unstable-core-do-not-import.d-C6mFWtNG.cjs";
import { inferAsyncReturnType } from "./index.d-CvZXeEyR.cjs";
export { AnyMiddlewareFunction, AnyMutationProcedure, AnyProcedure, AnyQueryProcedure, AnyRouter, AnySubscriptionProcedure, AnyClientTypes as AnyTRPCClientTypes, AnyMiddlewareFunction as AnyTRPCMiddlewareFunction, AnyMutationProcedure as AnyTRPCMutationProcedure, AnyProcedure as AnyTRPCProcedure, AnyQueryProcedure as AnyTRPCQueryProcedure, AnyRootTypes as AnyTRPCRootTypes, AnyRouter as AnyTRPCRouter, AnySubscriptionProcedure as AnyTRPCSubscriptionProcedure, CombinedDataTransformer, CreateContextCallback, DeepPartial, Dict, ProcedureType, StandardSchemaV1Error, TRPCBuilder, BuiltRouter as TRPCBuiltRouter, CombinedDataTransformer as TRPCCombinedDataTransformer, CreateRouterOptions as TRPCCreateRouterOptions, DecorateCreateRouterOptions as TRPCDecorateCreateRouterOptions, DefaultErrorData as TRPCDefaultErrorData, DefaultErrorShape as TRPCDefaultErrorShape, TRPCError, ErrorFormatter as TRPCErrorFormatter, TRPCErrorShape, MergeRouters as TRPCMergeRouters, MiddlewareBuilder as TRPCMiddlewareBuilder, MiddlewareFunction as TRPCMiddlewareFunction, MutationProcedure as TRPCMutationProcedure, ProcedureBuilder as TRPCProcedureBuilder, ProcedureType as TRPCProcedureType, QueryProcedure as TRPCQueryProcedure, RootConfig as TRPCRootConfig, TRPCRootObject, RouterBuilder as TRPCRouterBuilder, RouterCaller as TRPCRouterCaller, RouterCallerFactory as TRPCRouterCallerFactory, RouterDef as TRPCRouterDef, RouterRecord as TRPCRouterRecord, RuntimeConfigOptions as TRPCRuntimeConfigOptions, SubscriptionProcedure as TRPCSubscriptionProcedure, UnsetMarker as TRPCUnsetMarker, TRPC_ERROR_CODE_KEY, TRPC_ERROR_CODE_NUMBER, TrackedEnvelope, callProcedure as callTRPCProcedure, createFlatProxy as createTRPCFlatProxy, createRecursiveProxy as createTRPCRecursiveProxy, lazy as experimental_lazy, experimental_standaloneMiddleware, experimental_standaloneMiddleware as experimental_trpcMiddleware, getErrorShape, getTRPCErrorFromUnknown, getErrorShape as getTRPCErrorShape, inferAsyncReturnType, inferProcedureBuilderResolverOptions, inferProcedureInput, inferProcedureOutput, inferRouterContext, inferRouterError, inferRouterInputs, inferRouterOutputs, inferClientTypes as inferTRPCClientTypes, inferTransformedProcedureOutput, inferTransformedSubscriptionOutput, initTRPC, isTrackedEnvelope, lazy, sse, tracked, transformTRPCResponse };