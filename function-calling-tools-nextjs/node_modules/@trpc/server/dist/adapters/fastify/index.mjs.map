{"version": 3, "file": "index.mjs", "names": ["opts: FastifyRequestHandlerOptions<TRouter, TRequest, TResponse>", "createContext: ResolveHTTPRequestOptionsContextFn<TRouter>", "incomingMessage: NodeHTTPRequest", "fastify: FastifyInstance", "opts: FastifyTRPCPluginOptions<TRouter>", "done: (err?: Error) => void"], "sources": ["../../../src/adapters/fastify/fastifyRequestHandler.ts", "../../../src/adapters/fastify/fastifyTRPCPlugin.ts"], "sourcesContent": ["/**\n * If you're making an adapter for tRPC and looking at this file for reference, you should import types and functions from `@trpc/server` and `@trpc/server/http`\n *\n * @example\n * ```ts\n * import type { AnyTRPCRouter } from '@trpc/server'\n * import type { HTTPBaseHandlerOptions } from '@trpc/server/http'\n * ```\n */\nimport type { FastifyReply, FastifyRequest } from 'fastify';\n// @trpc/server\nimport type { AnyRouter } from '../../@trpc/server';\n// @trpc/server/http\nimport {\n  resolveResponse,\n  type HTTPBaseHandlerOptions,\n  type ResolveHTTPRequestOptionsContextFn,\n} from '../../@trpc/server/http';\n// @trpc/server/node-http\nimport type { NodeHTTPRequest } from '../node-http';\nimport {\n  incomingMessageToRequest,\n  type NodeHTTPCreateContextOption,\n} from '../node-http';\n\nexport type FastifyHandlerOptions<\n  TRouter extends AnyRouter,\n  TRequest extends FastifyRequest,\n  TResponse extends FastifyReply,\n> = HTTPBaseHandlerOptions<TRouter, TRequest> &\n  NodeHTTPCreateContextOption<TRouter, TRequest, TResponse>;\n\ntype FastifyRequestHandlerOptions<\n  TRouter extends AnyRouter,\n  TRequest extends FastifyRequest,\n  TResponse extends FastifyReply,\n> = FastifyHandlerOptions<TRouter, TRequest, TResponse> & {\n  req: TRequest;\n  res: TResponse;\n  path: string;\n};\n\nexport async function fastifyRequestHandler<\n  TRouter extends AnyRouter,\n  TRequest extends FastifyRequest,\n  TResponse extends FastifyReply,\n>(opts: FastifyRequestHandlerOptions<TRouter, TRequest, TResponse>) {\n  const createContext: ResolveHTTPRequestOptionsContextFn<TRouter> = async (\n    innerOpts,\n  ) => {\n    return await opts.createContext?.({\n      ...opts,\n      ...innerOpts,\n    });\n  };\n\n  const incomingMessage: NodeHTTPRequest = opts.req.raw;\n\n  // monkey-path body to the IncomingMessage\n  if ('body' in opts.req) {\n    incomingMessage.body = opts.req.body;\n  }\n  const req = incomingMessageToRequest(incomingMessage, opts.res.raw, {\n    maxBodySize: null,\n  });\n\n  const res = await resolveResponse({\n    ...opts,\n    req,\n    error: null,\n    createContext,\n    onError(o) {\n      opts?.onError?.({\n        ...o,\n        req: opts.req,\n      });\n    },\n  });\n\n  await opts.res.send(res);\n}\n", "/**\n * If you're making an adapter for tRPC and looking at this file for reference, you should import types and functions from `@trpc/server` and `@trpc/server/http`\n *\n * @example\n * ```ts\n * import type { AnyTRPCRouter } from '@trpc/server'\n * import type { HTTPBaseHandlerOptions } from '@trpc/server/http'\n * ```\n */\n/// <reference types=\"@fastify/websocket\" />\nimport type { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';\n// @trpc/server\nimport type { AnyRouter } from '../../@trpc/server';\n// @trpc/server/http\nimport type { NodeHTTPCreateContextFnOptions } from '../node-http';\n// @trpc/server/ws\nimport {\n  getWSConnectionHandler,\n  handleKeepAlive,\n  type WSSHandlerOptions,\n} from '../ws';\nimport type { FastifyHandlerOptions } from './fastifyRequestHandler';\nimport { fastifyRequestHandler } from './fastifyRequestHandler';\n\nexport interface FastifyTRPCPluginOptions<TRouter extends AnyRouter> {\n  prefix?: string;\n  useWSS?: boolean;\n  trpcOptions: FastifyHandlerOptions<TRouter, FastifyRequest, FastifyReply>;\n}\n\nexport type CreateFastifyContextOptions = NodeHTTPCreateContextFnOptions<\n  FastifyRequest,\n  FastifyReply\n>;\n\nexport function fastifyTRPCPlugin<TRouter extends AnyRouter>(\n  fastify: FastifyInstance,\n  opts: FastifyTRPCPluginOptions<TRouter>,\n  done: (err?: Error) => void,\n) {\n  fastify.removeContentTypeParser('application/json');\n  fastify.addContentTypeParser(\n    'application/json',\n    { parseAs: 'string' },\n    function (_, body, _done) {\n      _done(null, body);\n    },\n  );\n\n  let prefix = opts.prefix ?? '';\n\n  // https://github.com/fastify/fastify-plugin/blob/fe079bef6557a83794bf437e14b9b9edb8a74104/plugin.js#L11\n  // @ts-expect-error property 'default' does not exists on type ...\n  if (typeof fastifyTRPCPlugin.default !== 'function') {\n    prefix = ''; // handled by fastify internally\n  }\n\n  fastify.all(`${prefix}/:path`, async (req, res) => {\n    const path = (req.params as any).path;\n    await fastifyRequestHandler({ ...opts.trpcOptions, req, res, path });\n  });\n\n  if (opts.useWSS) {\n    const trpcOptions =\n      opts.trpcOptions as unknown as WSSHandlerOptions<TRouter>;\n\n    const onConnection = getWSConnectionHandler<TRouter>({\n      ...trpcOptions,\n    });\n\n    fastify.get(prefix ?? '/', { websocket: true }, (socket, req) => {\n      onConnection(socket, req.raw);\n      if (trpcOptions?.keepAlive?.enabled) {\n        const { pingMs, pongWaitMs } = trpcOptions.keepAlive;\n        handleKeepAlive(socket, pingMs, pongWaitMs);\n      }\n    });\n  }\n\n  done();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AA0CA,eAAsB,sBAIpBA,MAAkE;CAClE,MAAMC,gBAA6D,OACjE,cACG;;AACH,SAAO,8BAAM,KAAK,qEAAL,2GACR,OACA,WACH;CACH;CAED,MAAMC,kBAAmC,KAAK,IAAI;AAGlD,KAAI,UAAU,KAAK,IACjB,iBAAgB,OAAO,KAAK,IAAI;CAElC,MAAM,MAAM,yBAAyB,iBAAiB,KAAK,IAAI,KAAK,EAClE,aAAa,KACd,EAAC;CAEF,MAAM,MAAM,MAAM,4FACb;EACH;EACA,OAAO;EACP;EACA,QAAQ,GAAG;;AACT,6DAAM,iDAAN,qGACK,UACH,KAAK,KAAK,OACV;EACH;IACD;AAEF,OAAM,KAAK,IAAI,KAAK,IAAI;AACzB;;;;;AC7CD,SAAgB,kBACdC,SACAC,MACAC,MACA;;AACA,SAAQ,wBAAwB,mBAAmB;AACnD,SAAQ,qBACN,oBACA,EAAE,SAAS,SAAU,GACrB,SAAU,GAAG,MAAM,OAAO;AACxB,QAAM,MAAM,KAAK;CAClB,EACF;CAED,IAAI,yBAAS,KAAK,6DAAU;AAI5B,YAAW,kBAAkB,YAAY,WACvC,UAAS;AAGX,SAAQ,KAAK,EAAE,OAAO,SAAS,OAAO,KAAK,QAAQ;EACjD,MAAM,OAAQ,IAAI,OAAe;AACjC,QAAM,8FAA2B,KAAK;GAAa;GAAK;GAAK;KAAO;CACrE,EAAC;AAEF,KAAI,KAAK,QAAQ;;EACf,MAAM,cACJ,KAAK;EAEP,MAAM,eAAe,6DAChB,aACH;AAEF,UAAQ,eAAI,mDAAU,KAAK,EAAE,WAAW,KAAM,GAAE,CAAC,QAAQ,QAAQ;;AAC/D,gBAAa,QAAQ,IAAI,IAAI;AAC7B,kFAAI,YAAa,yFAAW,SAAS;IACnC,MAAM,EAAE,QAAQ,YAAY,GAAG,YAAY;AAC3C,oBAAgB,QAAQ,QAAQ,WAAW;GAC5C;EACF,EAAC;CACH;AAED,OAAM;AACP"}