{"version": 3, "file": "index.mjs", "names": ["path: string", "opts: FetchHandlerRequestOptions<TRouter>", "createContext: ResolveHTTPRequestOptionsContextFn<TRouter>"], "sources": ["../../../src/adapters/fetch/fetchRequestHandler.ts"], "sourcesContent": ["/**\n * If you're making an adapter for tRPC and looking at this file for reference, you should import types and functions from `@trpc/server` and `@trpc/server/http`\n *\n * @example\n * ```ts\n * import type { AnyTRPCRouter } from '@trpc/server'\n * import type { HTTPBaseHandlerOptions } from '@trpc/server/http'\n * ```\n */\n// @trpc/server\n\nimport type { AnyRouter } from '../../@trpc/server';\nimport type { ResolveHTTPRequestOptionsContextFn } from '../../@trpc/server/http';\nimport { resolveResponse } from '../../@trpc/server/http';\nimport type { FetchHandlerRequestOptions } from './types';\n\nconst trimSlashes = (path: string): string => {\n  path = path.startsWith('/') ? path.slice(1) : path;\n  path = path.endsWith('/') ? path.slice(0, -1) : path;\n\n  return path;\n};\n\nexport async function fetchRequestHandler<TRouter extends AnyRouter>(\n  opts: FetchHandlerRequestOptions<TRouter>,\n): Promise<Response> {\n  const resHeaders = new Headers();\n\n  const createContext: ResolveHTTPRequestOptionsContextFn<TRouter> = async (\n    innerOpts,\n  ) => {\n    return opts.createContext?.({ req: opts.req, resHeaders, ...innerOpts });\n  };\n\n  const url = new URL(opts.req.url);\n\n  const pathname = trimSlashes(url.pathname);\n  const endpoint = trimSlashes(opts.endpoint);\n  const path = trimSlashes(pathname.slice(endpoint.length));\n\n  return await resolveResponse({\n    ...opts,\n    req: opts.req,\n    createContext,\n    path,\n    error: null,\n    onError(o) {\n      opts?.onError?.({ ...o, req: opts.req });\n    },\n    responseMeta(data) {\n      const meta = opts.responseMeta?.(data);\n\n      if (meta?.headers) {\n        if (meta.headers instanceof Headers) {\n          for (const [key, value] of meta.headers.entries()) {\n            resHeaders.append(key, value);\n          }\n        } else {\n          /**\n           * @deprecated, delete in v12\n           */\n          for (const [key, value] of Object.entries(meta.headers)) {\n            if (Array.isArray(value)) {\n              for (const v of value) {\n                resHeaders.append(key, v);\n              }\n            } else if (typeof value === 'string') {\n              resHeaders.set(key, value);\n            }\n          }\n        }\n      }\n\n      return {\n        headers: resHeaders,\n        status: meta?.status,\n      };\n    },\n  });\n}\n"], "mappings": ";;;;;;;;AAgBA,MAAM,cAAc,CAACA,SAAyB;AAC5C,QAAO,KAAK,WAAW,IAAI,GAAG,KAAK,MAAM,EAAE,GAAG;AAC9C,QAAO,KAAK,SAAS,IAAI,GAAG,KAAK,MAAM,GAAG,GAAG,GAAG;AAEhD,QAAO;AACR;AAED,eAAsB,oBACpBC,MACmB;CACnB,MAAM,aAAa,IAAI;CAEvB,MAAMC,gBAA6D,OACjE,cACG;;AACH,gCAAO,KAAK,qEAAL;GAAuB,KAAK,KAAK;GAAK;KAAe,WAAY;CACzE;CAED,MAAM,MAAM,IAAI,IAAI,KAAK,IAAI;CAE7B,MAAM,WAAW,YAAY,IAAI,SAAS;CAC1C,MAAM,WAAW,YAAY,KAAK,SAAS;CAC3C,MAAM,OAAO,YAAY,SAAS,MAAM,SAAS,OAAO,CAAC;AAEzD,QAAO,MAAM,wFACR;EACH,KAAK,KAAK;EACV;EACA;EACA,OAAO;EACP,QAAQ,GAAG;;AACT,6DAAM,iDAAN,iGAAqB,UAAG,KAAK,KAAK,OAAM;EACzC;EACD,aAAa,MAAM;;GACjB,MAAM,6BAAO,KAAK,mEAAL,8BAAoB,KAAK;AAEtC,mDAAI,KAAM,SACR;QAAI,KAAK,mBAAmB,QAC1B,MAAK,MAAM,CAAC,KAAK,MAAM,IAAI,KAAK,QAAQ,SAAS,CAC/C,YAAW,OAAO,KAAK,MAAM;;;;;AAM/B,SAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,CACrD,KAAI,MAAM,QAAQ,MAAM,CACtB,MAAK,MAAM,KAAK,MACd,YAAW,OAAO,KAAK,EAAE;oBAEX,UAAU,SAC1B,YAAW,IAAI,KAAK,MAAM;GAG/B;AAGH,UAAO;IACL,SAAS;IACT,oDAAQ,KAAM;GACf;EACF;IACD;AACH"}