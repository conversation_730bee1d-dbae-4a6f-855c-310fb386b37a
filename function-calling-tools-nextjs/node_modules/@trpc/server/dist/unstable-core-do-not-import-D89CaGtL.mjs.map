{"version": 3, "file": "unstable-core-do-not-import-D89CaGtL.mjs", "names": ["str: string", "obj: Record<string, any>", "path: readonly string[]", "value: unknown", "formData: FormData", "obj: Record<string, unknown>"], "sources": ["../src/unstable-core-do-not-import/http/formDataToObject.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-non-null-assertion */\n\nconst isNumberString = (str: string) => /^\\d+$/.test(str);\n\nfunction set(\n  obj: Record<string, any>,\n  path: readonly string[],\n  value: unknown,\n): void {\n  if (path.length > 1) {\n    const newPath = [...path];\n    const key = newPath.shift()!;\n    const nextKey = newPath[0]!;\n\n    if (!obj[key]) {\n      obj[key] = isNumberString(nextKey) ? [] : {};\n    } else if (Array.isArray(obj[key]) && !isNumberString(nextKey)) {\n      obj[key] = Object.fromEntries(Object.entries(obj[key]));\n    }\n\n    set(obj[key], newPath, value);\n\n    return;\n  }\n  const p = path[0]!;\n  if (obj[p] === undefined) {\n    obj[p] = value;\n  } else if (Array.isArray(obj[p])) {\n    obj[p].push(value);\n  } else {\n    obj[p] = [obj[p], value];\n  }\n}\n\nexport function formDataToObject(formData: FormData) {\n  const obj: Record<string, unknown> = {};\n\n  for (const [key, value] of formData.entries()) {\n    const parts = key.split(/[\\.\\[\\]]/).filter(Boolean);\n    set(obj, parts, value);\n  }\n\n  return obj;\n}\n"], "mappings": ";AAEA,MAAM,iBAAiB,CAACA,QAAgB,QAAQ,KAAK,IAAI;AAEzD,SAAS,IACPC,KACAC,MACAC,OACM;AACN,KAAI,KAAK,SAAS,GAAG;EACnB,MAAM,UAAU,CAAC,GAAG,IAAK;EACzB,MAAM,MAAM,QAAQ,OAAO;EAC3B,MAAM,UAAU,QAAQ;AAExB,OAAK,IAAI,KACP,KAAI,OAAO,eAAe,QAAQ,GAAG,CAAE,IAAG,CAAE;WACnC,MAAM,QAAQ,IAAI,KAAK,KAAK,eAAe,QAAQ,CAC5D,KAAI,OAAO,OAAO,YAAY,OAAO,QAAQ,IAAI,KAAK,CAAC;AAGzD,MAAI,IAAI,MAAM,SAAS,MAAM;AAE7B;CACD;CACD,MAAM,IAAI,KAAK;AACf,KAAI,IAAI,cACN,KAAI,KAAK;UACA,MAAM,QAAQ,IAAI,GAAG,CAC9B,KAAI,GAAG,KAAK,MAAM;KAElB,KAAI,KAAK,CAAC,IAAI,IAAI,KAAM;AAE3B;AAED,SAAgB,iBAAiBC,UAAoB;CACnD,MAAMC,MAA+B,CAAE;AAEvC,MAAK,MAAM,CAAC,KAAK,MAAM,IAAI,SAAS,SAAS,EAAE;EAC7C,MAAM,QAAQ,IAAI,MAAM,WAAW,CAAC,OAAO,QAAQ;AACnD,MAAI,KAAK,OAAO,MAAM;CACvB;AAED,QAAO;AACR"}