{"version": 3, "file": "utils-DdbbrDku.mjs", "names": ["TRPC_ERROR_CODES_BY_NUMBER: InvertKeyValue<\n  typeof TRPC_ERROR_CODES_BY_KEY\n>", "retryableRpcCodes: TRPC_ERROR_CODE_NUMBER[]", "obj1: TType", "newObj: TType", "value: unknown", "fn: unknown", "obj: TObj", "fn: () => TValue", "it: T", "condition: boolean", "signals: AbortSignal[]"], "sources": ["../src/unstable-core-do-not-import/rpc/codes.ts", "../src/unstable-core-do-not-import/utils.ts"], "sourcesContent": ["import type { InvertKeyValue, ValueOf } from '../types';\n\n// reference: https://www.jsonrpc.org/specification\n\n/**\n * JSON-RPC 2.0 Error codes\n *\n * `-32000` to `-32099` are reserved for implementation-defined server-errors.\n * For tRPC we're copying the last digits of HTTP 4XX errors.\n */\nexport const TRPC_ERROR_CODES_BY_KEY = {\n  /**\n   * Invalid JSON was received by the server.\n   * An error occurred on the server while parsing the JSON text.\n   */\n  PARSE_ERROR: -32700,\n  /**\n   * The JSON sent is not a valid Request object.\n   */\n  BAD_REQUEST: -32600, // 400\n\n  // Internal JSON-RPC error\n  INTERNAL_SERVER_ERROR: -32603, // 500\n  NOT_IMPLEMENTED: -32603, // 501\n  BAD_GATEWAY: -32603, // 502\n  SERVICE_UNAVAILABLE: -32603, // 503\n  GATEWAY_TIMEOUT: -32603, // 504\n\n  // Implementation specific errors\n  UNAUTHORIZED: -32001, // 401\n  PAYMENT_REQUIRED: -32002, // 402\n  FORBIDDEN: -32003, // 403\n  NOT_FOUND: -32004, // 404\n  METHOD_NOT_SUPPORTED: -32005, // 405\n  TIMEOUT: -32008, // 408\n  CONFLICT: -32009, // 409\n  PRECONDITION_FAILED: -32012, // 412\n  PAYLOAD_TOO_LARGE: -32013, // 413\n  UNSUPPORTED_MEDIA_TYPE: -32015, // 415\n  UNPROCESSABLE_CONTENT: -32022, // 422\n  TOO_MANY_REQUESTS: -32029, // 429\n  CLIENT_CLOSED_REQUEST: -32099, // 499\n} as const;\n\n// pure\nexport const TRPC_ERROR_CODES_BY_NUMBER: InvertKeyValue<\n  typeof TRPC_ERROR_CODES_BY_KEY\n> = {\n  [-32700]: 'PARSE_ERROR',\n  [-32600]: 'BAD_REQUEST',\n  [-32603]: 'INTERNAL_SERVER_ERROR',\n  [-32001]: 'UNAUTHORIZED',\n  [-32002]: 'PAYMENT_REQUIRED',\n  [-32003]: 'FORBIDDEN',\n  [-32004]: 'NOT_FOUND',\n  [-32005]: 'METHOD_NOT_SUPPORTED',\n  [-32008]: 'TIMEOUT',\n  [-32009]: 'CONFLICT',\n  [-32012]: 'PRECONDITION_FAILED',\n  [-32013]: 'PAYLOAD_TOO_LARGE',\n  [-32015]: 'UNSUPPORTED_MEDIA_TYPE',\n  [-32022]: 'UNPROCESSABLE_CONTENT',\n  [-32029]: 'TOO_MANY_REQUESTS',\n  [-32099]: 'CLIENT_CLOSED_REQUEST',\n};\n\nexport type TRPC_ERROR_CODE_NUMBER = ValueOf<typeof TRPC_ERROR_CODES_BY_KEY>;\nexport type TRPC_ERROR_CODE_KEY = keyof typeof TRPC_ERROR_CODES_BY_KEY;\n\n/**\n * tRPC error codes that are considered retryable\n * With out of the box SSE, the client will reconnect when these errors are encountered\n */\nexport const retryableRpcCodes: TRPC_ERROR_CODE_NUMBER[] = [\n  TRPC_ERROR_CODES_BY_KEY.BAD_GATEWAY,\n  TRPC_ERROR_CODES_BY_KEY.SERVICE_UNAVAILABLE,\n  TRPC_ERROR_CODES_BY_KEY.GATEWAY_TIMEOUT,\n  TRPC_ERROR_CODES_BY_KEY.INTERNAL_SERVER_ERROR,\n];\n", "/** @internal */\nexport type UnsetMarker = 'unsetMarker' & {\n  __brand: 'unsetMarker';\n};\n\n/**\n * Ensures there are no duplicate keys when building a procedure.\n * @internal\n */\nexport function mergeWithoutOverrides<TType extends Record<string, unknown>>(\n  obj1: TType,\n  ...objs: Partial<TType>[]\n): TType {\n  const newObj: TType = Object.assign(Object.create(null), obj1);\n\n  for (const overrides of objs) {\n    for (const key in overrides) {\n      if (key in newObj && newObj[key] !== overrides[key]) {\n        throw new Error(`Duplicate key ${key}`);\n      }\n      newObj[key as keyof TType] = overrides[key] as TType[keyof TType];\n    }\n  }\n  return newObj;\n}\n\n/**\n * Check that value is object\n * @internal\n */\nexport function isObject(value: unknown): value is Record<string, unknown> {\n  return !!value && !Array.isArray(value) && typeof value === 'object';\n}\n\ntype AnyFn = ((...args: any[]) => unknown) & Record<keyof any, unknown>;\nexport function isFunction(fn: unknown): fn is AnyFn {\n  return typeof fn === 'function';\n}\n\n/**\n * Create an object without inheriting anything from `Object.prototype`\n * @internal\n */\nexport function omitPrototype<TObj extends Record<string, unknown>>(\n  obj: TObj,\n): TObj {\n  return Object.assign(Object.create(null), obj);\n}\n\nconst asyncIteratorsSupported =\n  typeof Symbol === 'function' && !!Symbol.asyncIterator;\n\nexport function isAsyncIterable<TValue>(\n  value: unknown,\n): value is AsyncIterable<TValue> {\n  return (\n    asyncIteratorsSupported && isObject(value) && Symbol.asyncIterator in value\n  );\n}\n\n/**\n * Run an IIFE\n */\nexport const run = <TValue>(fn: () => TValue): TValue => fn();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop(): void {}\n\nexport function identity<T>(it: T): T {\n  return it;\n}\n\n/**\n * Generic runtime assertion function. Throws, if the condition is not `true`.\n *\n * Can be used as a slightly less dangerous variant of type assertions. Code\n * mistakes would be revealed at runtime then (hopefully during testing).\n */\nexport function assert(\n  condition: boolean,\n  msg = 'no additional info',\n): asserts condition {\n  if (!condition) {\n    throw new Error(`AssertionError: ${msg}`);\n  }\n}\n\nexport function sleep(ms = 0): Promise<void> {\n  return new Promise<void>((res) => setTimeout(res, ms));\n}\n\n/**\n * Ponyfill for\n * [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).\n */\nexport function abortSignalsAnyPonyfill(signals: AbortSignal[]): AbortSignal {\n  if (typeof AbortSignal.any === 'function') {\n    return AbortSignal.any(signals);\n  }\n\n  const ac = new AbortController();\n\n  for (const signal of signals) {\n    if (signal.aborted) {\n      trigger();\n      break;\n    }\n    signal.addEventListener('abort', trigger, { once: true });\n  }\n\n  return ac.signal;\n\n  function trigger() {\n    ac.abort();\n    for (const signal of signals) {\n      signal.removeEventListener('abort', trigger);\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAUA,MAAa,0BAA0B;CAKrC,aAAa;CAIb,aAAa;CAGb,uBAAuB;CACvB,iBAAiB;CACjB,aAAa;CACb,qBAAqB;CACrB,iBAAiB;CAGjB,cAAc;CACd,kBAAkB;CAClB,WAAW;CACX,WAAW;CACX,sBAAsB;CACtB,SAAS;CACT,UAAU;CACV,qBAAqB;CACrB,mBAAmB;CACnB,wBAAwB;CACxB,uBAAuB;CACvB,mBAAmB;CACnB,uBAAuB;AACxB;AAGD,MAAaA,6BAET;EACD,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;AACX;;;;;AASD,MAAaC,oBAA8C;CACzD,wBAAwB;CACxB,wBAAwB;CACxB,wBAAwB;CACxB,wBAAwB;AACzB;;;;;;;;ACrED,SAAgB,sBACdC,MACA,GAAG,MACI;CACP,MAAMC,SAAgB,OAAO,OAAO,OAAO,OAAO,KAAK,EAAE,KAAK;AAE9D,MAAK,MAAM,aAAa,KACtB,MAAK,MAAM,OAAO,WAAW;AAC3B,MAAI,OAAO,UAAU,OAAO,SAAS,UAAU,KAC7C,OAAM,IAAI,OAAO,gBAAgB,IAAI;AAEvC,SAAO,OAAsB,UAAU;CACxC;AAEH,QAAO;AACR;;;;;AAMD,SAAgB,SAASC,OAAkD;AACzE,UAAS,UAAU,MAAM,QAAQ,MAAM,WAAW,UAAU;AAC7D;AAGD,SAAgB,WAAWC,IAA0B;AACnD,eAAc,OAAO;AACtB;;;;;AAMD,SAAgB,cACdC,KACM;AACN,QAAO,OAAO,OAAO,OAAO,OAAO,KAAK,EAAE,IAAI;AAC/C;AAED,MAAM,iCACG,WAAW,gBAAgB,OAAO;AAE3C,SAAgB,gBACdF,OACgC;AAChC,QACE,2BAA2B,SAAS,MAAM,IAAI,OAAO,iBAAiB;AAEzE;;;;AAKD,MAAa,MAAM,CAASG,OAA6B,IAAI;AAG7D,SAAgB,OAAa,CAAE;AAE/B,SAAgB,SAAYC,IAAU;AACpC,QAAO;AACR;;;;;;;AAQD,SAAgB,OACdC,WACA,MAAM,sBACa;AACnB,MAAK,UACH,OAAM,IAAI,OAAO,kBAAkB,IAAI;AAE1C;AAED,SAAgB,MAAM,KAAK,GAAkB;AAC3C,QAAO,IAAI,QAAc,CAAC,QAAQ,WAAW,KAAK,GAAG;AACtD;;;;;AAMD,SAAgB,wBAAwBC,SAAqC;AAC3E,YAAW,YAAY,QAAQ,WAC7B,QAAO,YAAY,IAAI,QAAQ;CAGjC,MAAM,KAAK,IAAI;AAEf,MAAK,MAAM,UAAU,SAAS;AAC5B,MAAI,OAAO,SAAS;AAClB,YAAS;AACT;EACD;AACD,SAAO,iBAAiB,SAAS,SAAS,EAAE,MAAM,KAAM,EAAC;CAC1D;AAED,QAAO,GAAG;CAEV,SAAS,UAAU;AACjB,KAAG,OAAO;AACV,OAAK,MAAM,UAAU,QACnB,QAAO,oBAAoB,SAAS,QAAQ;CAE/C;AACF"}