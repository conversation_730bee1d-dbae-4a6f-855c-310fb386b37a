{"version": 3, "file": "index.mjs", "names": ["opts: CreateServerSideHelpersOptions<TRouter>", "resolvedOpts: {\n    serialize: (obj: unknown) => any;\n    query: (queryOpts: { path: string; input: unknown }) => Promise<unknown>;\n  }", "opts: DehydrateOptions", "opts", "helperMap: Record<keyof AnyDecoratedProcedure, () => unknown>"], "sources": ["../../src/server/ssgProxy.ts"], "sourcesContent": ["import type {\n  DehydratedState,\n  DehydrateOptions,\n  QueryClient,\n} from '@tanstack/react-query';\nimport { dehydrate } from '@tanstack/react-query';\nimport type { TRPCClient } from '@trpc/client';\nimport { getUntypedClient, TRPCUntypedClient } from '@trpc/client';\nimport type { CoercedTransformerParameters } from '@trpc/client/unstable-internals';\nimport {\n  getTransformer,\n  type TransformerOptions,\n} from '@trpc/client/unstable-internals';\nimport type {\n  AnyQueryProcedure,\n  AnyRootTypes,\n  AnyRouter,\n  inferClientTypes,\n  inferRouterContext,\n  Maybe,\n  ProtectedIntersection,\n  RouterRecord,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  callProcedure,\n  createFlatProxy,\n  createRecursiveProxy,\n  run,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { getQueryKeyInternal } from '../internals/getQueryKey';\nimport type {\n  CreateTRPCReactQueryClientConfig,\n  DecorateQueryProcedure,\n  TRPCFetchInfiniteQueryOptions,\n  TRPCFetchQueryOptions,\n} from '../shared';\nimport { getQueryClient, getQueryType } from '../shared';\n\ntype CreateSSGHelpersInternal<TRouter extends AnyRouter> = {\n  router: TRouter;\n  ctx: inferRouterContext<TRouter>;\n} & TransformerOptions<inferClientTypes<TRouter>>;\n\ninterface CreateSSGHelpersExternal<TRouter extends AnyRouter> {\n  client: TRPCClient<TRouter> | TRPCUntypedClient<TRouter>;\n}\n\ntype CreateServerSideHelpersOptions<TRouter extends AnyRouter> =\n  CreateTRPCReactQueryClientConfig &\n    (CreateSSGHelpersExternal<TRouter> | CreateSSGHelpersInternal<TRouter>);\n\ntype SSGFns =\n  | 'queryOptions'\n  | 'infiniteQueryOptions'\n  | 'fetch'\n  | 'fetchInfinite'\n  | 'prefetch'\n  | 'prefetchInfinite';\n\n/**\n * @internal\n */\ntype DecoratedProcedureSSGRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyQueryProcedure\n      ? Pick<DecorateQueryProcedure<TRoot, $Value>, SSGFns>\n      : $Value extends RouterRecord\n        ? DecoratedProcedureSSGRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\ntype AnyDecoratedProcedure = Pick<DecorateQueryProcedure<any, any>, SSGFns>;\n\n/**\n * Create functions you can use for server-side rendering / static generation\n * @see https://trpc.io/docs/v11/client/nextjs/server-side-helpers\n */\nexport function createServerSideHelpers<TRouter extends AnyRouter>(\n  opts: CreateServerSideHelpersOptions<TRouter>,\n) {\n  const queryClient = getQueryClient(opts);\n  const transformer = getTransformer(\n    (opts as CoercedTransformerParameters).transformer,\n  );\n\n  const resolvedOpts: {\n    serialize: (obj: unknown) => any;\n    query: (queryOpts: { path: string; input: unknown }) => Promise<unknown>;\n  } = (() => {\n    if ('router' in opts) {\n      const { ctx, router } = opts;\n      return {\n        serialize: transformer.output.serialize,\n        query: (queryOpts) => {\n          return callProcedure({\n            router,\n            path: queryOpts.path,\n            getRawInput: async () => queryOpts.input,\n            ctx,\n            type: 'query',\n            signal: undefined,\n          });\n        },\n      };\n    }\n\n    const { client } = opts;\n    const untypedClient =\n      client instanceof TRPCUntypedClient ? client : getUntypedClient(client);\n\n    return {\n      query: (queryOpts) =>\n        untypedClient.query(queryOpts.path, queryOpts.input),\n      serialize: (obj) => transformer.output.serialize(obj),\n    };\n  })();\n\n  function _dehydrate(\n    opts: DehydrateOptions = {\n      shouldDehydrateQuery(query) {\n        if (query.state.status === 'pending') {\n          return false;\n        }\n        // makes sure to serialize errors\n        return true;\n      },\n    },\n  ): DehydratedState {\n    const before = run(() => {\n      const dehydrated = dehydrate(queryClient, opts);\n\n      return {\n        ...dehydrated,\n        queries: dehydrated.queries.map((query) => {\n          if (query.promise) {\n            const { promise: _, ...rest } = query;\n            return rest;\n          }\n          return query;\n        }),\n      };\n    });\n    const after = resolvedOpts.serialize(before);\n    return after;\n  }\n\n  type CreateSSGHelpers = ProtectedIntersection<\n    {\n      queryClient: QueryClient;\n      dehydrate: (opts?: DehydrateOptions) => DehydratedState;\n    },\n    DecoratedProcedureSSGRecord<\n      TRouter['_def']['_config']['$types'],\n      TRouter['_def']['record']\n    >\n  >;\n  const proxy = createRecursiveProxy<CreateSSGHelpers>((opts) => {\n    const args = opts.args;\n    const input = args[0];\n    const arrayPath = [...opts.path];\n    const utilName = arrayPath.pop() as keyof AnyDecoratedProcedure;\n\n    const queryFn = () =>\n      resolvedOpts.query({ path: arrayPath.join('.'), input });\n\n    const queryKey = getQueryKeyInternal(\n      arrayPath,\n      input,\n      getQueryType(utilName),\n    );\n\n    const helperMap: Record<keyof AnyDecoratedProcedure, () => unknown> = {\n      queryOptions: () => {\n        const args1 = args[1] as Maybe<any>;\n        return { ...args1, queryKey, queryFn };\n      },\n      infiniteQueryOptions: () => {\n        const args1 = args[1] as Maybe<any>;\n        return { ...args1, queryKey, queryFn };\n      },\n      fetch: () => {\n        const args1 = args[1] as Maybe<TRPCFetchQueryOptions<any, any>>;\n        return queryClient.fetchQuery({ ...args1, queryKey, queryFn });\n      },\n      fetchInfinite: () => {\n        const args1 = args[1] as Maybe<\n          TRPCFetchInfiniteQueryOptions<any, any, any>\n        >;\n        return queryClient.fetchInfiniteQuery({\n          ...args1,\n          queryKey,\n          queryFn,\n          initialPageParam: args1?.initialCursor ?? null,\n        });\n      },\n      prefetch: () => {\n        const args1 = args[1] as Maybe<TRPCFetchQueryOptions<any, any>>;\n        return queryClient.prefetchQuery({ ...args1, queryKey, queryFn });\n      },\n      prefetchInfinite: () => {\n        const args1 = args[1] as Maybe<\n          TRPCFetchInfiniteQueryOptions<any, any, any>\n        >;\n        return queryClient.prefetchInfiniteQuery({\n          ...args1,\n          queryKey,\n          queryFn,\n          initialPageParam: args1?.initialCursor ?? null,\n        });\n      },\n    };\n\n    return helperMap[utilName]();\n  });\n  return createFlatProxy<CreateSSGHelpers>((key) => {\n    if (key === 'queryClient') return queryClient;\n    if (key === 'dehydrate') return _dehydrate;\n    return proxy[key];\n  });\n}\n"], "mappings": ";;;;;;;;;;mBA2IoB;;;;;AA1DpB,SAAgB,wBACdA,MACA;CACA,MAAM,cAAc,eAAe,KAAK;CACxC,MAAM,cAAc,eACjB,KAAsC,YACxC;CAED,MAAMC,eAGF,CAAC,MAAM;AACT,MAAI,YAAY,MAAM;GACpB,MAAM,EAAE,KAAK,QAAQ,GAAG;AACxB,UAAO;IACL,WAAW,YAAY,OAAO;IAC9B,OAAO,CAAC,cAAc;AACpB,YAAO,cAAc;MACnB;MACA,MAAM,UAAU;MAChB,aAAa,YAAY,UAAU;MACnC;MACA,MAAM;MACN;KACD,EAAC;IACH;GACF;EACF;EAED,MAAM,EAAE,QAAQ,GAAG;EACnB,MAAM,gBACJ,kBAAkB,oBAAoB,SAAS,iBAAiB,OAAO;AAEzE,SAAO;GACL,OAAO,CAAC,cACN,cAAc,MAAM,UAAU,MAAM,UAAU,MAAM;GACtD,WAAW,CAAC,QAAQ,YAAY,OAAO,UAAU,IAAI;EACtD;CACF,IAAG;CAEJ,SAAS,WACPC,SAAyB,EACvB,qBAAqB,OAAO;AAC1B,MAAI,MAAM,MAAM,WAAW,UACzB,QAAO;AAGT,SAAO;CACR,EACF,GACgB;EACjB,MAAM,SAAS,IAAI,MAAM;GACvB,MAAM,aAAa,UAAU,aAAaC,OAAK;AAE/C,kFACK,mBACH,SAAS,WAAW,QAAQ,IAAI,CAAC,UAAU;AACzC,QAAI,MAAM,SAAS;KACjB,MAAM,EAAE,SAAS,GAAY,UAAN,mDAAS;AAChC,YAAO;IACR;AACD,WAAO;GACR,EAAC;EAEL,EAAC;EACF,MAAM,QAAQ,aAAa,UAAU,OAAO;AAC5C,SAAO;CACR;CAYD,MAAM,QAAQ,qBAAuC,CAACA,WAAS;EAC7D,MAAM,OAAOA,OAAK;EAClB,MAAM,QAAQ,KAAK;EACnB,MAAM,YAAY,CAAC,GAAGA,OAAK,IAAK;EAChC,MAAM,WAAW,UAAU,KAAK;EAEhC,MAAM,UAAU,MACd,aAAa,MAAM;GAAE,MAAM,UAAU,KAAK,IAAI;GAAE;EAAO,EAAC;EAE1D,MAAM,WAAW,oBACf,WACA,OACA,aAAa,SAAS,CACvB;EAED,MAAMC,YAAgE;GACpE,cAAc,MAAM;IAClB,MAAM,QAAQ,KAAK;AACnB,mFAAY;KAAO;KAAU;;GAC9B;GACD,sBAAsB,MAAM;IAC1B,MAAM,QAAQ,KAAK;AACnB,mFAAY;KAAO;KAAU;;GAC9B;GACD,OAAO,MAAM;IACX,MAAM,QAAQ,KAAK;AACnB,WAAO,YAAY,mFAAgB;KAAO;KAAU;OAAU;GAC/D;GACD,eAAe,MAAM;;IACnB,MAAM,QAAQ,KAAK;AAGnB,WAAO,YAAY,2FACd;KACH;KACA;KACA,wFAAkB,MAAO,oFAAiB;OAC1C;GACH;GACD,UAAU,MAAM;IACd,MAAM,QAAQ,KAAK;AACnB,WAAO,YAAY,sFAAmB;KAAO;KAAU;OAAU;GAClE;GACD,kBAAkB,MAAM;;IACtB,MAAM,QAAQ,KAAK;AAGnB,WAAO,YAAY,8FACd;KACH;KACA;KACA,yFAAkB,MAAO,sFAAiB;OAC1C;GACH;EACF;AAED,SAAO,UAAU,WAAW;CAC7B,EAAC;AACF,QAAO,gBAAkC,CAAC,QAAQ;AAChD,MAAI,QAAQ,cAAe,QAAO;AAClC,MAAI,QAAQ,YAAa,QAAO;AAChC,SAAO,MAAM;CACd,EAAC;AACH"}