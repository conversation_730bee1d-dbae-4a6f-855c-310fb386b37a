import { CreateClient, CreateQueryUtils, CreateReactUtils, CreateTRPCReactOptions, CreateTRPCReactQueryClientConfig, DecorateProcedure, DecorateQueryProcedure, DecorateRouterRecord, DecoratedProcedureUtilsRecord, DecoratedTRPCContextProps, DefinedTRPCInfiniteQueryOptionsIn, DefinedTRPCInfiniteQueryOptionsOut, DefinedTRPCQueryOptionsIn, DefinedTRPCQueryOptionsOut, DefinedUseTRPCQueryOptions, DefinedUseTRPCQueryResult, ExtractCursorType, InferMutationLikeData, InferMutationLikeInput, InferQueryLikeData, InferQueryLikeInput, MutationLike, OutputWithCursor, QueryLike, RouterLike, RouterLikeInner, SSRState, TRPCContext, TRPCContextProps, TRPCContextPropsBase, TRPCContextState, TRPCFetchInfiniteQueryOptions, TRPCFetchQueryOptions, TRPCHookResult, TRPCProvider, TRPCProviderProps, TRPCQueryBaseOptions, TRPCQueryOptions, TRPCQueryOptionsResult, TRPCQueryUtils, TRPCReactRequestOptions, TRPCSubscriptionBaseResult, TRPCSubscriptionConnectingResult, TRPCSubscriptionErrorResult, TRPCSubscriptionIdleResult, TRPCSubscriptionPendingResult, TRPCSubscriptionResult, TRPCUseQueries, TRPCUseQueryBaseOptions, TRPCUseSuspenseQueries, UndefinedTRPCInfiniteQueryOptionsIn, UndefinedTRPCInfiniteQueryOptionsOut, UndefinedTRPCQueryOptionsIn, UndefinedTRPCQueryOptionsOut, UnusedSkipTokenTRPCInfiniteQueryOptionsIn, UnusedSkipTokenTRPCInfiniteQueryOptionsOut, UnusedSkipTokenTRPCQueryOptionsIn, UnusedSkipTokenTRPCQueryOptionsOut, UseMutationOverride, UseQueriesProcedureRecord, UseSuspenseQueriesProcedureRecord, UseTRPCInfiniteQueryOptions, UseTRPCInfiniteQueryResult, UseTRPCInfiniteQuerySuccessResult, UseTRPCMutationOptions, UseTRPCMutationResult, UseTRPCPrefetchInfiniteQueryOptions, UseTRPCPrefetchQueryOptions, UseTRPCQueryOptions, UseTRPCQueryResult, UseTRPCQuerySuccessResult, UseTRPCSubscriptionOptions, UseTRPCSuspenseInfiniteQueryOptions, UseTRPCSuspenseInfiniteQueryResult, UseTRPCSuspenseQueryOptions, UseTRPCSuspenseQueryResult, UtilsLike, contextProps, createQueryUtilsProxy, createReactDecoration, createReactQueryUtils, createRootHooks, createUseQueries, getClientArgs, getQueryClient, getQueryType } from "../getQueryKey.d-CruH3ncI.mjs";
export { CreateClient, CreateQueryUtils, CreateReactUtils, CreateTRPCReactOptions, CreateTRPCReactQueryClientConfig, DecorateProcedure, DecorateQueryProcedure, DecorateRouterRecord, DecoratedProcedureUtilsRecord, DecoratedTRPCContextProps, DefinedTRPCInfiniteQueryOptionsIn, DefinedTRPCInfiniteQueryOptionsOut, DefinedTRPCQueryOptionsIn, DefinedTRPCQueryOptionsOut, DefinedUseTRPCQueryOptions, DefinedUseTRPCQueryResult, ExtractCursorType, InferMutationLikeData, InferMutationLikeInput, InferQueryLikeData, InferQueryLikeInput, MutationLike, OutputWithCursor, QueryLike, RouterLike, RouterLikeInner, SSRState, TRPCContext, TRPCContextProps, TRPCContextPropsBase, TRPCContextState, TRPCFetchInfiniteQueryOptions, TRPCFetchQueryOptions, TRPCHookResult, TRPCProvider, TRPCProviderProps, TRPCQueryBaseOptions, TRPCQueryOptions, TRPCQueryOptionsResult, TRPCQueryUtils, TRPCReactRequestOptions, TRPCSubscriptionBaseResult, TRPCSubscriptionConnectingResult, TRPCSubscriptionErrorResult, TRPCSubscriptionIdleResult, TRPCSubscriptionPendingResult, TRPCSubscriptionResult, TRPCUseQueries, TRPCUseQueryBaseOptions, TRPCUseSuspenseQueries, UndefinedTRPCInfiniteQueryOptionsIn, UndefinedTRPCInfiniteQueryOptionsOut, UndefinedTRPCQueryOptionsIn, UndefinedTRPCQueryOptionsOut, UnusedSkipTokenTRPCInfiniteQueryOptionsIn, UnusedSkipTokenTRPCInfiniteQueryOptionsOut, UnusedSkipTokenTRPCQueryOptionsIn, UnusedSkipTokenTRPCQueryOptionsOut, UseMutationOverride, UseQueriesProcedureRecord, UseSuspenseQueriesProcedureRecord, UseTRPCInfiniteQueryOptions, UseTRPCInfiniteQueryResult, UseTRPCInfiniteQuerySuccessResult, UseTRPCMutationOptions, UseTRPCMutationResult, UseTRPCPrefetchInfiniteQueryOptions, UseTRPCPrefetchQueryOptions, UseTRPCQueryOptions, UseTRPCQueryResult, UseTRPCQuerySuccessResult, UseTRPCSubscriptionOptions, UseTRPCSuspenseInfiniteQueryOptions, UseTRPCSuspenseInfiniteQueryResult, UseTRPCSuspenseQueryOptions, UseTRPCSuspenseQueryResult, UtilsLike, contextProps, createQueryUtilsProxy, createReactDecoration, createReactQueryUtils, createRootHooks, createUseQueries, getClientArgs, getQueryClient, getQueryType };