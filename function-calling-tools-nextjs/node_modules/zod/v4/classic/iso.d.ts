import * as core from "../core/index.js";
import * as schemas from "./schemas.js";
export interface ZodISODateTime extends schemas.ZodStringFormat {
    _zod: core.$ZodISODateTimeInternals;
}
export declare const ZodISODateTime: core.$constructor<ZodISODateTime>;
export declare function datetime(params?: string | core.$ZodISODateTimeParams): ZodISODateTime;
export interface ZodISODate extends schemas.ZodStringFormat {
    _zod: core.$ZodISODateInternals;
}
export declare const ZodISODate: core.$constructor<ZodISODate>;
export declare function date(params?: string | core.$ZodISODateParams): ZodISODate;
export interface ZodISOTime extends schemas.ZodStringFormat {
    _zod: core.$ZodISOTimeInternals;
}
export declare const ZodISOTime: core.$constructor<ZodISOTime>;
export declare function time(params?: string | core.$ZodISOTimeParams): ZodISOTime;
export interface ZodISODuration extends schemas.ZodStringFormat {
    _zod: core.$ZodISODurationInternals;
}
export declare const ZodISODuration: core.$constructor<ZodISODuration>;
export declare function duration(params?: string | core.$ZodISODurationParams): ZodISODuration;
