# Next.js + tRPC 迁移方案

## 📋 概述

本文档详细说明如何将现有的 React + Express 项目迁移到 Next.js + tRPC 技术栈。

## 🎯 迁移目标

- **统一技术栈**: 从前后端分离架构迁移到 Next.js 全栈应用
- **类型安全**: 通过 tRPC 实现端到端类型安全
- **开发体验**: 提升开发效率和代码维护性
- **性能优化**: 利用 Next.js 的 SSR/SSG 能力

## 🏗️ 新项目结构

```
function-calling-tools-nextjs/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/trpc/[trpc]/   # tRPC API 路由
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 首页
│   ├── components/            # React 组件 (迁移自 frontend/src/components)
│   ├── hooks/                 # 自定义 hooks (迁移自 frontend/src/hooks)
│   ├── server/                # 服务端代码
│   │   ├── api/               # tRPC 路由定义
│   │   │   ├── routers/       # 各功能模块路由
│   │   │   ├── root.ts        # 根路由
│   │   │   └── trpc.ts        # tRPC 配置
│   │   └── tools/             # 工具函数 (迁移自 backend/src/tools)
│   ├── types/                 # 类型定义 (迁移自 shared/types)
│   └── utils/                 # 工具函数
├── public/                    # 静态资源
├── shared/                    # 保留共享类型 (向后兼容)
├── docs/                      # 文档 (保留)
├── next.config.ts             # Next.js 配置
├── package.json               # 依赖管理
└── tailwind.config.ts         # Tailwind 配置
```

## 🚀 迁移步骤

### 第一阶段：项目初始化

#### 1. 创建新的 Next.js 项目

```bash
# 创建新目录
mkdir function-calling-tools-nextjs
cd function-calling-tools-nextjs

# 初始化 Next.js 项目
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir
```

#### 2. 安装 tRPC 相关依赖

```bash
npm install @trpc/server @trpc/client @trpc/react-query @trpc/next
npm install @tanstack/react-query zod
npm install openai
npm install -D @types/node
```

#### 3. 配置 Next.js

```typescript
// next.config.ts
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  experimental: {
    reactCompiler: true,
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  env: {
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
  },
};

export default nextConfig;
```

### 第二阶段：后端迁移

#### 1. 设置 tRPC 服务器

```typescript
// src/server/api/trpc.ts
import { initTRPC } from '@trpc/server';
import { ZodError } from 'zod';

const t = initTRPC.create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

export const createTRPCRouter = t.router;
export const publicProcedure = t.procedure;
```

#### 2. 迁移工具函数

```typescript
// src/server/api/routers/tools.ts
import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '../trpc';
import { weatherTool, translationTool, summaryTool } from '../../tools';

export const toolsRouter = createTRPCRouter({
  // 天气查询
  getWeather: publicProcedure
    .input(z.object({
      city: z.string(),
      country: z.string().optional(),
    }))
    .query(async ({ input }) => {
      return await weatherTool.execute(input);
    }),

  // 翻译
  translate: publicProcedure
    .input(z.object({
      text: z.string(),
      targetLanguage: z.string(),
      sourceLanguage: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      return await translationTool.execute(input);
    }),

  // 文本摘要
  summarize: publicProcedure
    .input(z.object({
      text: z.string(),
      maxLength: z.number().optional(),
    }))
    .mutation(async ({ input }) => {
      return await summaryTool.execute(input);
    }),
});
```

#### 3. 迁移聊天功能

```typescript
// src/server/api/routers/chat.ts
import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '../trpc';
import { OpenAI } from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const chatRouter = createTRPCRouter({
  sendMessage: publicProcedure
    .input(z.object({
      message: z.string(),
      conversationId: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      // OpenAI Function Calling 逻辑
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: input.message }],
        tools: [
          // 工具定义
        ],
      });

      return {
        message: response.choices[0]?.message,
        conversationId: input.conversationId || generateId(),
        toolCalls: response.choices[0]?.message?.tool_calls || [],
      };
    }),
});
```

#### 4. 创建根路由

```typescript
// src/server/api/root.ts
import { createTRPCRouter } from './trpc';
import { chatRouter } from './routers/chat';
import { toolsRouter } from './routers/tools';

export const appRouter = createTRPCRouter({
  chat: chatRouter,
  tools: toolsRouter,
});

export type AppRouter = typeof appRouter;
```

#### 5. 设置 API 路由

```typescript
// src/app/api/trpc/[trpc]/route.ts
import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { appRouter } from '../../../../server/api/root';

const handler = (req: Request) =>
  fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => ({}),
  });

export { handler as GET, handler as POST };
```

### 第三阶段：前端迁移

#### 1. 设置 tRPC 客户端

```typescript
// src/utils/trpc.ts
import { createTRPCNext } from '@trpc/next';
import { httpBatchLink } from '@trpc/client';
import type { AppRouter } from '../server/api/root';

export const trpc = createTRPCNext<AppRouter>({
  config() {
    return {
      links: [
        httpBatchLink({
          url: '/api/trpc',
        }),
      ],
    };
  },
  ssr: false,
});
```

#### 2. 配置根布局

```typescript
// src/app/layout.tsx
import { trpc } from '../utils/trpc';
import './globals.css';

function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="zh">
      <body>
        {children}
      </body>
    </html>
  );
}

export default trpc.withTRPC(RootLayout);
```

#### 3. 迁移 React 组件

```typescript
// src/components/ChatInterface.tsx
import { trpc } from '../utils/trpc';

export function ChatInterface() {
  const sendMessage = trpc.chat.sendMessage.useMutation();
  
  const handleSendMessage = async (message: string) => {
    try {
      const result = await sendMessage.mutateAsync({ message });
      // 处理响应
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  return (
    <div className="chat-interface">
      {/* 聊天界面 JSX */}
    </div>
  );
}
```

### 第四阶段：数据迁移

#### 1. 类型定义迁移

```bash
# 复制共享类型
cp -r shared/types src/types
```

#### 2. 组件迁移

```bash
# 复制前端组件
cp -r frontend/src/components src/components
cp -r frontend/src/hooks src/hooks
```

#### 3. 样式迁移

```bash
# 复制 Tailwind 配置
cp frontend/tailwind.config.js tailwind.config.ts
```

### 第五阶段：测试和优化

#### 1. 功能测试

```bash
# 启动开发服务器
npm run dev

# 测试各项功能
# - 聊天功能
# - 工具调用
# - 类型安全
```

#### 2. 性能优化

- 启用 React Compiler
- 配置代码分割
- 优化图片和字体加载

## 📝 迁移检查清单

### 后端迁移
- [ ] tRPC 服务器配置
- [ ] 工具函数迁移
- [ ] OpenAI 集成
- [ ] API 路由设置
- [ ] 环境变量配置

### 前端迁移
- [ ] tRPC 客户端配置
- [ ] React 组件迁移
- [ ] 状态管理调整
- [ ] 样式系统迁移
- [ ] 路由配置

### 测试验证
- [ ] 聊天功能正常
- [ ] 工具调用正常
- [ ] 类型安全验证
- [ ] 性能测试
- [ ] 错误处理测试

## 🔧 开发脚本

```json
{
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  }
}
```

## 🚀 部署配置

### Vercel 部署

```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

### 环境变量

```bash
# .env.local
OPENAI_API_KEY=your_openai_api_key
NEXTAUTH_SECRET=your_secret_key
```

## 📚 参考资源

- [Next.js 15 文档](https://nextjs.org/docs)
- [tRPC 文档](https://trpc.io/docs)
- [从 Vite 迁移到 Next.js](https://nextjs.org/docs/app/building-your-application/upgrading/from-vite)
- [OpenAI Function Calling](https://platform.openai.com/docs/guides/function-calling)

## 🤝 支持

如有问题，请参考：
1. 项目文档 `docs/` 目录
2. GitHub Issues
3. 开发团队联系方式

---

**预计迁移时间**: 2-3 个工作日
**风险等级**: 中等
**建议**: 先在分支上完成迁移，充分测试后再合并到主分支