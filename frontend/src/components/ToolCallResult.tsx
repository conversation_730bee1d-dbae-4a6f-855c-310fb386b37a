import React, { useState } from 'react';
import { ToolCall } from '@shared/types';
import { 
  Cloud, 
  Languages, 
  FileText, 
  Database, 
  Calculator,
  ChevronDown,
  ChevronRight,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';

interface ToolCallResultProps {
  toolCall: ToolCall;
}

const toolIcons = {
  weather_query: Cloud,
  translate_text: Languages,
  summarize_text: FileText,
  database_query: Database,
  calculate: Calculator,
};

const toolNames = {
  weather_query: '天气查询',
  translate_text: '文本翻译',
  summarize_text: '文本摘要',
  database_query: '数据库查询',
  calculate: '数学计算',
};

export function ToolCallResult({ toolCall }: ToolCallResultProps) {
  const [expanded, setExpanded] = useState(false);
  
  const IconComponent = toolIcons[toolCall.function.name as keyof typeof toolIcons];
  const toolName = toolNames[toolCall.function.name as keyof typeof toolNames] || toolCall.function.name;

  let parsedArgs: any = {};
  try {
    parsedArgs = JSON.parse(toolCall.function.arguments);
  } catch (error) {
    console.error('Failed to parse tool arguments:', error);
  }

  const renderArguments = () => {
    return Object.entries(parsedArgs).map(([key, value]) => (
      <div key={key} className="flex gap-2">
        <span className="font-medium text-muted-foreground">{key}:</span>
        <span className="text-foreground">
          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
        </span>
      </div>
    ));
  };

  return (
    <Card className="tool-result">
      <CardHeader className="pb-2">
        <CardTitle className="tool-result-header">
          <IconComponent className="h-4 w-4" />
          <span>{toolName}</span>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 ml-auto"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        </CardTitle>
      </CardHeader>
      
      {expanded && (
        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* 工具参数 */}
            <div>
              <h4 className="text-sm font-medium mb-2">调用参数:</h4>
              <div className="bg-muted/50 rounded-md p-3 space-y-1 text-sm">
                {renderArguments()}
              </div>
            </div>

            {/* 工具ID */}
            <div className="text-xs text-muted-foreground">
              调用ID: {toolCall.id}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
